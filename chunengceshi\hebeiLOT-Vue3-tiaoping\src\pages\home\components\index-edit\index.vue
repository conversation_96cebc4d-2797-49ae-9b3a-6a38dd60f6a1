<template>
	<div class="index-num">
		<a-row class="index-row-value">
			<a-col class="index-col">
				<span>运行模式：</span>
				<a-tabs v-model:activeKey="activeKey" type="card" class="tabs">
					<a-tab-pane key="1" tab="并网模式"></a-tab-pane>
					<a-tab-pane key="2" tab="离网模式"></a-tab-pane>
					<a-tab-pane key="3" tab="混合模式"></a-tab-pane>
				</a-tabs>
				<!-- <span> <a-switch v-model:checked="checked1" /></span> -->
			</a-col>
			<a-divider type="vertical" class="index-divider" />
		</a-row>
		<a-row class="index-row-value">
			<a-divider type="vertical" class="index-divider" />
			<a-col class="index-col">
				<span style="margin-right: 10px">充电电压:</span>
				<span><a-input-number id="inputNumber" v-model:value="checknum2" size="large" :min="1" :max="100000" /></span>
				<span style="margin-left: 10px">V</span>
			</a-col>
			<a-col class="index-col">
				<span style="margin-right: 10px">功率限制:</span>
				<span><a-input-number id="inputNumber" v-model:value="checknum3" size="large" :min="1" :max="100000" /></span>
				<span style="margin-left: 10px">kw</span>
			</a-col>
		</a-row>
		<div class="butbox">
			<button class="but">发送指令</button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue';
// import gsap from 'gsap';

const checked1 = ref(true);
const checknum2 = ref(2.5);
const checknum3 = ref(100);

const activeKey = ref('1');
</script>

<style lang="scss" scoped>
@font-face {
	font-family: electronicFont;
	src: url('@/assets/font/DS-DIGIT.TTF');
}
.butbox {
	width: 100%;
	height: 60px;
	margin-top: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	.but {
		width: 180px;
		height: 38px;
		font-size: 20px;
		background-color: transparent;
		color: #058dc1;
		border: #058dc1 1.5px solid;
		cursor: pointer;
	}
	padding-bottom: 20px;
}
.index-num {
	background-color: #0f2159;
	border-radius: 4px;

	.index-row-value {
		position: relative;
		padding: 5px 0;
		border: 1px solid rgb(255 255 255 / 20%);

		.index-col {
			flex: 1;
			font-family: electronicFont;
			font-size: 1rem;
			color: #ffffff;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.index-divider {
			height: unset;
			margin-block: 20px;
			background: rgb(255 255 255 / 20%);
		}

		@mixin border-conrner {
			position: absolute;
			width: 30px;
			height: 15px;
			content: '';
		}

		&::before {
			@include border-conrner;

			top: 0;
			left: 0;
			border-top: 2px solid #02a6b5;
			border-left: 2px solid #02a6b5;
		}

		&::after {
			@include border-conrner;

			right: 0;
			bottom: 0;
			border-right: 2px solid #02a6b5;
			border-bottom: 2px solid #02a6b5;
		}
	}

	.index-row-label {
		color: rgb(255 255 255 / 70%);

		.index-col {
			flex: 1;
			margin: 8px 0;
			font-size: 16px;
			text-align: center;
		}
	}
}
</style>
<style>
.ant-input-number-lg input {
	font-size: 32px;
}
.ant-tabs > .ant-tabs-nav .ant-tabs-nav-list,
.ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-list {
	display: flex;
	align-items: center !important;
	justify-content: center;
	padding-top: 10px;
}
.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active,
.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active {
	background-color: navy;
	border: navy 1px solid;
}
.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
.ant-tabs-card.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
.ant-tabs-card.ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
	border: navy 1px solid;
	/* background-color: #0f2159; */
}
.ant-tabs-tab-btn {
	/* color: #ffeb7b; */
	font-size: 18px;
	font-weight: 600;
	color: #ffffff;
}

.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab:not(.ant-tabs-tab-active),
.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab:not(.ant-tabs-tab-active) {
	background-color: rgba(15, 33, 89, 0.6);
	border: 1px solid #1e3276;
}

.ant-tabs-tab:not(.ant-tabs-tab-active) .ant-tabs-tab-btn {
	color: #ffffff;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
	color: #ffeb7b;
}

@media (max-width: 768px) {
	.ant-tabs-tab-btn {
		font-size: 16px;
	}
	
	.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab:not(.ant-tabs-tab-active),
	.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab:not(.ant-tabs-tab-active) {
		background-color: #0f2159;
	}
}
</style>
