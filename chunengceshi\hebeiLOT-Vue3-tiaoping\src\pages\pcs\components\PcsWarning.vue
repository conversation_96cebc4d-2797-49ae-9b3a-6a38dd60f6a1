<template>
  <div class="pcs-warning">
    <div class="section-header">PCS告警</div>
    
    <div class="warning-content">
      <div class="warning-filters">
        <div class="filter-item">
          <div class="filter-label">告警级别：</div>
          <div class="filter-options">
            <div class="option" :class="{ active: selectedLevel === 'all' }" @click="selectedLevel = 'all'">全部</div>
            <div class="option" :class="{ active: selectedLevel === 'high' }" @click="selectedLevel = 'high'">高</div>
            <div class="option" :class="{ active: selectedLevel === 'medium' }" @click="selectedLevel = 'medium'">中</div>
            <div class="option" :class="{ active: selectedLevel === 'low' }" @click="selectedLevel = 'low'">低</div>
          </div>
        </div>
        
        <div class="filter-item">
          <div class="filter-label">状态：</div>
          <div class="filter-options">
            <div class="option" :class="{ active: selectedStatus === 'all' }" @click="selectedStatus = 'all'">全部</div>
            <div class="option" :class="{ active: selectedStatus === 'active' }" @click="selectedStatus = 'active'">活动</div>
            <div class="option" :class="{ active: selectedStatus === 'resolved' }" @click="selectedStatus = 'resolved'">已解决</div>
          </div>
        </div>
        
        <div class="filter-item search">
          <input type="text" v-model="searchTerm" placeholder="搜索告警内容..." class="search-input" />
          <button class="search-btn" @click="handleSearch">
            <i class="search-icon">🔍</i>
          </button>
        </div>
      </div>
      
      <div class="warning-statistics">
        <div class="stat-item">
          <div class="stat-value">{{ highCount }}</div>
          <div class="stat-label">高级告警</div>
          <div class="stat-bar">
            <div class="bar-fill high" :style="{ width: `${highPercentage}%` }"></div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ mediumCount }}</div>
          <div class="stat-label">中级告警</div>
          <div class="stat-bar">
            <div class="bar-fill medium" :style="{ width: `${mediumPercentage}%` }"></div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ lowCount }}</div>
          <div class="stat-label">低级告警</div>
          <div class="stat-bar">
            <div class="bar-fill low" :style="{ width: `${lowPercentage}%` }"></div>
          </div>
        </div>
      </div>
      
      <div class="warning-list">
        <div v-for="(warning, index) in filteredWarnings" :key="index" class="warning-card" :class="warning.level">
          <div class="warning-icon" :class="warning.level">
            <i class="icon">⚠</i>
          </div>
          <div class="warning-details">
            <div class="warning-title">{{ warning.title }}</div>
            <div class="warning-info">
              <div class="warning-time">{{ warning.time }}</div>
              <div class="warning-level">{{ getLevelText(warning.level) }}</div>
              <div class="warning-device">设备ID: {{ warning.deviceId }}</div>
            </div>
            <div class="warning-description">{{ warning.description }}</div>
          </div>
          <div class="warning-actions">
            <button class="action-btn acknowledge-btn" v-if="warning.status === 'active'" @click="acknowledgeWarning(index)">确认</button>
            <button class="action-btn details-btn" @click="viewWarningDetails(warning)">详情</button>
          </div>
        </div>
        
        <div v-if="filteredWarnings.length === 0" class="no-warnings">
          没有符合条件的告警
        </div>
      </div>
    </div>
    
    <div class="warning-footer">
      <button class="export-btn" @click="exportData">导出告警数据</button>
      <div class="warning-pagination">
        <button class="page-btn prev" :disabled="currentPage === 1" @click="prevPage">上一页</button>
        <div class="page-number">{{ currentPage }} / {{ totalPages }}</div>
        <button class="page-btn next" :disabled="currentPage === totalPages" @click="nextPage">下一页</button>
      </div>
      <button class="refresh-btn" @click="refreshData" :class="{ 'refreshing': isRefreshing }">
        <span class="refresh-icon">↻</span> 刷新
      </button>
    </div>

    <!-- 告警详情弹窗 -->
    <div class="warning-detail-modal" v-if="showDetailModal" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header" :class="selectedWarning.level">
          <div class="modal-title">告警详情</div>
          <button class="close-btn" @click="closeDetailModal">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-item">
            <div class="detail-label">告警标题：</div>
            <div class="detail-value">{{ selectedWarning.title }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">告警级别：</div>
            <div class="detail-value">
              <span class="level-badge" :class="selectedWarning.level">
                {{ getLevelText(selectedWarning.level) }}
              </span>
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">发生时间：</div>
            <div class="detail-value">{{ selectedWarning.time }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">设备ID：</div>
            <div class="detail-value">{{ selectedWarning.deviceId }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">当前状态：</div>
            <div class="detail-value">{{ selectedWarning.status === 'active' ? '活动' : '已解决' }}</div>
          </div>
          <div class="detail-item full">
            <div class="detail-label">告警描述：</div>
            <div class="detail-value">{{ selectedWarning.description }}</div>
          </div>
          <div class="detail-item full" v-if="selectedWarning.recommendation">
            <div class="detail-label">处理建议：</div>
            <div class="detail-value recommendation">{{ selectedWarning.recommendation }}</div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn acknowledge-btn" v-if="selectedWarning.status === 'active'" @click="acknowledgeSelectedWarning">确认告警</button>
          <button class="action-btn close-detail-btn" @click="closeDetailModal">关闭</button>
        </div>
      </div>
    </div>

    <!-- 通知提示 -->
    <div class="notification" v-if="notification.show">
      <div class="notification-content" :class="notification.type">
        <div class="notification-icon">{{ notification.type === 'success' ? '✓' : 'ℹ' }}</div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// 过滤条件
const selectedLevel = ref('all');
const selectedStatus = ref('all');
const searchTerm = ref('');

// 分页
const currentPage = ref(1);
const totalPages = ref(3);
const itemsPerPage = 5;

// 刷新状态
const isRefreshing = ref(false);

// 详情弹窗
const showDetailModal = ref(false);
const selectedWarning = ref({
  title: '',
  description: '',
  time: '',
  level: '',
  status: '',
  deviceId: '',
  recommendation: ''
});

// 通知提示
const notification = ref({
  show: false,
  message: '',
  type: 'info' // success, info, warning, error
});

// 告警数据
const warnings = ref([
  {
    title: '电池过温告警',
    description: 'PCS连接的电池组温度超过设定阈值，当前温度48°C，阈值45°C',
    time: '2023-11-15 09:45:22',
    level: 'high',
    status: 'active',
    deviceId: 'PCS-001',
    recommendation: '建议降低充放电功率，增加电池组散热，检查环境温度是否异常升高。如温度持续升高，应停止使用并联系维护人员检查电池组。'
  },
  {
    title: '输出功率波动',
    description: '检测到PCS输出功率波动超过15%，可能影响系统稳定性',
    time: '2023-11-15 08:12:35',
    level: 'medium',
    status: 'active',
    deviceId: 'PCS-001',
    recommendation: '检查负载变化情况，确认系统参数设置是否合理。可尝试调整功率控制参数，提高系统响应稳定性。'
  },
  {
    title: '风扇运行异常',
    description: '冷却风扇2号转速低于正常值，当前转速800RPM，正常值应大于1200RPM',
    time: '2023-11-14 23:56:10',
    level: 'low',
    status: 'active',
    deviceId: 'PCS-001',
    recommendation: '检查风扇是否堵转或积尘，清洁散热系统。如问题持续存在，考虑更换风扇。'
  },
  {
    title: '电网电压波动',
    description: '电网电压波动幅度较大，可能导致PCS频繁切换工作模式',
    time: '2023-11-14 16:30:45',
    level: 'medium',
    status: 'resolved',
    deviceId: 'PCS-001',
    recommendation: '监测电网电压变化趋势，如持续不稳定，建议安装稳压装置或调整PCS电压保护参数范围。'
  },
  {
    title: '通信延迟增加',
    description: 'PCS与监控系统通信延迟增加到250ms，正常应小于100ms',
    time: '2023-11-14 14:18:20',
    level: 'low',
    status: 'resolved',
    deviceId: 'PCS-001',
    recommendation: '检查通信线路质量和网络负载情况，排除干扰源，必要时优化网络拓扑或更换通信设备。'
  },
  {
    title: '电池SOC过低',
    description: '电池组SOC低于20%，建议及时充电以避免深度放电',
    time: '2023-11-13 19:42:30',
    level: 'high',
    status: 'resolved',
    deviceId: 'PCS-001',
    recommendation: '立即为电池充电，避免长时间低SOC状态，以防止电池过度放电导致容量衰减或损坏。'
  },
  {
    title: '效率下降警告',
    description: 'PCS转换效率下降至92%，低于正常值95%，建议检查系统',
    time: '2023-11-13 11:05:15',
    level: 'medium',
    status: 'resolved',
    deviceId: 'PCS-001',
    recommendation: '检查功率器件温度、散热系统、滤波电容等关键部件状态，进行系统清洁和维护，必要时进行校准。'
  }
]);

// 分页后的告警列表
const paginatedWarnings = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  return filteredWarnings.value.slice(startIndex, startIndex + itemsPerPage);
});

// 过滤后的告警列表
const filteredWarnings = computed(() => {
  return warnings.value.filter(warning => {
    // 级别过滤
    if (selectedLevel.value !== 'all' && warning.level !== selectedLevel.value) {
      return false;
    }
    
    // 状态过滤
    if (selectedStatus.value !== 'all') {
      const requiredStatus = selectedStatus.value === 'active' ? 'active' : 'resolved';
      if (warning.status !== requiredStatus) {
        return false;
      }
    }
    
    // 搜索条件
    if (searchTerm.value) {
      const searchLower = searchTerm.value.toLowerCase();
      return (
        warning.title.toLowerCase().includes(searchLower) ||
        warning.description.toLowerCase().includes(searchLower)
      );
    }
    
    return true;
  });
});

// 重置分页
watch([selectedLevel, selectedStatus, searchTerm], () => {
  currentPage.value = 1;
  totalPages.value = Math.ceil(filteredWarnings.value.length / itemsPerPage);
});

// 统计信息
const highCount = computed(() => warnings.value.filter(w => w.level === 'high').length);
const mediumCount = computed(() => warnings.value.filter(w => w.level === 'medium').length);
const lowCount = computed(() => warnings.value.filter(w => w.level === 'low').length);
const totalCount = computed(() => warnings.value.length);

// 百分比计算
const highPercentage = computed(() => (highCount.value / totalCount.value) * 100);
const mediumPercentage = computed(() => (mediumCount.value / totalCount.value) * 100);
const lowPercentage = computed(() => (lowCount.value / totalCount.value) * 100);

// 确认告警
const acknowledgeWarning = (index: number) => {
  const warningIndex = (currentPage.value - 1) * itemsPerPage + index;
  const warning = filteredWarnings.value[warningIndex];
  
  // 在原始数据中查找告警
  const originalIndex = warnings.value.findIndex(w => 
    w.title === warning.title && w.time === warning.time);
  
  if (originalIndex >= 0) {
    warnings.value[originalIndex].status = 'resolved';
    showNotification('告警已确认', 'success');
  }
};

// 确认当前选中的告警
const acknowledgeSelectedWarning = () => {
  // 在原始数据中查找告警
  const originalIndex = warnings.value.findIndex(w => 
    w.title === selectedWarning.value.title && w.time === selectedWarning.value.time);
  
  if (originalIndex >= 0) {
    warnings.value[originalIndex].status = 'resolved';
    selectedWarning.value.status = 'resolved';
    showNotification('告警已确认', 'success');
  }
  
  closeDetailModal();
};

// 查看告警详情
const viewWarningDetails = (warning: any) => {
  selectedWarning.value = warning;
  showDetailModal.value = true;
};

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false;
};

// 显示通知
const showNotification = (message: string, type: string = 'info') => {
  notification.value = {
    show: true,
    message,
    type
  };
  
  // 3秒后自动隐藏
  setTimeout(() => {
    notification.value.show = false;
  }, 3000);
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
};

// 翻页
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 刷新数据
const refreshData = () => {
  isRefreshing.value = true;
  
  // 模拟刷新操作
  setTimeout(() => {
    isRefreshing.value = false;
    showNotification('数据已刷新', 'success');
  }, 1000);
};

// 导出数据
const exportData = () => {
  showNotification('告警数据已导出', 'success');
};

// 获取告警级别文本
const getLevelText = (level: string) => {
  switch (level) {
    case 'high': return '高级告警';
    case 'medium': return '中级告警';
    case 'low': return '低级告警';
    default: return '未知级别';
  }
};
</script>

<style scoped lang="scss">
.pcs-warning {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  color: #e0e0e0;
  
  .section-header {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #00a8ff;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  }
  
  .warning-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .warning-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    
    .filter-item {
      display: flex;
      align-items: center;
      
      &.search {
        margin-left: auto;
      }
      
      .filter-label {
        margin-right: 10px;
        white-space: nowrap;
      }
      
      .filter-options {
        display: flex;
        
        .option {
          padding: 5px 12px;
          background-color: rgba(0, 40, 80, 0.5);
          cursor: pointer;
          border: 1px solid rgba(0, 168, 255, 0.3);
          
          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }
          
          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }
          
          &:not(:first-child) {
            border-left: none;
          }
          
          &:hover {
            background-color: rgba(0, 60, 120, 0.5);
          }
          
          &.active {
            background-color: rgba(0, 100, 200, 0.5);
            color: white;
          }
        }
      }
      
      .search-input {
        padding: 6px 12px;
        width: 250px;
        background-color: rgba(0, 30, 60, 0.6);
        border: 1px solid rgba(0, 168, 255, 0.3);
        border-radius: 4px 0 0 4px;
        color: white;
        
        &:focus {
          outline: none;
          border-color: #00a8ff;
        }
      }
      
      .search-btn {
        padding: 6px 12px;
        background-color: rgba(0, 60, 120, 0.8);
        border: 1px solid rgba(0, 168, 255, 0.3);
        border-left: none;
        border-radius: 0 4px 4px 0;
        color: white;
        cursor: pointer;
        
        &:hover {
          background-color: rgba(0, 80, 160, 0.8);
        }
      }
    }
  }
  
  .warning-statistics {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-item {
      flex: 1;
      background-color: rgba(0, 40, 80, 0.3);
      border: 1px solid rgba(0, 168, 255, 0.3);
      border-radius: 4px;
      padding: 15px;
      position: relative;
      overflow: hidden;
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
        position: relative;
        z-index: 2;
      }
      
      .stat-label {
        font-size: 14px;
        position: relative;
        z-index: 2;
      }
      
      .stat-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background-color: rgba(255, 255, 255, 0.1);
        
        .bar-fill {
          height: 100%;
          transition: width 0.5s ease;
          
          &.high {
            background-color: #ff4040;
          }
          
          &.medium {
            background-color: #ffaa00;
          }
          
          &.low {
            background-color: #ffff40;
          }
        }
      }
    }
  }
  
  .warning-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    .warning-card {
      display: flex;
      background-color: rgba(0, 40, 80, 0.3);
      border: 1px solid rgba(0, 168, 255, 0.3);
      border-radius: 4px;
      overflow: hidden;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }
      
      &.high {
        border-left: 5px solid #ff4040;
      }
      
      &.medium {
        border-left: 5px solid #ffaa00;
      }
      
      &.low {
        border-left: 5px solid #ffff40;
      }
      
      .warning-icon {
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        
        &.high {
          color: #ff4040;
        }
        
        &.medium {
          color: #ffaa00;
        }
        
        &.low {
          color: #ffff40;
        }
      }
      
      .warning-details {
        flex: 1;
        padding: 15px 10px;
        
        .warning-title {
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 5px;
        }
        
        .warning-info {
          display: flex;
          gap: 15px;
          font-size: 12px;
          margin-bottom: 8px;
          opacity: 0.7;
        }
        
        .warning-description {
          font-size: 14px;
          line-height: 1.4;
        }
      }
      
      .warning-actions {
        width: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        padding: 0 10px;
        background-color: rgba(0, 30, 60, 0.5);
        
        .action-btn {
          width: 100%;
          padding: 6px 0;
          text-align: center;
          border: none;
          border-radius: 3px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s;
          
          &.acknowledge-btn {
            background-color: #00aa44;
            color: white;
            
            &:hover {
              background-color: #00cc55;
            }
          }
          
          &.details-btn {
            background-color: #0066aa;
            color: white;
            
            &:hover {
              background-color: #0088cc;
            }
          }
        }
      }
    }
    
    .no-warnings {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      color: #888;
      padding: 40px 0;
    }
  }
  
  .warning-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 168, 255, 0.2);
    
    .export-btn, .refresh-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;
    }
    
    .export-btn {
      background-color: #006633;
      color: white;
      
      &:hover {
        background-color: #007744;
      }
    }
    
    .refresh-btn {
      position: relative;
      
      .refresh-icon {
        display: inline-block;
        margin-right: 5px;
        transition: transform 0.3s ease;
      }
      
      &.refreshing .refresh-icon {
        animation: spin 1s linear infinite;
      }
    }
    
    .warning-pagination {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .page-btn {
        background-color: rgba(0, 60, 120, 0.5);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px 12px;
        cursor: pointer;
        
        &:hover:not(:disabled) {
          background-color: rgba(0, 80, 160, 0.5);
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 添加详情弹窗样式
.warning-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  .modal-content {
    width: 600px;
    max-width: 90%;
    background-color: #001530;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  }
  
  .modal-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 50, 100, 0.5);
    border-bottom: 1px solid rgba(0, 168, 255, 0.3);
    
    &.high {
      background-color: rgba(255, 50, 50, 0.3);
    }
    
    &.medium {
      background-color: rgba(255, 160, 0, 0.3);
    }
    
    &.low {
      background-color: rgba(255, 255, 0, 0.15);
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: bold;
      color: #00a8ff;
    }
    
    .close-btn {
      background: none;
      border: none;
      color: #aaa;
      font-size: 24px;
      cursor: pointer;
      
      &:hover {
        color: white;
      }
    }
  }
  
  .modal-body {
    padding: 20px;
    
    .detail-item {
      display: flex;
      margin-bottom: 15px;
      
      &.full {
        flex-direction: column;
        
        .detail-label {
          width: 100%;
          margin-bottom: 8px;
        }
        
        .detail-value {
          padding-left: 0;
        }
      }
      
      .detail-label {
        width: 120px;
        color: #aaa;
      }
      
      .detail-value {
        flex: 1;
        padding-left: 10px;
        
        .level-badge {
          padding: 3px 8px;
          border-radius: 10px;
          font-size: 12px;
          
          &.high {
            background-color: rgba(255, 0, 0, 0.2);
            color: #ff6060;
          }
          
          &.medium {
            background-color: rgba(255, 160, 0, 0.2);
            color: #ffcc60;
          }
          
          &.low {
            background-color: rgba(255, 255, 0, 0.2);
            color: #ffff60;
          }
        }
        
        &.recommendation {
          background-color: rgba(0, 60, 120, 0.2);
          padding: 12px;
          border-radius: 4px;
          line-height: 1.5;
          border-left: 3px solid #00a8ff;
        }
      }
    }
  }
  
  .modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid rgba(0, 168, 255, 0.3);
    
    .action-btn {
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      
      &.close-detail-btn {
        background-color: #333;
        color: white;
        
        &:hover {
          background-color: #444;
        }
      }
      
      &.acknowledge-btn {
        background-color: #00aa44;
        color: white;
        
        &:hover {
          background-color: #00bb55;
        }
      }
    }
  }
}

// 通知样式
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1100;
  
  .notification-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out forwards;
    
    &.success {
      background-color: rgba(0, 150, 50, 0.9);
      border-left: 4px solid #00cc44;
    }
    
    &.info {
      background-color: rgba(0, 100, 200, 0.9);
      border-left: 4px solid #00a8ff;
    }
    
    .notification-icon {
      margin-right: 10px;
      font-size: 18px;
    }
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// 翻页按钮禁用状态
.page-btn {
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: rgba(0, 60, 120, 0.5);
    }
  }
}
</style> 