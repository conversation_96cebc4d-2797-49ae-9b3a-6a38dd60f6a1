<template>
  <div class="testing-container">
    <transition-loading :isShow="loadShow" />
    <div class="header">
      <home-header />
    </div>
    <div class="content">
      <div class="sidebar">
        <div class="section-title">测试类型</div>
        <div class="test-type-menu">
          <div 
            v-for="test in testTypes" 
            :key="test.id"
            :class="['test-item', { active: currentTest === test.id }]"
            @click="selectTest(test.id)"
          >
            <span class="test-icon">{{ test.icon }}</span>
            <span class="test-name">{{ test.name }}</span>
          </div>
        </div>
        
        <!-- 添加导出报告按钮 -->
        <div class="export-section">
          <div class="section-title">测试报告</div>
          <button class="export-btn" @click="exportTestReport">
            <span class="export-icon">📊</span>
            <span class="export-text">导出测试报告</span>
          </button>
        </div>
      </div>
      
      <div class="main-panel">
        <!-- 测试结果显示区 -->
        <div class="results-display">
          <div class="panel-header">
            <span class="panel-title">测试结果显示区</span>
          </div>
          <div class="panel-content">
            <div class="result-grid">
              <div class="result-row header-row">
                <div class="result-cell">参数</div>
                <div class="result-cell">设定值</div>
                <div class="result-cell">测量值</div>
                <div class="result-cell">状态</div>
              </div>
              
              <transition-group name="test-result">
                <div v-for="(item, index) in currentTestResults" :key="item.name + index" class="result-row">
                  <div class="result-cell">{{ item.name }}</div>
                  <div class="result-cell">{{ item.setPoint }}</div>
                  <div class="result-cell">{{ item.measured }}</div>
                  <div class="result-cell">
                    <span :class="['status-indicator', item.status]">
                      {{ getStatusText(item.status) }}
                    </span>
                  </div>
                </div>
              </transition-group>
            </div>
            
            <transition name="fade">
              <div class="calculation-result" v-if="calculationResult">
                <div class="calc-title">计算结果:</div>
                <div class="calc-content">{{ calculationResult }}</div>
              </div>
            </transition>
          </div>
        </div>
        
        <!-- 参数设定区 -->
        <div class="parameter-settings">
          <div class="panel-header">
            <span class="panel-title">参数设定区</span>
          </div>
          <div class="panel-content">
            <div class="parameter-form">
              <transition-group name="param-change">
                <div v-for="(param, index) in currentParameters" :key="param.name + index" class="param-row">
                  <div class="param-label">{{ param.name }}:</div>
                  <div class="param-input">
                    <input 
                      type="text" 
                      v-model="param.value" 
                      :placeholder="param.placeholder"
                    />
                    <span class="param-unit">{{ param.unit }}</span>
                  </div>
                </div>
              </transition-group>
            </div>
            
            <div class="control-buttons">
              <button class="control-btn start" @click="startTest" :disabled="testStatus === 'running'">
                <span class="btn-icon" v-if="testStatus === 'running'">⏳</span>
                <span class="btn-icon" v-else>▶️</span>
                开始测试
              </button>
              <button class="control-btn stop" @click="stopTest" :disabled="testStatus !== 'running'">
                <span class="btn-icon">⏹️</span>
                停止测试
              </button>
              <button class="control-btn reset" @click="resetTest">
                <span class="btn-icon">🔄</span>
                重置
              </button>
            </div>
          </div>
        </div>
        
        <!-- 状态显示区 -->
        <div class="status-display">
          <div class="panel-header">
            <span class="panel-title">状态显示区</span>
          </div>
          <div class="panel-content">
            <div class="status-list">
              <transition-group name="status-update">
                <div v-for="(status, index) in systemStatus" :key="status.time + index" class="status-item">
                  <div class="status-time">{{ status.time }}</div>
                  <div class="status-message" :class="status.type">{{ status.message }}</div>
                </div>
              </transition-group>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加报告预览对话框 -->
    <div v-if="showReportPreview" class="report-preview-overlay" @click="closeReportPreview">
      <div class="report-preview-container" @click.stop>
        <div class="preview-header">
          <div class="preview-title">测试报告预览</div>
          <button class="close-preview-btn" @click="closeReportPreview">×</button>
        </div>
        <div class="preview-content">
          <div id="reportTemplate" ref="reportRef" class="report-template">
            <!-- 报告头部 -->
            <div class="report-header">
              <div class="company-logo">BMS</div>
              <div class="report-title-area">
                <h1 class="report-title">电池管理系统测试报告</h1>
                <p class="report-subtitle">Battery Management System Test Report</p>
              </div>
              <div class="report-info">
                <div class="report-info-item">
                  <span class="info-label">报告编号:</span>
                  <span class="info-value">{{ reportData.reportId }}</span>
                </div>
                <div class="report-info-item">
                  <span class="info-label">测试日期:</span>
                  <span class="info-value">{{ reportData.testDate }}</span>
                </div>
              </div>
            </div>
            
            <!-- 报告概述 -->
            <div class="report-section">
              <h2 class="section-title">测试概述</h2>
              <div class="section-content">
                <div class="summary-item">
                  <span class="summary-label">测试类型:</span>
                  <span class="summary-value">{{ currentTestName }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">测试时长:</span>
                  <span class="summary-value">{{ reportData.duration }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">测试结果:</span>
                  <span class="summary-value" :class="reportData.overallStatus">
                    {{ getOverallStatusText(reportData.overallStatus) }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 测试参数 -->
            <div class="report-section">
              <h2 class="section-title">测试参数</h2>
              <div class="section-content">
                <table class="report-table">
                  <thead>
                    <tr>
                      <th>参数名称</th>
                      <th>设定值</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(param, index) in currentParameters" :key="'param-' + index">
                      <td>{{ param.name }}</td>
                      <td>{{ param.value + param.unit }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <!-- 测试结果 -->
            <div class="report-section">
              <h2 class="section-title">测试结果</h2>
              <div class="section-content">
                <table class="report-table">
                  <thead>
                    <tr>
                      <th>测试项</th>
                      <th>预期值</th>
                      <th>实际值</th>
                      <th>结果</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(result, index) in currentTestResults" :key="'result-' + index">
                      <td>{{ result.name }}</td>
                      <td>{{ result.setPoint }}</td>
                      <td>{{ result.measured }}</td>
                      <td class="status-cell">
                        <span class="report-status" :class="result.status">
                          {{ getStatusText(result.status) }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <!-- 分析结论 -->
            <div class="report-section">
              <h2 class="section-title">分析与结论</h2>
              <div class="section-content">
                <p class="conclusion-text">{{ reportData.conclusion }}</p>
              </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="report-section">
              <h2 class="section-title">操作日志</h2>
              <div class="section-content">
                <div class="log-list">
                  <div v-for="(log, index) in reportData.logs" :key="'log-' + index" class="log-item">
                    <span class="log-time">{{ log.time }}</span>
                    <span class="log-message" :class="log.type">{{ log.message }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 报告页脚 -->
            <div class="report-footer">
              <div class="footer-line"></div>
              <div class="footer-content">
                <span class="footer-company">BMS电池管理系统</span>
                <span class="footer-page">第1页</span>
              </div>
            </div>
          </div>
        </div>
        <div class="preview-actions">
          <button class="download-btn" @click="downloadPdf">下载PDF</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import HomeHeader from './components/home-header/index.vue';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// 定义类型
interface TestItem {
  id: string;
  name: string;
  icon: string;
}

interface StatusMessage {
  time: string;
  message: string;
  type: string;
}

interface TestParameter {
  name: string;
  value: string;
  unit: string;
  placeholder: string;
}

interface TestResult {
  name: string;
  setPoint: string;
  measured: string;
  status: string;
}

interface ReportData {
  reportId: string;
  testDate: string;
  duration: string;
  overallStatus: string;
  conclusion: string;
  logs: {
    time: string;
    message: string;
    type: string;
  }[];
}

type TestParametersType = {
  [key: string]: TestParameter[];
}

type TestResultsType = {
  [key: string]: TestResult[];
}

// 加载状态
const loadShow = ref(false);

// 测试类型
const testTypes: TestItem[] = [
  { id: 'voltage', name: '电池电压测试', icon: '🔋' },
  { id: 'current', name: '电池残电流测试', icon: '⚡' },
  { id: 'temperature', name: '温度和绝缘电阻测试', icon: '🌡️' },
  { id: 'balance', name: '均衡测试', icon: '⚖️' },
  { id: 'control', name: '控制功能测试', icon: '🎮' },
  { id: 'alarm', name: '报警与保护测试', icon: '⚠️' },
  { id: 'energy', name: '能量状态估算', icon: '📊' },
  { id: 'directPower', name: '直流供电测试', icon: '🔌' }
];

// 当前选中的测试
const currentTest = ref('voltage');

// 测试状态
const testStatus = ref('idle'); // idle, running, completed, error

// 系统状态消息
const systemStatus = ref<StatusMessage[]>([
  { time: getCurrentTime(), message: '系统已就绪', type: 'info' }
]);

// 测试参数
const testParameters = reactive<TestParametersType>({
  voltage: [
    { name: '单体电压设定值', value: '3.7', unit: 'V', placeholder: '输入电压值' },
    { name: '总电压设定值', value: '48.1', unit: 'V', placeholder: '输入电压值' },
    { name: '测试时长', value: '10', unit: '分钟', placeholder: '输入时长' }
  ],
  current: [
    { name: '电池残电流', value: '20', unit: 'mA', placeholder: '输入电流值' },
    { name: '测试时长', value: '5', unit: '分钟', placeholder: '输入时长' }
  ],
  temperature: [
    { name: '温度设定值', value: '25', unit: '°C', placeholder: '输入温度值' },
    { name: '绝缘电阻设定值', value: '1000', unit: 'kΩ', placeholder: '输入电阻值' }
  ],
  balance: [
    { name: '均衡启动条件', value: '20', unit: 'mV', placeholder: '输入电压差值' },
    { name: '记录电池模拟器均衡前电压值', value: '3.8', unit: 'V', placeholder: '输入电压值' }
  ],
  control: [
    { name: 'BMS控制电压', value: '12', unit: 'V', placeholder: '输入电压值' },
    { name: '继电器状态', value: '1', unit: '', placeholder: '0:关闭 1:开启' }
  ],
  alarm: [
    { name: '单体过压阈值', value: '3.9', unit: 'V', placeholder: '输入电压值' },
    { name: '单体欠压阈值', value: '2.8', unit: 'V', placeholder: '输入电压值' },
    { name: '过温阈值', value: '45', unit: '°C', placeholder: '输入温度值' }
  ],
  energy: [
    { name: 'SOE初始百分比', value: '80', unit: '%', placeholder: '输入百分比' },
    { name: '初始单体电压值', value: '3.7', unit: 'V', placeholder: '输入电压值' }
  ],
  directPower: [
    { name: '供电源正常电压值', value: '12', unit: 'V', placeholder: '输入电压值' },
    { name: '过电压值', value: '14', unit: 'V', placeholder: '输入电压值' },
    { name: '反向电压值', value: '-12', unit: 'V', placeholder: '输入电压值' }
  ]
});

// 测试结果
const testResults = reactive<TestResultsType>({
  voltage: [
    { name: '单体电压', setPoint: '3.7V', measured: '3.68V', status: 'pass' },
    { name: '总电压', setPoint: '48.1V', measured: '48.05V', status: 'pass' }
  ],
  current: [
    { name: '电池残电流', setPoint: '20mA', measured: '18.5mA', status: 'pass' }
  ],
  temperature: [
    { name: '温度', setPoint: '25°C', measured: '24.8°C', status: 'pass' },
    { name: '绝缘电阻', setPoint: '1000kΩ', measured: '980kΩ', status: 'pass' }
  ],
  balance: [
    { name: '均衡前电压', setPoint: '3.8V', measured: '3.79V', status: 'pass' },
    { name: '均衡动作时电压值', setPoint: '20mV差异', measured: '22mV差异', status: 'pass' },
    { name: 'BMS各电池通道均衡状态', setPoint: '正常', measured: '工作中', status: 'pass' }
  ],
  control: [
    { name: 'BMS继电器控制电压', setPoint: '12V', measured: '11.9V', status: 'pass' },
    { name: '继电器工作状态', setPoint: '开启', measured: '正常', status: 'pass' }
  ],
  alarm: [
    { name: '单体过压保护', setPoint: '3.9V', measured: '已触发', status: 'pass' },
    { name: '单体欠压保护', setPoint: '2.8V', measured: '已触发', status: 'pass' },
    { name: '温度保护', setPoint: '45°C', measured: '已触发', status: 'pass' }
  ],
  energy: [
    { name: 'SOE状态', setPoint: '80%', measured: '79%', status: 'pass' },
    { name: '容量估算准确度', setPoint: '>95%', measured: '97%', status: 'pass' }
  ],
  directPower: [
    { name: '正常电压测试', setPoint: '12V', measured: '11.95V', status: 'pass' },
    { name: '过电压测试', setPoint: '14V', measured: '14.1V', status: 'pass' },
    { name: '反向电压测试', setPoint: '-12V', measured: '-11.98V', status: 'pass' }
  ]
});

// 计算结果
const calculationResult = ref('所有测试参数均在设定范围内，电池状态良好');

// 获取当前参数
const currentParameters = computed(() => {
  return testParameters[currentTest.value] || [];
});

// 获取当前测试结果
const currentTestResults = computed(() => {
  return testResults[currentTest.value] || [];
});

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'pass': return '通过';
    case 'fail': return '失败';
    case 'warning': return '警告';
    default: return '未知';
  }
};

// 选择测试
const selectTest = (testId: string): void => {
  currentTest.value = testId;
  const selectedTest = testTypes.find(t => t.id === testId);
  if (selectedTest) {
    addStatusMessage(`已选择${selectedTest.name}`, 'info');
  }
};

// 开始测试
const startTest = (): void => {
  if (testStatus.value === 'running') {
    addStatusMessage('测试已在进行中', 'warning');
    return;
  }
  
  testStatus.value = 'running';
  const selectedTest = testTypes.find(t => t.id === currentTest.value);
  if (selectedTest) {
    addStatusMessage(`开始${selectedTest.name}`, 'success');
  }
  
  // 模拟测试进行
  loadShow.value = true;
  
  setTimeout(() => {
    loadShow.value = false;
    testStatus.value = 'completed';
    const testName = testTypes.find(t => t.id === currentTest.value)?.name || '测试';
    addStatusMessage(`${testName}已完成`, 'success');
    
    // 更新结果
    updateTestResults();
  }, 3000);
};

// 停止测试
const stopTest = (): void => {
  if (testStatus.value !== 'running') {
    addStatusMessage('没有正在进行的测试', 'warning');
    return;
  }
  
  testStatus.value = 'idle';
  loadShow.value = false;
  const testName = testTypes.find(t => t.id === currentTest.value)?.name || '测试';
  addStatusMessage(`已停止${testName}`, 'warning');
};

// 重置测试
const resetTest = (): void => {
  // 重置参数到默认值
  testStatus.value = 'idle';
  addStatusMessage('测试已重置', 'info');
};

// 更新测试结果
const updateTestResults = (): void => {
  // 这里应该根据测试类型更新实际测试结果
  // 模拟更新，实际应从后端获取
  const results = testResults[currentTest.value];
  if (results) {
    results.forEach(result => {
      // 模拟略微偏差的测量值
      const setPointValue = parseFloat(result.setPoint);
      if (!isNaN(setPointValue)) {
        const deviation = (Math.random() * 0.1 - 0.05) * setPointValue;
        const measured = setPointValue + deviation;
        if (Math.abs(deviation) > 0.1 * setPointValue) {
          result.status = 'warning';
        } else {
          result.status = 'pass';
        }
      }
    });
  }
};

// 添加状态消息
const addStatusMessage = (message: string, type = 'info'): void => {
  systemStatus.value.unshift({
    time: getCurrentTime(),
    message,
    type
  });
  
  // 保持最近的10条消息
  if (systemStatus.value.length > 10) {
    systemStatus.value.pop();
  }
};

// 获取当前时间
function getCurrentTime(): string {
  const now = new Date();
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
}

// 获取当前测试名称
const currentTestName = computed(() => {
  const test = testTypes.find(t => t.id === currentTest.value);
  return test ? test.name : '';
});

// 获取整体状态文本
const getOverallStatusText = (status: string): string => {
  switch (status) {
    case 'pass': return '通过';
    case 'fail': return '失败';
    case 'warning': return '警告';
    default: return '未知';
  }
};

// 添加报告相关状态
const showReportPreview = ref(false);
const reportRef = ref<HTMLElement | null>(null);
const reportData = reactive<ReportData>({
  reportId: '',
  testDate: '',
  duration: '',
  overallStatus: 'pass',
  conclusion: '',
  logs: []
});

// 导出测试报告
const exportTestReport = () => {
  // 准备报告数据
  const now = new Date();
  reportData.reportId = `TEST-${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
  reportData.testDate = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
  reportData.duration = testStatus.value === 'completed' ? '约3分钟' : '测试未完成';
  
  // 决定整体状态
  let hasWarning = false;
  let hasFail = false;
  
  currentTestResults.value.forEach(result => {
    if (result.status === 'fail') hasFail = true;
    if (result.status === 'warning') hasWarning = true;
  });
  
  if (hasFail) {
    reportData.overallStatus = 'fail';
    reportData.conclusion = '测试过程中发现严重问题，测试未通过。建议检查设备并重新进行测试。';
  } else if (hasWarning) {
    reportData.overallStatus = 'warning';
    reportData.conclusion = '测试过程中发现轻微问题，但不影响基本功能。建议在后续使用中注意这些参数的变化。';
  } else {
    reportData.overallStatus = 'pass';
    reportData.conclusion = '所有测试项目均符合要求，电池管理系统工作正常。';
  }
  
  // 获取操作日志
  reportData.logs = [...systemStatus.value].slice(0, 5);
  
  // 显示报告预览
  showReportPreview.value = true;
};

// 关闭报告预览
const closeReportPreview = () => {
  showReportPreview.value = false;
};

// 下载PDF报告
const downloadPdf = async () => {
  if (!reportRef.value) return;
  
  // 显示加载动画
  loadShow.value = true;
  
  try {
    const content = reportRef.value;
    const canvas = await html2canvas(content, {
      scale: 2, // 提高像素比以提高清晰度
      useCORS: true, // 允许跨域图片
      logging: false, // 关闭日志
      backgroundColor: '#ffffff' // 设置白色背景
    });
    
    const imgData = canvas.toDataURL('image/jpeg', 1.0);
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const imgX = (pdfWidth - imgWidth * ratio) / 2;
    const imgY = 0;
    
    pdf.addImage(imgData, 'JPEG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);
    pdf.save(`${currentTestName.value}_测试报告_${reportData.reportId}.pdf`);
    
    // 添加成功消息
    addStatusMessage('测试报告已成功导出为PDF', 'success');
  } catch (error) {
    console.error('PDF生成失败:', error);
    addStatusMessage('PDF生成失败，请重试', 'error');
  } finally {
    // 隐藏加载动画
    loadShow.value = false;
  }
};

// 组件挂载
onMounted(() => {
  // 初始化操作
});
</script>

<style lang="scss" scoped>
.testing-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('@/assets/images/index-bg.png') no-repeat;
  background-size: 100% 100%;
  color: #fff;
  overflow: hidden;
}

.header {
  height: 77px;
  flex-shrink: 0;
}

.content {
  flex: 1;
  display: flex;
  padding: 0 20px 20px;
  gap: 20px;
  overflow: auto;
  min-height: 0;
}

.sidebar {
  width: 220px;
  background: rgba(4, 40, 90, 0.5);
  border-radius: 10px;
  padding: 15px;
  flex-shrink: 0;
  height: fit-content;
  
  .section-title {
    font-size: 18px;
    color: #01c2ff;
    margin-bottom: 15px;
    padding-left: 10px;
    border-left: 3px solid #2ca6ff;
  }
  
  .test-type-menu {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .test-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(1, 194, 255, 0.2);
      }
      
      &.active {
        background: rgba(1, 194, 255, 0.3);
        box-shadow: 0 0 10px rgba(1, 194, 255, 0.3);
      }
      
      .test-icon {
        margin-right: 10px;
        font-size: 18px;
      }
      
      .test-name {
        font-size: 14px;
      }
    }
  }
}

.main-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: visible;
  padding-right: 5px;
  min-width: 0;
}

.results-display,
.parameter-settings,
.status-display {
  background: rgba(4, 40, 90, 0.5);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  min-height: 0;
  flex-shrink: 0;
}

.results-display {
  flex: auto;
  min-height: auto;
}

.parameter-settings {
  flex: auto;
  min-height: auto;
}

.status-display {
  flex: auto;
  min-height: auto;
  max-height: none;
}

.panel-header {
  padding: 12px 15px;
  border-bottom: 1px solid rgba(1, 194, 255, 0.3);
  flex-shrink: 0;
  
  .panel-title {
    font-size: 16px;
    color: #01c2ff;
    padding-left: 10px;
    border-left: 3px solid #2ca6ff;
  }
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow: visible;
  height: auto;
  position: relative;
}

.result-grid {
  width: 100%;
  border: 1px solid rgba(1, 194, 255, 0.3);
  border-radius: 5px;
  overflow: hidden;
  table-layout: fixed;
  
  .result-row {
    display: flex;
    min-width: 0;
    
    &.header-row {
      background: rgba(1, 194, 255, 0.2);
      font-weight: bold;
    }
    
    &:not(.header-row):nth-child(odd) {
      background: rgba(255, 255, 255, 0.05);
    }
    
    &:not(.header-row):hover {
      background: rgba(1, 194, 255, 0.1);
    }
  }
  
  .result-cell {
    flex: 1;
    padding: 10px;
    text-align: center;
    border-right: 1px solid rgba(1, 194, 255, 0.2);
    white-space: normal;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &:last-child {
      border-right: none;
    }
  }
  
  .status-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    
    &.pass {
      background: rgba(0, 255, 117, 0.2);
      color: #00ff75;
    }
    
    &.fail {
      background: rgba(255, 110, 118, 0.2);
      color: #ff6e76;
    }
    
    &.warning {
      background: rgba(255, 204, 0, 0.2);
      color: #ffcc00;
    }
  }
}

.calculation-result {
  margin-top: 15px;
  padding: 10px;
  background: rgba(0, 255, 117, 0.1);
  border-radius: 5px;
  
  .calc-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #00ff75;
  }
  
  .calc-content {
    font-size: 14px;
  }
}

.parameter-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  
  .param-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .param-label {
      width: 180px;
      padding-right: 10px;
      text-align: right;
      color: #8ecbff;
      white-space: normal;
    }
    
    .param-input {
      flex: 1;
      position: relative;
      min-width: 120px;
      
      input {
        width: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(1, 194, 255, 0.3);
        padding: 8px 40px 8px 10px;
        border-radius: 5px;
        color: #fff;
        
        &:focus {
          outline: none;
          border-color: rgba(1, 194, 255, 0.8);
          box-shadow: 0 0 5px rgba(1, 194, 255, 0.5);
        }
      }
      
      .param-unit {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #8ecbff;
      }
    }
  }
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
  
  .control-btn {
    padding: 8px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .btn-icon {
      margin-right: 5px;
      font-size: 16px;
    }
    
    &.start {
      background: rgba(0, 255, 117, 0.3);
      color: #00ff75;
      
      &:disabled {
        background: rgba(0, 255, 117, 0.1);
      }
      
      &:hover {
        background: rgba(0, 255, 117, 0.5);
      }
    }
    
    &.stop {
      background: rgba(255, 110, 118, 0.3);
      color: #ff6e76;
      
      &:disabled {
        background: rgba(255, 110, 118, 0.1);
      }
      
      &:hover {
        background: rgba(255, 110, 118, 0.5);
      }
    }
    
    &.reset {
      background: rgba(1, 194, 255, 0.3);
      color: #01c2ff;
      
      &:hover {
        background: rgba(1, 194, 255, 0.5);
      }
    }
  }
}

.status-list {
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: visible;
  
  &::-webkit-scrollbar,
  &::-webkit-scrollbar-thumb,
  &::-webkit-scrollbar-track {
    display: none;
  }
  
  .status-item {
    display: flex;
    padding: 8px 0;
    border-bottom: 1px dashed rgba(1, 194, 255, 0.2);
    word-break: break-word;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 10px;
    }
    
    .status-time {
      width: 100px;
      min-width: 80px;
      color: #8ecbff;
      flex-shrink: 0;
    }
    
    .status-message {
      flex: 1;
      padding-left: 5px;
      
      &.info {
        color: #01c2ff;
      }
      
      &.success {
        color: #00ff75;
      }
      
      &.warning {
        color: #ffcc00;
      }
      
      &.error {
        color: #ff6e76;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content {
    flex-direction: column;
    padding: 0 10px 10px;
  }
  
  .sidebar {
    width: 100%;
    margin-bottom: 10px;
    
    .test-type-menu {
      flex-direction: row;
      flex-wrap: wrap;
      
      .test-item {
        width: calc(50% - 5px);
      }
    }
  }
  
  .main-panel {
    gap: 10px;
    width: 100%;
  }
  
  .results-display,
  .parameter-settings,
  .status-display {
    min-height: auto;
  }
  
  .status-display {
    max-height: none;
  }
  
  .status-list {
    .status-item {
      flex-direction: column;
      
      .status-time {
        width: 100%;
        margin-bottom: 5px;
      }
    }
  }
  
  .parameter-form {
    .param-row {
      flex-direction: column;
      align-items: flex-start;
      
      .param-label {
        width: 100%;
        text-align: left;
        margin-bottom: 5px;
      }
      
      .param-input {
        width: 100%;
      }
    }
  }
  
  .control-buttons {
    flex-wrap: wrap;
    
    .control-btn {
      flex: 1;
      min-width: 100px;
    }
  }
}

// 添加动画样式
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.test-result-enter-active, .test-result-leave-active {
  transition: all 0.5s ease;
}
.test-result-enter-from, .test-result-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.param-change-enter-active, .param-change-leave-active {
  transition: all 0.3s ease-in-out;
}
.param-change-enter-from, .param-change-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.status-update-enter-active, .status-update-leave-active {
  transition: all 0.4s ease;
}
.status-update-enter-from, .status-update-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 导出报告按钮样式
.export-section {
  margin-top: 20px;
  
  .export-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 117, 0.2);
    border: 1px solid rgba(0, 255, 117, 0.4);
    color: #00ff75;
    font-weight: bold;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(0, 255, 117, 0.3);
      box-shadow: 0 0 10px rgba(0, 255, 117, 0.3);
    }
    
    .export-icon {
      font-size: 20px;
      margin-right: 10px;
    }
    
    .export-text {
      font-size: 16px;
    }
  }
}

// 报告预览对话框样式
.report-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.report-preview-container {
  width: 90%;
  max-width: 900px;
  height: 90vh;
  background: #0a1a30;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(1, 194, 255, 0.3);
  border: 1px solid rgba(1, 194, 255, 0.3);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(1, 194, 255, 0.2);
  border-bottom: 1px solid rgba(1, 194, 255, 0.3);
  
  .preview-title {
    font-size: 18px;
    font-weight: bold;
    color: #01c2ff;
  }
  
  .close-preview-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    
    &:hover {
      color: #ff6e76;
    }
  }
}

.preview-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.preview-actions {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(1, 194, 255, 0.3);
  
  .download-btn {
    background: rgba(0, 255, 117, 0.3);
    color: #00ff75;
    font-weight: bold;
    padding: 10px 20px;
    border: 1px solid rgba(0, 255, 117, 0.5);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(0, 255, 117, 0.5);
      box-shadow: 0 0 10px rgba(0, 255, 117, 0.3);
    }
  }
}

// 报告模板样式
.report-template {
  background: #ffffff;
  color: #333333;
  padding: 30px;
  width: 100%;
  box-sizing: border-box;
  font-family: 'Arial', sans-serif;
  
  .report-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    border-bottom: 2px solid #01c2ff;
    padding-bottom: 20px;
    
    .company-logo {
      font-size: 40px;
      font-weight: bold;
      color: #01c2ff;
      background: #0a1a30;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
    }
    
    .report-title-area {
      flex: 1;
      text-align: center;
      
      .report-title {
        font-size: 24px;
        color: #0a1a30;
        margin: 0 0 5px;
      }
      
      .report-subtitle {
        font-size: 16px;
        color: #666;
        margin: 0;
      }
    }
    
    .report-info {
      width: 200px;
      
      .report-info-item {
        margin-bottom: 5px;
        font-size: 14px;
        
        .info-label {
          color: #666;
          margin-right: 5px;
        }
        
        .info-value {
          font-weight: bold;
          color: #333;
        }
      }
    }
  }
  
  .report-section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 18px;
      border-left: 4px solid #01c2ff;
      padding-left: 10px;
      margin-bottom: 15px;
      color: #0a1a30;
    }
    
    .section-content {
      padding: 0 20px;
      
      .summary-item {
        margin-bottom: 10px;
        
        .summary-label {
          font-weight: bold;
          margin-right: 10px;
          color: #444;
        }
        
        .summary-value {
          &.pass {
            color: #00aa44;
          }
          
          &.fail {
            color: #ff3333;
          }
          
          &.warning {
            color: #ff9900;
          }
        }
      }
      
      .report-table {
        width: 100%;
        border-collapse: collapse;
        
        th, td {
          border: 1px solid #ddd;
          padding: 10px;
          text-align: left;
        }
        
        th {
          background: #f2f9ff;
          color: #01c2ff;
          font-weight: bold;
        }
        
        tr:nth-child(even) {
          background: #f9f9f9;
        }
        
        .status-cell {
          text-align: center;
          
          .report-status {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 12px;
            
            &.pass {
              background: rgba(0, 170, 68, 0.1);
              color: #00aa44;
              border: 1px solid rgba(0, 170, 68, 0.3);
            }
            
            &.fail {
              background: rgba(255, 51, 51, 0.1);
              color: #ff3333;
              border: 1px solid rgba(255, 51, 51, 0.3);
            }
            
            &.warning {
              background: rgba(255, 153, 0, 0.1);
              color: #ff9900;
              border: 1px solid rgba(255, 153, 0, 0.3);
            }
          }
        }
      }
      
      .conclusion-text {
        line-height: 1.6;
        color: #333;
      }
      
      .log-list {
        .log-item {
          padding: 8px 0;
          border-bottom: 1px dashed #ddd;
          
          &:last-child {
            border-bottom: none;
          }
          
          .log-time {
            display: inline-block;
            width: 90px;
            color: #666;
            font-size: 13px;
          }
          
          .log-message {
            &.info {
              color: #01c2ff;
            }
            
            &.success {
              color: #00aa44;
            }
            
            &.warning {
              color: #ff9900;
            }
            
            &.error {
              color: #ff3333;
            }
          }
        }
      }
    }
  }
  
  .report-footer {
    margin-top: 40px;
    
    .footer-line {
      height: 2px;
      background: #01c2ff;
      margin-bottom: 10px;
    }
    
    .footer-content {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #666;
      
      .footer-company {
        font-weight: bold;
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .report-preview-container {
    width: 95%;
    height: 95vh;
  }
  
  .report-template {
    padding: 15px;
    
    .report-header {
      flex-direction: column;
      align-items: center;
      gap: 15px;
      
      .company-logo {
        margin-bottom: 10px;
      }
      
      .report-info {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style> 