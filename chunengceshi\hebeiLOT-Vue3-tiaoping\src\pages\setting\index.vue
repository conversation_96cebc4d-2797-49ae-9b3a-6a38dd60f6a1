<template>
	<div class="home">
		<transition-loading :isShow="loadShow" />
		<div class="chart-list">
			<home-header />
			<div style="padding: 0 8px" class="chart-content">
				<a-row :gutter="[8, 8]" class="chart-content-row">
					<a-col :span="3">
						<div class="left box">
							<div :class="['left-li', selectLeftId == leftItem.id ? 'act' : '']"
								v-for="leftItem in leftList" :key="leftItem.id" @click="selectLeftEvent(leftItem.id)">
								<div class="text">{{ leftItem.table_name }}</div>
							</div>
						</div>
					</a-col>
					<a-col :span="21">
						<PcsLog/>
						<!-- <component :is="currentComponent" /> -->
					</a-col>
				</a-row>
				<earth-bg />
			</div>
		</div>

	</div>
</template>

<script lang="ts" setup>
import { ref, } from 'vue';
import HomeHeader from './components/home-header/index.vue';
import EarthBg from './components/earth-bg/index.vue';
import PcsLog from './components/pcs-log/index.vue';

const loadShow = ref(false);
const leftList = ref([
	{ id: 1, table_name: '设备日志' },
	{ id: 2, table_name: '系统设置' }
]);
const selectLeftId = ref(1);

const selectLeftEvent = (id: number) => {
	selectLeftId.value = id;
};


</script>



<style lang="scss" scoped>
.control-params-card {
	//   background-color: #000033;
	// display: flex;
	// flex-direction: column;
	// align-items: center;
	// justify-content: flex-start;
	height: 100%;
	width: 80%;
	margin: auto;
	padding: 20px;
	color: white;
}

.table {
	padding: 20px;
}

.control-params-card :deep(.ant-card-head-title) {
	color: white;
	text-align: center;
}

.control-params-card :deep(.ant-form-item-label > label) {
	color: white;
}

.control-params-card :deep(.ant-slider-rail) {
	background-color: #1e3a8a;
}

.control-params-card :deep(.ant-slider-track) {
	background-color: #3b82f6;
}

.control-params-card :deep(.ant-slider-handle) {
	border-color: #3b82f6;
}

.button-wrapper {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

.home {
	position: relative;
	width: 100%;
	height: 100%;
	background: url('@/assets/images/index-bg.png') no-repeat;
	background-size: 100% 100%;

	.chart-list {
		height: 100%;


		.chart-content {
			height: calc(100% - 77px);
			margin-top: 12px;

			.chart-content-row,
			.chart-content-col {
				height: 100%;
			}

			.chart-container {
				width: 100%;
				height: 100%;
			}

			.container {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				justify-content: space-around;
				margin-top: 20px;

				.li {
					width: 40%;
					height: 100px;
					background-color: #060c20;
					color: white;
					font-size: 25px;
					padding-top: 30px;
					padding-left: 10px;

					// display: flex;
					// align-items: center;
					.sw {
						transform: scale(1.5);
					}
				}
			}

			.virtual-list-content {
				display: flex;
				flex-direction: column;
				height: 98%;
				padding: 0 8px;

				.virtual-list-item {
					display: flex;
					gap: 8px;
					align-items: center;
					padding: 4px;
					color: rgb(255 255 255);
					cursor: pointer;

					&:hover {
						color: #68d8ff;
						background: rgb(255 255 255 / 10%);
					}

					&-col {
						width: 16%;
						overflow: hidden;
						text-align: center;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&-col:nth-child(1) {
						width: 19.5%;
						text-align: left;
					}
				}
			}

			&-left {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-center {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;

					.index-data {
						display: flex;
						flex-direction: column;
						height: 100%;
						margin: 0 16px;
					}
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-right {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item {
					flex: 1;
				}
			}
		}
	}

	.box {
		width: 100%;
		height: 95%;
		background-color: rgba($color: #04285a, $alpha: 0.5);
	}

	.left {
		&-li {
			margin-bottom: 10px;
			background-color: rgb(#5d9dae, 0.1);
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			// padding-left: 30px;
			cursor: pointer;

			.text {
				color: #279abd;
				font-size: 20px;
			}

			&.act {
				background-color: rgb(#01c2ff, 0.1);
				border: 3px #058dc1 solid;

				// background-image: url('/src/assets/images/bg1.png') ;
				// background-repeat: no-repeat;
				// background-size: 100%;
				.text {
					color: #01c2ff;
				}
			}
		}
	}

	.right {
		padding: 10px;
		height: 90vh;

		.title {
			color: #fff;
			border-left: #2ca6ff 3px solid;
			padding-left: 10px;
			margin-top: 10px;
			margin-left: 10px;
			font-size: 18px;
		}

		.edit {
			margin: auto;
			margin-top: 20px;
			margin-bottom: 5px;
			width: 98%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-left {
				display: flex;
				gap: 15px;
				width: 40%;
			}

			&-right {
				display: flex;
				gap: 15px;

				&-item {
					cursor: pointer;
				}
			}
		}
	}

	.right::-webkit-scrollbar {
		width: 10px;
	}

	.right::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 2px;
	}

	.right::-webkit-scrollbar-thumb {
		background: #888;
		border-radius: 10px;
	}

	.right::-webkit-scrollbar-thumb:hover {
		background: #555;
	}
}

// 小屏幕下的样式
@media (max-width: 576px) {
	.home {
		height: unset;
		background: #060c20;

		.chart-content {
			.chart-content-col:first-child {
				height: 1000px !important;
			}

			&-left,
			&-center {
				&-item {
					flex: 1 !important;
				}
			}

			.chart-content-col:nth-child(2) {
				height: 1500px !important;
			}

			.chart-content-col:nth-child(3) {
				height: 1500px !important;
			}
		}
	}
}
</style>

<style lang="scss">
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis, .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis{
	color: #fff !important;
}
.ant-pagination{
	display: flex;
    margin-top: 1.25rem;
    justify-content: flex-end;
	color: #fff;
}
.ant-tabs-tab-btn {
	color: #fff;
}

.ant-tooltip-inner {
	min-height: unset;
}

.ant-form-item-label {
	text-align: left;
}

.ant-table-thead>tr>th {
	color: #fff;
	background-color: rgba($color: #2ca6ff, $alpha: 0.5);
}

.ant-table-tbody {
	color: #fff;
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table-tbody>tr.ant-table-row:hover>td {
	background-color: rgba($color: #1e5c88, $alpha: 0.7);
}

.ant-table-tbody>tr.ant-table-row>td {
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table {
	background: none !important;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
