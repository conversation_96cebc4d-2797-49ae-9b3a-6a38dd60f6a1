import { GET, POST, PUT, DELETE, DOWNLOAD } from '@/service/api';

// 获取PCS日志数据，增加搜索参数和分页
export const getPCSLogs = (params: any) => {
    let url = '/task-schedules/pcs-logs';
    const queryParams = [];

    if (params.page) queryParams.push(`page=${params.page}`);
    if (params.pageSize) queryParams.push(`pageSize=${params.pageSize}`);
    if (params.search) queryParams.push(`search=${params.search}`);
    if (params.startTime) queryParams.push(`startTime=${params.startTime}`);
    if (params.endTime) queryParams.push(`endTime=${params.endTime}`);
    if (params.sortOrder) queryParams.push(`sortOrder=${params.sortOrder}`);

    if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
    }

    return GET(url);
};

// 添加新记录
export const addRecord = (data: any) => {
    return POST('/user', data);
};

// 更新记录
export const updateRecord = (data: any) => {
    return PUT(`/user/${data.user_id}`, data);
};

// 删除记录
export const deleteRecord = (user_id: number) => {
    return DELETE(`/user/${user_id}`);
};

// 下载PCS日志
export const downloadPCSLogs = (params: any) => {
    let url = '/task-schedules/pcs-logs/download';
    const queryParams = [];

    if (params.startTime) queryParams.push(`startTime=${params.startTime}`);
    if (params.endTime) queryParams.push(`endTime=${params.endTime}`);
    if (params.sortOrder) queryParams.push(`sortOrder=${params.sortOrder}`);

    if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
    }

    return DOWNLOAD(url);
};
