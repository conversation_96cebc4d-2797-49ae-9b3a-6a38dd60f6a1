<template>
  <div class="pcs-data-table">
    <div class="data-row header-row">
      <div class="column column-title">机器参数</div>
      <div class="column column-title">状态参数</div>
      <div class="column column-title">告警参数</div>
      <div class="column column-title">机器数据</div>
      <div class="column column-title">机器数值</div>
    </div>
    
    <!-- 电压电流参数行 -->
    <div class="data-row">
      <div class="column param-name">电压电流</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">12</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">A 相电压，B 相电压，C 相电压超限</span>
      </div>
      <div class="column device-data">
        <div class="device-name">变流器A相电压</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.23Hz, 5.67kw</div>
        </div>
      </div>
    </div>
    
    <!-- 工作状态行 -->
    <div class="data-row">
      <div class="column param-name">工作状态</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">15</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">A 相欠压，B 相过流，C 相过流</span>
      </div>
      <div class="column device-data">
        <div class="device-name">变流器B相电压</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.23Hz, 5.67kw</div>
        </div>
      </div>
    </div>
    
    <!-- 系统配置参数行 -->
    <div class="data-row">
      <div class="column param-name">系统配置参数</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">35</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">功率超限 系统停机</span>
      </div>
      <div class="column device-data">
        <div class="device-name">变流器C相电压</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.23Hz, 5.67kw</div>
        </div>
      </div>
    </div>
    
    <!-- 系统输出设定行 -->
    <div class="data-row">
      <div class="column param-name">系统输出设定</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">58</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">PCS离网运行中</span>
      </div>
      <div class="column device-data">
        <div class="device-name">BMS 系统保护功能</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.23Hz, 5.67kw</div>
        </div>
      </div>
    </div>
    
    <!-- 直流侧参数设定行 -->
    <div class="data-row">
      <div class="column param-name">直流侧参数设定</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">88</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">无</span>
      </div>
      <div class="column device-data">
        <div class="device-name">中央集中控制器</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot red"></div>
          <div class="value-text">1.32Hz, 6.54kw</div>
        </div>
      </div>
    </div>
    
    <!-- 直流侧设定行 -->
    <div class="data-row">
      <div class="column param-name">直流侧设定</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">49</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">无</span>
      </div>
      <div class="column device-data">
        <div class="device-name">中控集中控制器</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot red"></div>
          <div class="value-text">1.00Hz, 5.64kw</div>
        </div>
      </div>
    </div>
    
    <!-- 交流侧参数设定行 -->
    <div class="data-row">
      <div class="column param-name">交流侧参数设定</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">112</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">100字节/100分钟内未响应</span>
      </div>
      <div class="column device-data">
        <div class="device-name">干式变压器冷却系统</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.34Hz, 5.36kw</div>
        </div>
      </div>
    </div>
    
    <!-- 系统电压频率设置行 -->
    <div class="data-row">
      <div class="column param-name">系统电压频率设置</div>
      <div class="column alarm">
        <div class="status-dot red"></div>
        <div class="alarm-value">94</div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">S1: 50Hz, T: 380V, Phase: 3</span>
      </div>
      <div class="column device-data">
        <div class="device-name">隔离变压器冷却系统</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.28Hz, 5.90kw</div>
        </div>
      </div>
    </div>
    
    <!-- 以下是更多数据行，可以继续添加其他行 -->
    <div class="data-row">
      <div class="column param-name">系统控制设置</div>
      <div class="column alarm">
        <div class="status-dot"></div>
        <div class="alarm-value"></div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">无</span>
      </div>
      <div class="column device-data">
        <div class="device-name">低压配电柜监控系统</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">1.28Hz, 5.90kw</div>
        </div>
      </div>
    </div>
    
    <div class="data-row">
      <div class="column param-name">电池监控系统</div>
      <div class="column alarm">
        <div class="status-dot"></div>
        <div class="alarm-value"></div>
      </div>
      <div class="column alarm-desc">
        <span class="alarm-text">无</span>
      </div>
      <div class="column device-data">
        <div class="device-name">自动继电器</div>
      </div>
      <div class="column device-value">
        <div class="device-value-container">
          <div class="value-dot orange"></div>
          <div class="value-text">0.1Hz - 1.5Hz</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 创建一个模拟数据更新效果
onMounted(() => {
  const updateInterval = setInterval(() => {
    const dots = document.querySelectorAll('.value-dot');
    dots.forEach(dot => {
      // 随机闪烁效果
      if (Math.random() > 0.7) {
        dot.classList.add('blink');
        setTimeout(() => {
          dot.classList.remove('blink');
        }, 300);
      }
    });
  }, 2000);
});
</script>

<style scoped lang="scss">
.pcs-data-table {
  width: 100%;
  border-collapse: collapse;
  color: white;
  margin-top: 20px;
  font-family: electronicFont, sans-serif;
  
  .data-row {
    display: grid;
    grid-template-columns: 0.8fr 0.4fr 1.2fr 0.8fr 1fr;
    position: relative;
    transition: background-color 0.3s;
    
    &:not(.header-row) {
      border-bottom: 1px solid rgba(0, 100, 200, 0.3);
      
      &:nth-child(odd) {
        background-color: rgba(0, 40, 80, 0.2);
      }
    }
    
    &::before, &::after {
      content: "";
      position: absolute;
      width: 2px;
      height: 2px;
      background-color: #0099ff;
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    &::before {
      top: 2px;
      left: 2px;
    }
    
    &::after {
      bottom: 2px;
      right: 2px;
    }
    
    &:hover {
      background-color: rgba(0, 70, 140, 0.3);
      
      &::before, &::after {
        opacity: 1;
      }
    }
    
    &.header-row {
      font-weight: bold;
      background-color: rgba(0, 50, 100, 0.5);
      padding: 10px 0;
      border-bottom: 2px solid rgba(0, 120, 255, 0.5);
      position: sticky;
      top: 0;
      z-index: 10;
      
      .column-title {
        position: relative;
        
        &::after {
          content: "";
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 40%;
          height: 2px;
          background-color: rgba(0, 160, 255, 0.5);
        }
      }
    }
    
    .column {
      padding: 10px;
      display: flex;
      align-items: center;
      position: relative;
      
      &.param-name {
        font-weight: 500;
        color: #aaccff;
        position: relative;
        padding-left: 20px;
        
        &::before {
          content: "▶";
          position: absolute;
          left: 5px;
          font-size: 10px;
          color: #00a8ff;
        }
      }
      
      &.alarm {
        justify-content: flex-start;
        color: #ff5555;
        
        .alarm-value {
          margin-left: 5px;
          font-weight: 500;
        }
      }
      
      &.alarm-desc {
        color: #ffaa00;
        
        .alarm-text {
          position: relative;
          padding-left: 15px;
          
          &::before {
            content: "!";
            position: absolute;
            left: 0;
            font-weight: bold;
            color: #ff5555;
          }
        }
      }
      
      &.device-data {
        color: #eeeeee;
      }
      
      .device-name {
        font-size: 14px;
        position: relative;
        padding-left: 15px;
        
        &::before {
          content: "•";
          position: absolute;
          left: 0;
          font-size: 18px;
          color: #00a8ff;
        }
      }
      
      .device-value-container {
        display: flex;
        align-items: center;
        color: #ffaa55;
        
        .value-text {
          font-family: electronicFont, monospace;
          letter-spacing: 1px;
        }
      }
      
      .status-dot, .value-dot {
        min-width: 12px;
        min-height: 12px;
        border-radius: 50%;
        margin-right: 10px;
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        position: relative;
        
        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.7);
        }
        
        &.red {
          background-color: #ff3333;
          box-shadow: 0 0 8px rgba(255, 0, 0, 0.5);
        }
        
        &.orange {
          background-color: #ffaa00;
          box-shadow: 0 0 8px rgba(255, 170, 0, 0.5);
        }
        
        &.green {
          background-color: #33cc33;
          box-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
        }
        
        &.blink {
          animation: blink-animation 0.3s ease;
        }
      }
    }
  }
}

@keyframes blink-animation {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}
</style> 