<template>
    <div class="right box">
        <div class="title">{{ tableTitle }}</div>
        <div class="container">
            <a-row>
                <a-col>
                    工步名称：
                    <a-input v-model:value="stepName" placeholder="请输入工步名称" />
                </a-col>
                <a-col :offset="1" style="margin-bottom: 20px;">
                    加载工步：
                    <a-select v-model:value="selectedStep" style="width: 300px; display: block;" @change="loadStep">
                        <a-select-option v-for="step in availableSteps" :key="step.design_id" :value="step.design_id">
                            {{ step.step_name }}
                        </a-select-option>
                    </a-select>
                </a-col>
                <a-col :offset="1" style="">
                    <div> - </div>
                    <a-button type="default" @click="clearStepSelection" danger>清除</a-button>
                </a-col>
                <a-col v-if="selectedStep" :offset="0" style="margin-left:10px">
                    <div> - </div>
                    <a-button type="primary" @click="confirmDelete" danger>删除</a-button>
                </a-col>
                <a-col v-if="selectedStep" :offset="0" style="margin-left:10px">
                    <div> - </div>
                    <a-button type="primary" :size="30" @click="exportStepDesignLogs">
                        <template #icon>
                            <ExportOutlined />
                        </template>
                    </a-button>
                </a-col>
                <a-col v-if="!selectedStep" :offset="0" style="margin-left:10px">
                    <div> - </div>
                    <input type="file" @change="importStepDesign" style="display: none;" ref="fileInput" />
                    <a-button type="primary" :size="30" @click="$refs.fileInput.click()">
                        <template #icon>
                            <DownloadOutlined />
                        </template>
                    </a-button>
                </a-col>
                <a-col :offset="7" style="display: flex; justify-content: flex-end; align-items: center">
                    <a-button type="primary" style="width: 100px;" @click="saveSteps">保存</a-button>
                </a-col>
                <a-col :span="24">
                    <a-card class="flowBox" @click="addStep">
                        <div v-if="!isEndStepAdded" class="remind">点击新增节点</div>
                        <transition-group name="fade" tag="div">
                            <div v-for="(step, index) in steps" :key="step.id" class="flowBox-row"
                                :class="step.className" @click.stop>
                                <div class="flowBox-row-item-delete" @click="removeStep(index)">
                                    <DeleteOutlined :style="{ fontSize: '26px', color: '#fff' }" />
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.result !== ''">
                                    <p>{{ step.result }}</p>
                                </div>
                                <div class="flowBox-row-item">
                                    <p class="flowBox-row-item-number">{{ index + 1 }}</p>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)">
                                    <p>操作类型：</p>
                                    <a-select v-model:value="step.type" style="width: 120px"
                                        @change="handleStepTypeChange(step, index)">
                                        <a-select-option value="充电">充电</a-select-option>
                                        <a-select-option value="放电">放电</a-select-option>
                                        <a-select-option value="静置">静置</a-select-option>
                                        <a-select-option value="循环">循环</a-select-option>
                                        <a-select-option value="跳转">跳转</a-select-option>
                                        <a-select-option value="工况">工况</a-select-option>
                                        <a-select-option value="结束">结束</a-select-option>
                                    </a-select>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '充电'">
                                    <p>参数：</p>
                                    <a-select v-model:value="step.parameter" style="width: 120px">
                                        <a-select-option value="恒流">恒流</a-select-option>
                                        <a-select-option value="恒功率">恒功率</a-select-option>
                                        <a-select-option value="恒压">恒压</a-select-option>
                                    </a-select>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '工况'">
                                    <p>工况选择：</p>
                                    <a-select v-model:value="step.templateID" style="width: 120px">
                                        <a-select-option v-for="template in availableTemplates"
                                            :key="template.templateID" :value="template.templateID">
                                            {{ template.templateName }}
                                        </a-select-option>
                                    </a-select>
                                    <!-- <span style="margin-left: 10px;">分钟</span> -->
                                    <div class="wokring" @click="showWorkModal()">
                                        <a-button>操作</a-button>
                                    </div>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '放电'">
                                    <p>参数：</p>
                                    <a-select v-model:value="step.parameter" style="width: 120px">
                                        <a-select-option value="恒流">恒流</a-select-option>
                                        <a-select-option value="恒功率">恒功率</a-select-option>
                                    </a-select>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="(step.type === '充电' || step.type === '放电') && step.parameter === '恒流'">
                                    <p>电流：</p>
                                    <a-input-number v-model:value="step.current" style="width: 120px"></a-input-number>
                                    <span>A</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '充电' && step.parameter === '恒压'">
                                    <p>电压：</p>
                                    <a-input-number v-model:value="step.voltage" style="width: 120px"></a-input-number>
                                    <span>V</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.parameter === '恒功率'">
                                    <p>功率：</p>
                                    <a-input-number v-model:value="step.power" style="width: 120px"></a-input-number>
                                    <span>W</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '充电' || step.type === '放电'">
                                    <p>结束条件：</p>
                                    <a-select v-model:value="step.endCondition" style="width: 120px">
                                        <a-select-option value="SOC状态">SOC状态</a-select-option>
                                        <a-select-option value="时间">时间</a-select-option>
                                        <a-select-option value="电流">电流</a-select-option>
                                        <a-select-option value="电压">电压</a-select-option>
                                    </a-select>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.endCondition === 'SOC状态'">
                                    <p>SOC数值：</p>
                                    <a-input-number v-model:value="step.socValue" style="width: 120px"></a-input-number>
                                    <span style="margin-left: 10px;">%</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.endCondition === '时间'">
                                    <p>时间：</p>
                                    <a-input-number v-model:value="step.timeValue"
                                        style="width: 120px"></a-input-number>
                                    <span style="margin-left: 10px;">分钟</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.endCondition === '电压'">
                                    <p>电压值：</p>
                                    <a-input-number v-model:value="step.endVoltageValue"
                                        style="width: 120px"></a-input-number>
                                    <span style="margin-left: 10px;">V</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.endCondition === '电流'">
                                    <p>电流值：</p>
                                    <a-input-number v-model:value="step.endCurrentValue"
                                        style="width: 120px"></a-input-number>
                                    <span style="margin-left: 10px;">A</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '静置'">
                                    <p>静置时间：</p>
                                    <a-input-number v-model:value="step.restTime" style="width: 120px"></a-input-number>
                                    <span style="margin-left: 10px;">分钟</span>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '循环'">
                                    <p>循环次数：</p>
                                    <a-input-number v-model:value="step.loopCount"
                                        style="width: 120px"></a-input-number>
                                    <p>从步骤：</p>
                                    <a-select v-model:value="step.loopStart" style="width: 120px">
                                        <a-select-option v-for="(item, idx) in steps" :key="idx" :value="idx + 1"
                                            :disabled="index === idx">
                                            {{ idx + 1 }}
                                        </a-select-option>
                                    </a-select>
                                    <p>到步骤：</p>
                                    <a-select v-model:value="step.loopEnd" style="width: 120px">
                                        <a-select-option v-for="(item, idx) in steps" :key="idx" :value="idx + 1"
                                            :disabled="index === idx">
                                            {{ idx + 1 }}
                                        </a-select-option>
                                    </a-select>
                                </div>
                                <div class="flowBox-row-item" :class="getStepClass(step.type)"
                                    v-if="step.type === '跳转'">
                                    <p>跳转到步骤：</p>
                                    <a-select v-model:value="step.jumpTo" style="width: 120px">
                                        <a-select-option v-for="(item, idx) in steps" :key="idx" :value="idx + 1"
                                            :disabled="index === idx">
                                            {{ idx + 1 }}
                                        </a-select-option>
                                    </a-select>
                                </div>
                            </div>
                        </transition-group>
                    </a-card>
                </a-col>
            </a-row>
        </div>
        <a-modal :visible="openwork" title="工况" @ok="handleOk" @cancel="handleCancel" width="100%" :footer="null"
            wrap-class-name="full-modal">
            <div class="workModal">
                <templateStatus />
            </div>
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { DeleteOutlined, DownloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
import { getAllBatterySimulations, getAllSteps, createStepDesign, updateStepDesign, getStepDesignById, deleteStepDesign, downloadStepDesignLogsAsExcel, importStepDesigns } from './service';
import { message, Modal } from 'ant-design-vue';
import templateStatus from '../template-status/index.vue';
import { getAllTemplates } from '../template-status/service';

const tableTitle = ref('工步设计');
const steps = ref([]);
const isEndStepAdded = ref(false);
const batteryModel = ref('');
const batteryInfo = ref({});
const stepName = ref('');
const selectedStep = ref('');
const availableBatteries = ref([]);
const availableSteps = ref([]);
const availableTemplates = ref([]);


const openwork = ref(false)
const showWorkModal = () => {
    openwork.value = true;
};
const handleOk = () => {
    openwork.value = false;
};
const handleCancel =async () => {
    openwork.value = false;
    await loadAvailableTemplates(); 
};

// 新增方法：根据design_id查询并回填工步
const loadStepById = async (designId) => {
    try {
        const res = await getStepDesignById(designId);
        const stepDesign = res.data.data;
        stepName.value = stepDesign.step_name;
        steps.value = stepDesign.stepdetails.map(detail => ({
            id: detail.detail_id,
            type: detail.step_type,
            parameter: detail.parameter,
            current: detail.current,
            voltage: detail.voltage,
            power: detail.power,
            endCondition: detail.end_condition,
            socValue: detail.soc_value,
            timeValue: detail.time_value,
            restTime: detail.rest_time,
            loopCount: detail.loop_count,
            loopStart: detail.loop_start,
            loopEnd: detail.loop_end,
            jumpTo: detail.jump_to,
            parentId: detail.parent_step_id,
            className: detail.class_name,
            result: detail.result,
            endVoltageValue: detail.end_voltage_value, // 新增字段
            endCurrentValue: detail.end_current_value, // 新增字段
            templateID: detail.working_id// 使用每个节点的 workingID
        }));

        // 检查是否存在“结束”步骤
        isEndStepAdded.value = steps.value.some(step => step.type === '结束');
    } catch (error) {
        message.error('加载工步设计失败');
        console.error('Error loading step design:', error);
    }
};

const addStep = () => {
    if (!stepName.value.trim()) {
        message.warning('请先输入工步名称');
        return;
    }
    if (!isEndStepAdded.value) {
        if (steps.value.length === 0) {
            steps.value.push(createStepObj('充电'));
        } else {
            steps.value.push(createStepObj(''));
        }
    }
};

const createStepObj = (type, parentId = null, result = '') => {
    return {
        id: Date.now() + Math.random(), // Unique ID for each step
        step_name: stepName.value || '',
        type: type || '',
        parameter: '',
        current: null,
        voltage: null,
        power: null,
        endCondition: '',
        socValue: null,
        timeValue: null,
        restTime: null,
        loopCount: null,
        loopStart: null,
        loopEnd: null,
        jumpTo: null,
        parentId: parentId,
        className: parentId ? 'sub-step' : 'main-step',
        result: result,
        endVoltageValue: null, // 新增字段
        endCurrentValue: null, // 新增字段
        templateID: null// 每个节点的 workingID
    };
};

const removeStep = (index: number) => {
    const removedStep = steps.value.splice(index, 1)[0];
    if (removedStep.type === '结束') {
        isEndStepAdded.value = false;
    }
    // Remove child steps as well
    steps.value = steps.value.filter(step => step.parentId !== removedStep.id);
};

const handleStepTypeChange = (step, index) => {
    if (step.type === '结束') {
        isEndStepAdded.value = true;
    } else if (step.type !== '结束' && isEndStepAdded.value) {
        isEndStepAdded.value = false;
    }
};

const selectBattery = (value) => {
    const selectedBattery = availableBatteries.value.find(battery => battery.model === value);
    if (selectedBattery) {
        batteryInfo.value = selectedBattery;
    }
};

const loadStep = async () => {
    try {
        const res = await getStepDesignById(selectedStep.value);
        const stepDesign = res.data.data;
        stepName.value = stepDesign.step_name;
        steps.value = stepDesign.stepdetails.map(detail => ({
            id: detail.detail_id,
            type: detail.step_type,
            parameter: detail.parameter,
            current: detail.current,
            voltage: detail.voltage,
            power: detail.power,
            endCondition: detail.end_condition,
            socValue: detail.soc_value,
            timeValue: detail.time_value,
            restTime: detail.rest_time,
            loopCount: detail.loop_count,
            loopStart: detail.loop_start,
            loopEnd: detail.loop_end,
            jumpTo: detail.jump_to,
            parentId: detail.parent_step_id,
            className: detail.class_name,
            result: detail.result,
            endVoltageValue: detail.end_voltage_value, // 新增字段
            endCurrentValue: detail.end_current_value, // 新增字段
            templateID: detail.working_id// 使用每个节点的 workingID
        }));

        // Check if there is an "结束" step
        isEndStepAdded.value = steps.value.some(step => step.type === '结束');
    } catch (error) {
        message.error('加载工步设计失败');
        console.error('Error loading step design:', error);
    }
};

const saveSteps = async () => {
    if (!stepName.value.trim()) {
        message.warning('请先输入工步名称');
        return;
    }

    const duplicateStep = availableSteps.value.find(step => step.step_name === stepName.value);
    if (duplicateStep && duplicateStep.design_id !== selectedStep.value) {
        message.warning('工步名称不能重复');
        return;
    }

    try {
        const stepData = {
            step_name: stepName.value,
            battery_id: batteryInfo.value.battery_id,
            steps: steps.value.map((step, index) => ({
                step_order: index + 1,
                step_type: step.type,
                parameter: step.parameter || '',
                current: step.current || null,
                voltage: step.voltage || null,
                power: step.power || null,
                end_condition: step.endCondition || '',
                soc_value: step.socValue || null,
                time_value: step.timeValue || null,
                rest_time: step.restTime || null,
                loop_count: step.loopCount || null,
                loop_start: step.loopStart || null,
                loop_end: step.loopEnd || null,
                jump_to: step.jumpTo || null,
                parent_step_id: step.parentId || null,
                class_name: step.className || '',
                result: step.result || '',
                end_voltage_value: step.endVoltageValue || null, // 新增字段
                end_current_value: step.endCurrentValue || null, // 新增字段
                working_id: step.templateID || null // 每个节点的 workingID
            }))
        };

        if (selectedStep.value) {
            // Update existing step design
            await updateStepDesign(selectedStep.value, stepData);
        } else {
            // Create new step design
            await createStepDesign(stepData);
        }
        
        message.success('工步设计保存成功');
        
        // Reload steps after save
        const stepRes = await getAllSteps();
        availableSteps.value = stepRes.data.data;
        
        // Reload the saved or updated step
        if (selectedStep.value) {
            await loadStepById(selectedStep.value);
        } else {
            const newStep = availableSteps.value.find(step => step.step_name === stepName.value);
            selectedStep.value = newStep.design_id;
            await loadStepById(selectedStep.value);
        }
    } catch (error) {
        message.error('保存工步设计失败');
        console.error('Error saving step design:', error);
    }
};

const confirmDelete = () => {
    Modal.confirm({
        title: '确认删除',
        content: '您确定要删除该工步设计吗？',
        onOk: async (close) => {
            try {
                await deleteStepDesign(selectedStep.value);
                message.success('工步设计删除成功');
                await clearStepSelection();
                close();
            } catch (error) {
                message.error('删除工步设计失败');
                console.error('Error deleting step design:', error);
                close();
            }
        },
        onCancel: () => {
            message.info('取消删除');
            Modal.destroyAll();
        }
    });
};

const clearStepSelection = async () => {
    stepName.value = '';
    selectedStep.value = '';
    steps.value = [];
    isEndStepAdded.value = false;
    // Reload the available steps
    try {
        const res = await getAllSteps();
        availableSteps.value = res.data.data;
    } catch (error) {
        message.error('刷新工步列表失败');
        console.error('Error refreshing steps:', error);
    }
};


const getStepClass = (type) => {
    switch (type) {
        case '充电':
            return 'charging';
        case '放电':
            return 'discharging';
        case '静置':
            return 'resting';
        case '循环':
            return 'looping';
        case '跳转':
            return 'jumping';
        case '工况':
            return 'working';
        case '结束':
            return 'ending';
        default:
            return '';
    }
};

const exportStepDesignLogs = async () => {
    if (!selectedStep.value) {
        message.warning('请选择一个工步设计来导出');
        return;
    }

    try {
        const response = await downloadStepDesignLogsAsExcel(selectedStep.value);
        // Add response handling for export here
    } catch (error) {
        message.error('导出工步设计日志失败');
        console.error('Error exporting step design logs:', error);
    }
};

// 导入工步设计
const importStepDesign = async (event) => {
    const file = event.target.files[0];
    if (!file) {
        message.warning('请选择一个文件');
        return;
    }

    try {
        const response = await importStepDesigns(file);
        message.success('工步设计导入成功');
        
        // Refresh step list
        const res = await getAllSteps();
        availableSteps.value = res.data.data;
        
        // Load imported step
        const newDesignId = response.createdStepDesigns[0].design_id;
        selectedStep.value = newDesignId;
        await loadStepById(newDesignId);
    } catch (error) {
        message.error('导入工步设计失败');
        console.error('Error importing step design:', error);
    }
};

const loadAvailableTemplates = async () => {
    try {
        const res = await getAllTemplates();
        availableTemplates.value = res.data.data;
    } catch (error) {
        message.error('加载模板失败');
        console.error('Error loading templates:', error);
    }
};

onMounted(async () => {
    try {
        const batteryRes = await getAllBatterySimulations();
        availableBatteries.value = batteryRes.data.data;

        const stepRes = await getAllSteps();
        availableSteps.value = stepRes.data.data;

        await loadAvailableTemplates();
    } catch (error) {
        message.error('初始化失败');
        console.error('Error initializing component:', error);
    }
});
</script>


<style lang="scss">
.wokring {
    .ant-btn {
        background-color: #9966cc;
        border-color: #9966cc;
        color: #fff;
    }
}

.full-modal {
    .ant-modal {
        max-width: 100%;
        top: 0vh;
        padding-bottom: 0;
        margin: 0;
    }

    .ant-modal-content {
        display: flex;
        flex-direction: column;
        // height: calc(50vh);
        // margin-top: -30vh;
        background-color: rgba($color: #04285a, $alpha: 1);
    }

    .ant-modal-body {
        flex: 1;
    }
}
</style>

<style lang="scss" scoped>
.workModal {
    height: 80vh;
    overflow: auto;
}

.right.box {
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.title {
    color: #fff;
    border-left: #2ca6ff 3px solid;
    padding-left: 10px;
    margin-top: 10px;
    margin-left: 10px;
    font-size: 18px;
}

.container {
    width: 98%;
    margin: auto;
    margin-top: 35px;
    color: #fff;
}

.flowBox {
    height: 70vh;
    cursor: pointer;

    .flowBox-row {
        display: flex;
        align-items: center;
        height: 100px;
        width: 99%;
        padding: 0 4%;
        background-color: #000033;
        border: #e5d014 solid 2px;
        border-bottom: none;
        cursor: default;
        position: relative;
        // margin-top: 10px;
        gap: 25px;

        &.sub-node {
            margin-left: 50px;
            width: 90%;
        }

        .flowBox-row-item-delete {
            position: absolute;
            top: 5%;
            right: 3%;
            cursor: pointer;
            width: 20px;
            height: 20px;
        }

        .flowBox-row-item {
            display: flex;
            color: #fdfdfd;
            align-items: center;

            .flowBox-row-item-number {
                font-size: 50px;
                border: 5px solid #25439d;
                border-radius: 50%;
                width: 80px;
                height: 80px;
                text-align: center;
                line-height: 70px;
            }

            p {
                margin-bottom: 0;
            }

            &.charging {
                background-color: #ffcc00; // 充电节点的颜色
            }

            &.discharging {
                background-color: #ff6600; // 放电节点的颜色
            }

            &.resting {
                background-color: #3399ff; // 静置节点的颜色
            }

            &.looping {
                background-color: #33cc33; // 循环节点的颜色
            }

            &.jumping {
                background-color: #cc33ff; // 跳转节点的颜色
            }

            &.working {
                background-color: #9966cc; // 工况节点的颜色
            }

            &.ending {
                background-color: #cc0000; // 结束节点的颜色
            }
        }
    }

    /* 选择最后一个 .flowBox-row 元素并显示它的 border-bottom */
    .flowBox-row:last-child {
        border-bottom: solid 2px #e5d014;
    }

    .remind {
        position: absolute;
        top: 50vh;
        left: 50%;
        transform: translate(-50%, 0%);
        color: #9c9c9c;
        font-size: 22px;
    }
}

.ant-card {
    background-color: #000033;
    border: #058dc1 dashed 2px;
    height: 65vh;
    overflow-y: auto;
    padding-bottom: 35vh;
}

.fade-enter-active,
.fade-leave-active {
    transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
    transform: scale(0);
    opacity: 0;
}
</style>
