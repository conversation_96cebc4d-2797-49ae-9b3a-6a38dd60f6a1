<template>
	<div class="home" style="height: 100%">
		<transition-loading :isShow="loadShow" />
		<div class="chart-list">
			<home-header />
			<div style="padding: 0 8px" class="chart-content">
				<a-row :gutter="[8, 8]" class="chart-content-row">
					<a-col :span="3">
						<div class="left box">
							<div
								:class="['left-li', selectLeftId == leftItem.id ? 'act' : '']"
								v-for="leftItem in leftList"
								:key="leftItem.id"
								@click="selectLeftEvent(leftItem.id)"
							>
								<div class="text">{{ leftItem.table_name }}</div>
							</div>
						</div>
					</a-col>
					<a-col :span="21">
						<div class="right box" v-if="selectLeftId == 1">
							<div class="title">{{ tableTitle }}</div>
							<div class="edit">
								<div class="edit-left">
									<a-input-search v-model:value="searchValue" placeholder="请输入搜索关键词" enter-button />
								</div>
								<div class="edit-right">
									<!-- <div class="edit-right-item" @click="openAddEvent">
										<a-button type="primary">新增</a-button>
									</div> -->
									<div class="edit-right-item">
										<DownloadOutlined style="font-size: 32px; color: #fff" />
									</div>
								</div>
							</div>
							<div class="table">
								<a-table :dataSource="dataSource" :columns="columns" :rowKey="record => record.device_id">
									<template #bodyCell="{ text, record, column }">
										<template v-if="column.key === 'action'">
											<a-space size="middle">
												<!-- <a-button @click="editRecord(record)">编辑</a-button> -->
												<a-popconfirm
													title="确定处理完成这条记录吗?"
													@confirm="() => deleteRecord(record.device_id)"
													okText="是"
													cancelText="否"
												>
													<a-button type="primary">处理完成</a-button>
												</a-popconfirm>
											</a-space>
										</template>
										<template v-else>
											{{ text }}
										</template>
									</template>
								</a-table>
							</div>
						</div>
						<div class="right box" v-else>
							<div class="title">{{ tableTitle }}</div>

							<div style="height: 40%; margin-top: 30px">
								<ModuleItem title="基本控制设参">
									<div class="control-params-card">
										<a-row type="flex" align="middle" justify="start" style="margin-bottom: 16px">
											<a-col :span="15" style="text-align: right; padding-right: 20px">防车距离下限参数:</a-col>
											<a-col :span="21">
												<a-slider v-model:value="value1" :min="0" :max="1" :step="0.01" />
											</a-col>
											<a-col :span="3">
												<a-input-number v-model:value="value1" :min="0" :max="1" :step="0.01" />
											</a-col>
										</a-row>

										<a-row type="flex" align="middle" justify="start" style="margin-bottom: 16px">
											<a-col :span="15" style="text-align: right; padding-right: 20px">电压下平衡参照值:</a-col>
											<a-col :span="21">
												<a-slider v-model:value="value2" :min="0" :max="1" :step="0.01" />
											</a-col>
											<a-col :span="3">
												<a-input-number v-model:value="value2" :min="0" :max="1" :step="0.01" />
											</a-col>
										</a-row>

										<a-row type="flex" align="middle" justify="start" style="margin-bottom: 16px">
											<a-col :span="15" style="text-align: right; padding-right: 20px">电流下平衡参照值:</a-col>
											<a-col :span="21">
												<a-slider v-model:value="value3" :min="0" :max="1" :step="0.01" />
											</a-col>
											<a-col :span="3">
												<a-input-number v-model:value="value3" :min="0" :max="1" :step="0.01" />
											</a-col>
										</a-row>

										<div class="button-wrapper">
											<a-popconfirm
												title="确定要保存修改吗？"
												ok-text="确定"
												cancel-text="取消"
												@confirm="handleConfirm"
											>
												<a-button type="primary">保存</a-button>
											</a-popconfirm>
										</div>
									</div>
								</ModuleItem>
							</div>
							<div style="height: 40%; margin-top: 30px">
								<ModuleItem title="事件控制设参">
									<div class="control-params-card">
										<a-row type="flex" align="middle" justify="start" style="margin-bottom: 16px">
											<a-col :span="15" style="text-align: right; padding-right: 20px">过载事件有功功率触发下限:</a-col>
											<a-col :span="21">
												<a-slider v-model:value="value1" :min="0" :max="1" :step="0.01" />
											</a-col>
											<a-col :span="3">
												<a-input-number v-model:value="value1" :min="0" :max="1" :step="0.01" />
											</a-col>
										</a-row>

										<a-row type="flex" align="middle" justify="start" style="margin-bottom: 16px">
											<a-col :span="15" style="text-align: right; padding-right: 20px">
												失压过压事件电压触发上下限:
											</a-col>
											<a-col :span="21">
												<a-slider range v-model:value="rangeValue" :min="0" :max="1" :step="0.01" />
											</a-col>
											<a-col :span="3">
												<a-input-number v-model:value="rangeValue[0]" :min="0" :max="1" :step="0.01" />
												<span style="margin: 0 8px">-</span>
												<a-input-number v-model:value="rangeValue[1]" :min="0" :max="1" :step="0.01" />
											</a-col>
										</a-row>

										<a-row type="flex" align="middle" justify="start" style="margin-bottom: 16px">
											<a-col :span="15" style="text-align: right; padding-right: 20px">过载事件电流触发上限:</a-col>
											<a-col :span="21">
												<a-slider v-model:value="value3" :min="0" :max="1" :step="0.01" />
											</a-col>
											<a-col :span="3">
												<a-input-number v-model:value="value3" :min="0" :max="1" :step="0.01" />
											</a-col>
										</a-row>

										<div class="button-wrapper">
											<a-popconfirm
												title="确定要保存修改吗？"
												ok-text="确定"
												cancel-text="取消"
												@confirm="handleConfirm"
											>
												<a-button type="primary">保存</a-button>
											</a-popconfirm>
										</div>
									</div>
								</ModuleItem>
							</div>
						</div>
					</a-col>
				</a-row>
				<earth-bg />
			</div>
		</div>
		<a-drawer
			v-model:visible="openAdd"
			class="custom-class"
			root-class-name="root-class-name"
			:root-style="{ color: 'blue' }"
			style="color: red"
			title=""
			placement="right"
			:width="720"
		>
			<a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="设备ID" name="device_id">
							<a-input v-model:value="form.device_id" placeholder="请输入设备ID" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="警报时间" name="time">
							<a-input v-model:value="form.time" placeholder="请输入警报时间" />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="警报类型" name="type">
							<a-input v-model:value="form.type" placeholder="请输入警报类型" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="警报内容" name="content">
							<a-input v-model:value="form.content" placeholder="请输入警报内容" />
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
			<template #extra>
				<a-space>
					<a-button @click="onClose">取消</a-button>
					<a-button type="primary" @click="submitForm">提交</a-button>
				</a-space>
			</template>
		</a-drawer>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import HomeHeader from './components/home-header/index.vue';
import EarthBg from './components/earth-bg/index.vue';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { getTableName, getTableData, addRecord, updateRecord, deleteRecord as deleteRecordAPI } from './service';
import message from 'ant-design-vue/es/message';

const loadShow = ref(false);
const leftList = ref([]);
const selectLeftId = ref<string | number>('');
const dataSource = ref([]);
const openAdd = ref(false);
const tableTitle = ref('警报日志');
const searchValue = ref('');
const formRef = ref(null);
const value1 = ref(0.9);
const value2 = ref(0.88);
const value3 = ref(0.91);
const handleConfirm = () => {
	// 处理保存逻辑
	message.success('保存成功');
};
const rangeValue = ref([0.1, 0.9]); // 初始化范围值

const route = useRoute();
const id = route.query.id;

const fetchTableName = async () => {
	// const res = await getTableName(id as string);
	// leftList.value = res.data.data;
	// if (leftList.value.length > 0) {
	// 	selectLeftEvent(leftList.value[0].id);
	// 	switchTable(leftList.value[0].api_url);
	// }
	leftList.value = [
		{ id: 1, table_name: '警报日志', api_url: '/api/loguser' },
		{ id: 2, table_name: '警报设置', api_url: '/api/role' }
	];
	selectLeftEvent(1);
};

const selectLeftEvent = (id: any) => {
	selectLeftId.value = id;
	tableTitle.value = leftList.value.find((item: any) => item.id === id)?.table_name;
	switchTable(leftList.value.find((item: any) => item.id === id)?.api_url);
};

const switchTable = async (url: string, search?: string) => {
	// const res = await getTableData(url, search);
	// dataSource.value = res.data.data;

	dataSource.value = [
		{ device_id: '001', time: '2024-06-01 08:30:00', type: '电流错误', content: '电流超出正常范围，当前电流: 150A' },
		{ device_id: '002', time: '2024-06-03 12:15:00', type: '电压错误', content: '电压过低，当前电压: 180V' },
		{ device_id: '003', time: '2024-06-05 14:50:00', type: '功率错误', content: '功率超出正常范围，当前功率: 5500W' },
		{ device_id: '004', time: '2024-06-07 16:05:00', type: '电流错误', content: '电流过低，当前电流: 80A' },
		{ device_id: '005', time: '2024-06-09 09:45:00', type: '电压错误', content: '电压超出正常范围，当前电压: 250V' },
		{ device_id: '006', time: '2024-06-11 11:20:00', type: '功率错误', content: '功率过低，当前功率: 2000W' },
		{ device_id: '007', time: '2024-06-13 13:35:00', type: '电流错误', content: '电流超出正常范围，当前电流: 145A' },
		{ device_id: '008', time: '2024-06-15 15:50:00', type: '电压错误', content: '电压过高，当前电压: 240V' }
	];
};

const columns = [
	{
		title: 'ID',
		dataIndex: 'device_id',
		key: 'device_id'
	},
	{
		title: '警报时间',
		dataIndex: 'time',
		key: 'time'
	},
	{
		title: '警报类型',
		dataIndex: 'type',
		key: 'type'
	},
	{
		title: '警报内容',
		dataIndex: 'content',
		key: 'content'
	},
	{
		title: '操作',
		key: 'action'
	}
];

const form = reactive({
	device_id: '',
	time: '',
	type: '',
	content: ''
});

const rules = ref({
	device_id: [{ required: true, message: '请输入设备ID' }],
	time: [{ required: true, message: '请输入警报时间' }],
	type: [{ required: true, message: '请输入警报类型' }],
	content: [{ required: true, message: '请输入警报内容' }]
});

const onClose = () => {
	openAdd.value = false;
};

const submitForm = async () => {
	try {
		await formRef.value.validate();
		if (form.device_id) {
			await updateRecord(form);
		} else {
			await addRecord(form);
		}
		openAdd.value = false;
		switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value).api_url);
	} catch (error) {
		console.error('表单校验失败:', error);
	}
};

const deleteRecord = async (device_id: string) => {
	try {
		await deleteRecordAPI(device_id);
		switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value).api_url);
	} catch (error) {
		console.error('删除记录失败:', error);
	}
};

watch(searchValue, newVal => {
	switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value)?.api_url, newVal);
});
// 页面跳转方法
const router = useRouter();
const handleResize = () => {
	if (window.innerWidth < 480) {
		router.push({ name: 'warningMobile', query: route.query });
	}
};
onMounted(() => {
	fetchTableName();
	handleResize();
	window.addEventListener('resize', handleResize); // 添加这个监听器
});
onBeforeUnmount(() => {
	window.removeEventListener('resize', handleResize); // 移除这个监听器
});
</script>
<style lang="scss" scoped>
.control-params-card {
	//   background-color: #000033;
	// display: flex;
	// flex-direction: column;
	// align-items: center;
	// justify-content: flex-start;
	height: 100%;
	width: 80%;
	margin: auto;
	padding: 20px;
	color: white;
}

.control-params-card :deep(.ant-card-head-title) {
	color: white;
	text-align: center;
}

.control-params-card :deep(.ant-form-item-label > label) {
	color: white;
}

.control-params-card :deep(.ant-slider-rail) {
	background-color: #1e3a8a;
}

.control-params-card :deep(.ant-slider-track) {
	background-color: #3b82f6;
}

.control-params-card :deep(.ant-slider-handle) {
	border-color: #3b82f6;
}

.button-wrapper {
	display: flex;
	justify-content: center;
	margin-top: 20px;
}

.home {
	position: relative;
	width: 100%;
	height: 100%;
	background: url('@/assets/images/index-bg.png') no-repeat;
	background-size: 100% 100%;

	.chart-list {
		height: 100%;
		

		.chart-content {
			height: calc(100% - 77px);
			margin-top: 12px;

			.chart-content-row,
			.chart-content-col {
				height: 100%;
			}

			.chart-container {
				width: 100%;
				height: 100%;
			}

			.container {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				justify-content: space-around;
				margin-top: 20px;

				.li {
					width: 40%;
					height: 100px;
					background-color: #060c20;
					color: white;
					font-size: 25px;
					padding-top: 30px;
					padding-left: 10px;

					// display: flex;
					// align-items: center;
					.sw {
						transform: scale(1.5);
					}
				}
			}

			.virtual-list-content {
				display: flex;
				flex-direction: column;
				height: 98%;
				padding: 0 8px;

				.virtual-list-item {
					display: flex;
					gap: 8px;
					align-items: center;
					padding: 4px;
					color: rgb(255 255 255);
					cursor: pointer;

					&:hover {
						color: #68d8ff;
						background: rgb(255 255 255 / 10%);
					}

					&-col {
						width: 16%;
						overflow: hidden;
						text-align: center;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&-col:nth-child(1) {
						width: 19.5%;
						text-align: left;
					}
				}
			}

			&-left {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-center {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;

					.index-data {
						display: flex;
						flex-direction: column;
						height: 100%;
						margin: 0 16px;
					}
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-right {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item {
					flex: 1;
				}
			}
		}
	}

	.box {
		width: 100%;
		height: 95%;
		background-color: rgba($color: #04285a, $alpha: 0.5);
	}

	.left {
		&-li {
			margin-bottom: 10px;
			background-color: rgb(#5d9dae, 0.1);
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			// padding-left: 30px;
			cursor: pointer;

			.text {
				color: #279abd;
				font-size: 20px;
			}

			&.act {
				background-color: rgb(#01c2ff, 0.1);
				border: 3px #058dc1 solid;

				// background-image: url('/src/assets/images/bg1.png') ;
				// background-repeat: no-repeat;
				// background-size: 100%;
				.text {
					color: #01c2ff;
				}
			}
		}
	}

	.right {
		padding: 10px;

		.title {
			color: #fff;
			border-left: #2ca6ff 3px solid;
			padding-left: 10px;
			margin-top: 10px;
			margin-left: 10px;
			font-size: 18px;
		}

		.edit {
			margin: auto;
			margin-top: 20px;
			margin-bottom: 5px;
			width: 98%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-right {
				display: flex;
				gap: 15px;

				&-item {
					cursor: pointer;
				}
			}
		}
	}
}

// 小屏幕下的样式
@media (max-width: 576px) {
	.home {
		height: unset;
		background: #060c20;

		.chart-content {
			.chart-content-col:first-child {
				height: 1000px !important;
			}

			&-left,
			&-center {
				&-item {
					flex: 1 !important;
				}
			}

			.chart-content-col:nth-child(2) {
				height: 1500px !important;
			}

			.chart-content-col:nth-child(3) {
				height: 1500px !important;
			}
		}
	}
}
</style>

<style lang="scss">
.ant-tooltip-inner {
	min-height: unset;
}

.ant-form-item-label {
	text-align: left;
}

.ant-table-thead > tr > th {
	color: #fff;
	background-color: rgba($color: #2ca6ff, $alpha: 0.5);
}

.ant-table-tbody {
	color: #fff;
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table-tbody > tr.ant-table-row:hover > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.7);
}

.ant-table-tbody > tr.ant-table-row > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table {
	background: none !important;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
