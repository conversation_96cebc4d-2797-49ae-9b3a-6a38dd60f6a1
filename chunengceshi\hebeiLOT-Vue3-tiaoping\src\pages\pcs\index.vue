<template>
  <div class="pcs-container">
    <div class="pcs-header">
      <div class="system-title">PCS监控</div>
      <button class="back-button" @click="goBackToMenu">
        <ArrowLeftOutlined /> 返回菜单
      </button>
      <div class="header-decor">
        <div class="decor-line decor-line-left"></div>
        <div class="decor-line decor-line-right"></div>
      </div>
    </div>
    
    <div class="pcs-content">
      <div class="right-content">
        <div class="nav-arrows">
          <div class="section-title">PCS数据</div>
        </div>
        
        <div class="sub-menu">
          <div class="sub-menu-item" :class="{ active: activeTab === 'params' }" @click="setActiveTab('params')">PCS参数设置</div>
          <div class="sub-menu-item" :class="{ active: activeTab === 'config' }" @click="setActiveTab('config')">PCS配置</div>
          <div class="sub-menu-item" :class="{ active: activeTab === 'debug' }" @click="setActiveTab('debug')">PCS调试</div>
          <div class="sub-menu-item" :class="{ active: activeTab === 'fault' }" @click="setActiveTab('fault')">PCS故障</div>
          <div class="sub-menu-item" :class="{ active: activeTab === 'warning' }" @click="setActiveTab('warning')">PCS告警</div>
        </div>
        
        <div class="data-table">
          <component :is="currentComponent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import PcsDataTable from './components/PcsDataTable.vue';
// 导入其他组件
import PcsConfig from './components/PcsConfig.vue';
import PcsDebug from './components/PcsDebug.vue';
import PcsFault from './components/PcsFault.vue';
import PcsWarning from './components/PcsWarning.vue';

const router = useRouter();
const activeTab = ref('params');

const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const currentComponent = computed(() => {
  switch (activeTab.value) {
    case 'params':
      return PcsDataTable;
    case 'config':
      return PcsConfig;
    case 'debug':
      return PcsDebug;
    case 'fault':
      return PcsFault;
    case 'warning':
      return PcsWarning;
    default:
      return PcsDataTable;
  }
});

const goBackToMenu = () => {
  router.push('/menu');
};
</script>

<style scoped lang="scss">
.pcs-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #000d2c;
  color: white;
  overflow: hidden;
  position: relative;
  
  &::before, &::after {
    content: "";
    position: absolute;
    width: 150px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #00a8ff, transparent);
  }
  
  &::before {
    top: 10px;
    left: 10px;
  }
  
  &::after {
    top: 10px;
    right: 10px;
  }
}

.pcs-header {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #000d2c;
  position: relative;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 100, 200, 0.3);
  
  .header-decor {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    
    .decor-line {
      position: absolute;
      height: 100%;
      width: 150px;
      background: linear-gradient(90deg, transparent, #00a8ff, transparent);
      
      &-left {
        left: 50px;
      }
      
      &-right {
        right: 50px;
      }
    }
  }
  
  .system-title {
    font-size: 24px;
    font-weight: bold;
    padding: 15px;
    color: white;
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 168, 255, 0.7);
    margin-bottom: 15px;
    position: relative;
    
    &::before, &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 80px;
      height: 2px;
      transform: translateY(-50%);
      background: linear-gradient(90deg, transparent, #00a8ff);
    }
    
    &::before {
      left: 25%;
    }
    
    &::after {
      right: 25%;
      transform: translateY(-50%) rotate(180deg);
    }
  }

  .back-button {
    position: absolute;
    top: 15px;
    right: 20px;
    background-color: rgba(0, 50, 100, 0.5);
    border: 1px solid #00a8ff;
    border-radius: 4px;
    color: #00a8ff;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    backdrop-filter: blur(5px);
    box-shadow: 0 0 10px rgba(0, 168, 255, 0.3);

    i {
      margin-right: 5px;
    }

    &:hover {
      background-color: rgba(0, 100, 200, 0.7);
      color: white;
      box-shadow: 0 0 15px rgba(0, 168, 255, 0.7);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.pcs-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  
  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .nav-arrows {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 20px;
      background-color: #001840;
      border-bottom: 1px solid rgba(0, 100, 200, 0.3);
      
      .section-title {
        font-size: 18px;
        color: #00a8ff;
        font-weight: bold;
        position: relative;
        
        &::before, &::after {
          content: "";
          position: absolute;
          top: 50%;
          width: 20px;
          height: 1px;
          background-color: #00a8ff;
        }
        
        &::before {
          left: -30px;
        }
        
        &::after {
          right: -30px;
        }
      }
    }
    
    .sub-menu {
      display: flex;
      border-bottom: 2px solid #003366;
      background-color: #001535;
      
      .sub-menu-item {
        padding: 10px 20px;
        cursor: pointer;
        border-right: 1px solid #003366;
        transition: all 0.3s;
        position: relative;
        
        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0;
          height: 2px;
          background-color: #00a8ff;
          transition: width 0.3s ease;
        }
        
        &:hover {
          background-color: #002255;
          
          &::after {
            width: 100%;
          }
        }
        
        &.active {
          background-color: #0055aa;
          color: white;
          
          &::after {
            width: 100%;
            height: 3px;
            background-color: #00ffff;
          }
        }
      }
    }
    
    .data-table {
      flex: 1;
      overflow: auto;
      padding: 10px;
      background-color: #001535;
    }
  }
}
</style> 