<template>
  <div class="pcs-config">
    <div class="section-header">PCS配置</div>
    <div class="config-content">
      <div class="config-card">
        <div class="card-header">PCS基本配置</div>
        <div class="card-body">
          <div class="config-item">
            <div class="item-label">PCS工作模式:</div>
            <div class="item-value">
              <select v-model="workMode">
                <option value="1">恒功率模式</option>
                <option value="2">恒流模式</option>
                <option value="3">离网模式</option>
              </select>
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">额定功率(kW):</div>
            <div class="item-value">
              <input type="number" v-model="ratedPower" />
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">并网方式:</div>
            <div class="item-value">
              <select v-model="gridMode">
                <option value="1">三相并网</option>
                <option value="2">单相并网</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      <div class="config-card">
        <div class="card-header">保护参数配置</div>
        <div class="card-body">
          <div class="config-item">
            <div class="item-label">过压保护值(V):</div>
            <div class="item-value">
              <input type="number" v-model="overVoltage" />
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">欠压保护值(V):</div>
            <div class="item-value">
              <input type="number" v-model="underVoltage" />
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">过频保护值(Hz):</div>
            <div class="item-value">
              <input type="number" v-model="overFrequency" />
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">欠频保护值(Hz):</div>
            <div class="item-value">
              <input type="number" v-model="underFrequency" />
            </div>
          </div>
        </div>
      </div>
      
      <div class="config-card">
        <div class="card-header">通信配置</div>
        <div class="card-body">
          <div class="config-item">
            <div class="item-label">通信方式:</div>
            <div class="item-value">
              <select v-model="commType">
                <option value="1">RS485</option>
                <option value="2">CAN</option>
                <option value="3">以太网</option>
              </select>
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">设备地址:</div>
            <div class="item-value">
              <input type="number" v-model="deviceAddress" />
            </div>
          </div>
          <div class="config-item">
            <div class="item-label">波特率:</div>
            <div class="item-value">
              <select v-model="baudRate">
                <option value="9600">9600</option>
                <option value="19200">19200</option>
                <option value="38400">38400</option>
                <option value="57600">57600</option>
                <option value="115200">115200</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="action-buttons">
      <button class="save-btn" @click="saveConfig">保存配置</button>
      <button class="reset-btn" @click="resetConfig">恢复默认</button>
    </div>

    <!-- 保存成功提示 -->
    <div class="notification-container" v-if="showSaveSuccess">
      <div class="notification success">
        <div class="notification-icon">✓</div>
        <div class="notification-message">配置已成功保存！</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 配置参数
const workMode = ref('1');
const ratedPower = ref(100);
const gridMode = ref('1');
const overVoltage = ref(264);
const underVoltage = ref(187);
const overFrequency = ref(51.5);
const underFrequency = ref(48.5);
const commType = ref('1');
const deviceAddress = ref(1);
const baudRate = ref('9600');

// 原始配置值（用于重置）
const originalConfig = {
  workMode: '1',
  ratedPower: 100,
  gridMode: '1',
  overVoltage: 264,
  underVoltage: 187,
  overFrequency: 51.5,
  underFrequency: 48.5,
  commType: '1',
  deviceAddress: 1,
  baudRate: '9600'
};

// 保存提示状态
const showSaveSuccess = ref(false);

// 保存配置
const saveConfig = () => {
  // 模拟保存操作
  setTimeout(() => {
    showSaveSuccess.value = true;
    
    // 3秒后隐藏提示
    setTimeout(() => {
      showSaveSuccess.value = false;
    }, 3000);
  }, 500);
};

// 重置配置
const resetConfig = () => {
  workMode.value = originalConfig.workMode;
  ratedPower.value = originalConfig.ratedPower;
  gridMode.value = originalConfig.gridMode;
  overVoltage.value = originalConfig.overVoltage;
  underVoltage.value = originalConfig.underVoltage;
  overFrequency.value = originalConfig.overFrequency;
  underFrequency.value = originalConfig.underFrequency;
  commType.value = originalConfig.commType;
  deviceAddress.value = originalConfig.deviceAddress;
  baudRate.value = originalConfig.baudRate;
};
</script>

<style scoped lang="scss">
.pcs-config {
  width: 100%;
  height: 100%;
  padding: 20px;
  color: #e0e0e0;
  
  .section-header {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #00a8ff;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  }
  
  .config-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .config-card {
      background-color: rgba(0, 40, 80, 0.3);
      border: 1px solid rgba(0, 168, 255, 0.3);
      border-radius: 4px;
      overflow: hidden;
      
      .card-header {
        background-color: rgba(0, 60, 120, 0.5);
        padding: 12px 15px;
        font-weight: bold;
        color: #00a8ff;
        border-bottom: 1px solid rgba(0, 168, 255, 0.3);
      }
      
      .card-body {
        padding: 15px;
        
        .config-item {
          display: flex;
          margin-bottom: 15px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .item-label {
            width: 150px;
            padding-right: 15px;
            display: flex;
            align-items: center;
          }
          
          .item-value {
            flex: 1;
            
            input, select {
              width: 100%;
              padding: 8px 10px;
              background-color: rgba(0, 30, 60, 0.6);
              border: 1px solid rgba(0, 168, 255, 0.3);
              border-radius: 4px;
              color: white;
              
              &:focus {
                outline: none;
                border-color: #00a8ff;
                box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
              }
            }
          }
        }
      }
    }
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    
    button {
      padding: 10px 25px;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
      
      &.save-btn {
        background-color: #0066cc;
        color: white;
        
        &:hover {
          background-color: #0077ee;
          box-shadow: 0 0 10px rgba(0, 168, 255, 0.7);
        }
      }
      
      &.reset-btn {
        background-color: #333;
        color: white;
        
        &:hover {
          background-color: #444;
        }
      }
    }
  }
}

.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  
  .notification {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    animation: slideIn 0.3s ease-out forwards;
    
    &.success {
      background-color: rgba(0, 120, 60, 0.9);
      border-left: 4px solid #00e676;
    }
    
    .notification-icon {
      font-size: 20px;
      margin-right: 12px;
      color: #00e676;
    }
    
    .notification-message {
      font-size: 14px;
      color: white;
    }
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
</style> 