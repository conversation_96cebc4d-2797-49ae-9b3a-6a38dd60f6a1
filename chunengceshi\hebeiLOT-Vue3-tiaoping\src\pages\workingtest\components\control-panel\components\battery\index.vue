<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import { getBMSLogsData } from '../../service.ts';

const chartContainer = ref(null);
let chartInstance = null;
let intervalId = null;

// Prop for starting data fetching
const props = defineProps({
  startFetching: Boolean,
  default: false
});

const initChart = () => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value);

    const option = {
      color: ['#4caf50'], // 柱状图颜色
      textStyle: {
        color: '#fff'
      },
      tooltip: {
        confine: true,
        axisPointer: {
          type: 'shadow'
        },
        className: 'tooltip-review',
        formatter: (params) => {
          let resStr = `<div>${params[0].axisValueLabel}</div>`;
          params.forEach((item) => {
            resStr += `
            <div class="tooltip-item">
              <div class="tooltip-label-icon">
                <span class="tooltip-icon" style="background-color: ${item.color}"></span>
                <span class="tooltip-label">${item.seriesName}：</span>
              </div>
              <span class="tooltip-value">${item.value}%</span>
            </div>
            `;
          });
          return resStr;
        },
        position: function (pos, _params, _dom, _rect, size) {
          let obj = { top: 60 };
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 5;
          return obj;
        },
        trigger: 'axis',
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        top: '20%',
        left: '5%',
        right: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '电池SOC (%)',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          fontSize: 12
        },
        nameTextStyle: {
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: '#fff'
          }
        }
      },
      series: [
        {
          name: '电池SOC',
          type: 'bar',
          barWidth: '60%',
          data: []
        }
      ]
    };

    chartInstance.setOption(option);
  }
};

const updateChart = (data) => {
  const option = {
    xAxis: {
      data: data.xAxisData
    },
    series: [
      {
        data: data.seriesData.map(Number)
      }
    ]
  };
  if (chartInstance) {
    chartInstance.setOption(option);
  }
};

const fetchData = async () => {
  try {
    const res = await getBMSLogsData();
    if (res.data && res.data.data) {
      updateChart(res.data.data);
    }
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

const startFetchingData = () => {
  intervalId = setInterval(fetchData, 5000);
};

const stopFetchingData = () => {
  clearInterval(intervalId);
};

watch(() => props.startFetching, (newVal) => {
  if (newVal) {
    fetchData(); // Fetch data immediately when startFetching is true
    startFetchingData();
  } else {
    stopFetchingData();
  }
});

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  });
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  stopFetchingData();
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  });
});
</script>

<style scoped>
.chart-container {
  height: 300px;
}
</style>
