import { message } from 'ant-design-vue';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import http from './service';

export interface IAxiosResponse<T> extends AxiosResponse {
	status: number;
	data: T;
	message?: string;
	error?: string;
	[key: string]: any;
}
const env = import.meta.env.DEV;

export interface RequestConfig extends AxiosRequestConfig {
	closeErrorMessage?: boolean;
	successStatusCheckValue?: number | 'none';
	header?: Record<string, any>;
}

/**
 * GET 一个示例: GET('/edit', { name: 'libai' }, { baseUrl: 'www.baidu.com' })
 * @param url
 * @param params
 * @param config
 */
export function GET<T, K = any>(url: string, params?: T, config?: AxiosRequestConfig): Promise<IAxiosResponse<K>> {
	return http.get<K>(url, { ...config, params }).catch(error => {
		if (env) {
			console.error('GET接口请求异常。下面是请求参数和异常信息：');
			console.error(url);
			console.error(error);
		}

		return Promise.reject(error);
	});
}

/**
 * POST 一个示例: POST('/edit', { name: 'libai' }, { params: { id: 2 } })
 * @param url
 * @param data
 * @param config
 */
export function POST<T, K = any>(url: string, data?: T, config?: AxiosRequestConfig): Promise<IAxiosResponse<K>> {
	return http.post<K>(url, data, config).catch(error => {
		if (env) {
			console.error('POST接口请求异常。下面是请求参数和异常信息：');
			console.error(url);
			console.error(error);
		}

		if (error.response?.status === 400) {
			message.error(`${error.response?.data?.data || '请求失败，请重试'}`);
		} else {
			message.error(`${(error && (error.error || error.message || error.msg)) || '请求失败，请重试'}`);
		}
		return Promise.reject(error);
	});
}


/**
 * 上传文件到指定的 URL
 * @param url 上传的目标 URL
 * @param file 要上传的文件对象
 * @param config 可选的 axios 配置
 * @returns Promise
 */
export function uploadFile(url: string, file: File, config?: AxiosRequestConfig): Promise<any> {
	const formData = new FormData();
	formData.append('file', file);
  
	const finalConfig: AxiosRequestConfig = {
	  headers: {
		...config?.headers,
		'Content-Type': 'multipart/form-data', // 确保正确的 Content-Type 设置
	  },
	  ...config,
	};
  
	// 使用自定义的 axios 实例发送请求
	return http.post(url, formData, finalConfig).then(response => {
	  message.success('文件上传成功');
	  return response.data; // 根据后端实际返回的结构调整
	}).catch(error => {
	  console.error('文件上传失败:', error);
	  message.error(`文件上传失败: ${error.response?.data?.message || error.message || '请重试'}`);
	  return Promise.reject(error);
	});
  }

/**
 * PUT 一个示例: PUT('/edit', { name: 'libai' }, { params: { id: 2 } })
 * @param url
 * @param data
 * @param config
 */
export function PUT<T, K = any>(url: string, data?: T, config?: AxiosRequestConfig): Promise<IAxiosResponse<K>> {
	return http.put<K>(url, data, config).catch(error => {
		if (env) {
			console.error('PUT接口请求异常。下面是请求参数和异常信息：');
			console.error(url);
			console.error(error);
		}

		if (error.response?.status === 400) {
			message.error(`${error.response?.data?.message || '请求失败，请重试'}`);
		} else {
			message.error(`${(error && (error.error || error.message || error.msg)) || '请求失败，请重试'}`);
		}
		return Promise.reject(error);
	});
}

/**
 * DELETE 一个示例: DELETE('/edit', { params: { id: 2 } })
 * @param url
 * @param config
 */
export function DELETE<T, K = any>(url: string, config?: AxiosRequestConfig): Promise<IAxiosResponse<K>> {
	return http.delete<K>(url, config).catch(error => {
		if (env) {
			console.error('DELETE接口请求异常。下面是请求参数和异常信息：');
			console.error(url);
			console.error(error);
		}

		if (error.response?.status === 400) {
			message.error(`${error.response?.data?.message || '请求失败，请重试'}`);
		} else {
			message.error(`${(error && (error.error || error.message || error.msg)) || '请求失败，请重试'}`);
		}
		return Promise.reject(error);
	});
}


/**
 * DOWNLOAD 一个示例: DOWNLOAD('/log/download', { params: { id: 2 } })
 * @param url
 * @param config
 */
export function DOWNLOAD<T, K = any>(url: string, config?: AxiosRequestConfig): Promise<IAxiosResponse<K>> {
    return http
        .get<K>(url, { ...config, responseType: 'blob' })
        .then(response => {
            // 检查响应内容是否是错误信息
            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const result = JSON.parse(reader.result as string);
                    if (result.code !== undefined && result.message !== undefined) {
                        message.error(result.message);
                        return Promise.reject(result);
                    } else {
                        // 不是错误信息，继续处理下载
                        const url = window.URL.createObjectURL(new Blob([response.data]));
                        const link = document.createElement('a');
                        link.href = url;
                        link.setAttribute('download', 'data.xlsx'); // 文件名可以根据需要更改
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        return response;
                    }
                } catch (e) {
                    // JSON 解析错误，说明不是标准错误信息，继续处理下载
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', 'data.xlsx'); // 文件名可以根据需要更改
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    return response;
                }
            };
            reader.readAsText(new Blob([response.data]));
        })
        .catch(error => {
            if (env) {
                console.error('DOWNLOAD接口请求异常。下面是请求参数和异常信息：');
                console.error(url);
                console.error(error);
            }

            if (error.response?.status === 400) {
                message.error(`${error.response?.data?.message || '请求失败，请重试'}`);
            } else {
                message.error(`${(error && (error.error || error.message || error.msg)) || '请求失败，请重试'}`);
            }

            return Promise.reject(error);
        });
}
