import { GET, POST, PUT, DELETE } from '@/service/api';

// 获取菜单列表
export const getTableName = (id: number | string) => {
	return GET(`/menu/${id}/iot-table`);
};

// 获取表格数据，增加搜索参数
export const getTableData = (url: string, search?: string) => {
	const params = search ? { search } : {};
	return GET(url, { params });
};

// 添加新记录
export const addRecord = (data: any) => {
	return POST('/user', data);
};

// 更新记录
export const updateRecord = (data: any) => {
	return PUT(`/user/${data.user_id}`, data);
};

// 删除记录
export const deleteRecord = (user_id: number) => {
	return DELETE(`/user/${user_id}`);
};
