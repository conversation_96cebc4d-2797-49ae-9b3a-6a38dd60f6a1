import { createApp } from 'vue';
import './style.css';
import 'ant-design-vue/es/message/style/css';
import App from './App.vue';
import router from '@/router/index';
import pinia from './store/index';
import { notification } from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';

const app = createApp(App);
app.use(pinia).use(router).mount('#app');
app.config.globalProperties.$notification = notification;
