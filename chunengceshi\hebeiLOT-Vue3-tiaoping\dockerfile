# 使用官方的 Node.js 运行时镜像
FROM node:18

# 设置工作目录
WORKDIR /app

# 复制 pnpm-lock.yaml 和 package.json 文件到工作目录
COPY pnpm-lock.yaml package.json ./

# 安装 pnpm
RUN npm install -g pnpm

# 安装 Vite 作为独立步骤
RUN pnpm add vite --save-dev

# 安装项目依赖
RUN pnpm install

# 复制所有项目文件到工作目录
COPY . .

# 列出 node_modules 文件夹内容进行调试
RUN ls -l node_modules
RUN ls -l node_modules/vite

# 暴露应用运行的端口
EXPOSE 5173

# 启动应用
CMD ["pnpm", "dev"]
