<template>
	<div class="home-header">
		<div class="home-header-title">综合能源管理平台</div>
		<div class="home-header-time">
			<span class="date-value">{{ date }}</span>
			<span class="time-value">{{ time }}</span>
		</div>
		<!-- <div class="home-header-menu">
			返回主页
		</div> -->
	</div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { onBeforeUnmount, onMounted, ref } from 'vue';

let timer: any = null;
const time = ref<string>('--');
const date = ref<string>('--');

const getTime = () => {
	time.value = dayjs().format('dddd HH:mm:ss');
	date.value = dayjs().format('YYYY/MM/DD');
};

onMounted(() => {
	getTime();
	timer = setInterval(() => {
		getTime();
	}, 1000);
});

onBeforeUnmount(() => {
	clearInterval(timer);
});
</script>

<style lang="scss" scoped>
@media (min-width: 480px) {
	.home-header {
		position: relative;
		height: 65px;
		padding-top: 3px;
		text-align: center;
		background: url('@/assets/images/head_bg.png') no-repeat;
		background-size: 100% 100%;
		&-menu {
			position: absolute;
			right: 0%;
			top: 25px;
			color: #ffffff;
			background-color: rgba($color: #2384dd, $alpha: 0.6);
			width: 150px;
			height: 40px;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			font-size: 18px;
		}

		&-title {
			font-size: 30px;
			font-weight: bold;
			color: #ffffff;

			// text-shadow: 0 0 0.5rem #0a54ea;
		}

		&-time {
			font-size: 25px;
			color: rgb(232, 232, 232);
			display: flex;
			gap: 20px;

			justify-content: flex-end;
			width: 95%;
			.time-value {
			}

			.date-value {
			}
		}
	}
}
@media (max-width: 480px) {
	.home-header {
		position: relative;
		height: 205px;
		padding-top: 3px;
		text-align: center;
		background: url('@/assets/images/head_bg.png') no-repeat;
		background-size: 100% 100%;
		&-menu {
			position: absolute;
			right: 0%;
			top: 25px;
			color: #ffffff;
			background-color: rgba($color: #2384dd, $alpha: 0.6);
			width: 150px;
			height: 40px;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			font-size: 38px;
		}

		&-title {
			font-size: 80px;
			font-weight: bold;
			color: #ffffff;

			// text-shadow: 0 0 0.5rem #0a54ea;
		}

		&-time {
			font-size: 35px;
			color: rgb(232, 232, 232);
			display: flex;
			gap: 20px;

			justify-content: flex-end;
			width: 95%;
			.time-value {
			}

			.date-value {
			}
		}
	}
}

// @media (max-width: 450px) {
// 	.home-header {
// 		height: 80px;
// 		padding-top: 4px;
// 	}
// }
</style>
