import { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'

const pcsRoutes: Array<RouteRecordRaw> = [
  {
    path: '/pcs',
    name: 'PCS',
    component: Layout,
    redirect: '/pcs/index',
    meta: {
      title: 'PCS数据监控',
      icon: 'dashboard',
    },
    children: [
      {
        path: 'index',
        name: 'PCSIndex',
        component: () => import('@/pages/pcs/index.vue'),
        meta: {
          title: 'PCS数据监控',
          icon: 'dashboard',
        },
      },
    ],
  },
]

export default pcsRoutes 