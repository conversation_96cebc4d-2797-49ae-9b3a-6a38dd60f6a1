<template>
  <div class="right box">
    <div class="title">{{ tableTitle }}</div>
    <div style="margin-top: 20px;">
      <a-row :gutter="16">
        <a-col :offset="2" :span="3">
          <a-form-item label="持续时间 (天:时:分)">
            <div style="display: flex; gap: 5px;">
              <a-input-number v-model:value="durationDays" @change="onDurationChange" min="0" placeholder="天" />
              <a-input-number v-model:value="durationHours" @change="onDurationChange" min="0" max="23" placeholder="时" />
              <a-input-number v-model:value="durationMinutes" @change="onDurationChange" min="0" max="59" placeholder="分" />
            </div>
          </a-form-item>
        </a-col>
        <a-col :offset="0">
          <a-form-item label="工况名称">
            <a-input v-model:value="templateName" placeholder="请输入工况名称" />
          </a-form-item>
        </a-col>
        <a-col :offset="0">
          <a-form-item label="加载工况">
            <a-select v-model:value="selectedTemplate" style="width: 200px; display: block;" @change="onTemplateChange">
              <a-select-option v-for="template in templates" :key="template.templateID" :value="template.templateID">{{
                template.templateName }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :offset="0" v-if="selectedTemplate">
          <a-button type="primary" :size="30" @click="exportTemplate">
            <template #icon>
              <ExportOutlined />
            </template>
          </a-button>
        </a-col>
        <a-col :offset="0" v-if="!selectedTemplate">
          <a-upload :before-upload="handleImport" :show-upload-list="false">
            <a-button type="primary" :size="30">
              <template #icon>
                <DownloadOutlined />
              </template>
            </a-button>
          </a-upload>
        </a-col>
        <a-col :offset="0" :span="1">
          <a-form-item label="">
            <a-button type="primary" style="width: 100px;" @click="saveTemplate" :disabled="!canSave">保存</a-button>
          </a-form-item>
        </a-col>
        <a-col :offset="1">
          <a-button v-if="canClear" type="default" danger style="width: 100px;" @click="clearTemplate">清除</a-button>
        </a-col>
        <a-col :offset="0">
          <a-button v-if="selectedTemplate" type="primary" danger style="width: 100px;"
            @click="deleteTemplate">删除</a-button>
        </a-col>
      </a-row>
      <a-row :gutter="16" v-if="!selectedTemplate">
        <a-col :offset="2" :span="4">
          <a-form-item label="选择类型" >
            <a-select v-model:value="selectedType" style="width: 200px;" @change="onTypeChange">
              <a-select-option value="voltage">电压</a-select-option>
              <a-select-option value="current">电流</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <div v-if="selectedType === 'voltage'">
        <h1 class="testTitle title">电压设置</h1>
        <div class="testItem" v-if="!selectedTemplate">
          <a-form-item label="初始电压">
            <a-input-number v-model:value="initialVoltage" @change="onVoltageChange" :min="0" />
          </a-form-item>
        </div>
        <Line :duration="totalMinutes" :initialVoltage="initialVoltage" :type="selectedType" :points="points"
          :draggable="true" @update:points="updatePoints" />
      </div>
      <div v-if="selectedType === 'current'">
        <h1 class="testTitle title">电流设置</h1>
        <div class="testItem" v-if="!selectedTemplate">
          <a-form-item label="初始电流">
            <a-input-number v-model:value="initialCurrent" @change="onCurrentChange" :min="0" />
          </a-form-item>
        </div>
        <Line :duration="totalMinutes" :initialCurrent="initialCurrent" :type="selectedType" :points="points"
          :draggable="true " @update:points="updatePoints" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import Line from './components/Line/index.vue';
import { DeleteOutlined, DownloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
import { getAllTemplates, createTemplateWithConfigurations, updateTemplateWithConfigurations, getTemplateById, deleteTemplateWithConfigurations, importData, exportData } from './service';

const tableTitle = ref('工况设置');
const templateName = ref('');
const initialVoltage = ref(3.0);
const initialCurrent = ref(1.0);
const durationDays = ref(0);
const durationHours = ref(0);
const durationMinutes = ref(0);
const totalMinutes = ref(0);
const selectedType = ref('voltage');
const templates = ref([]);
const selectedTemplate = ref(null);
const points = ref([]);

const isDraggable = computed(() => !selectedTemplate.value);

const getAllTemplatesInit = async () => {
  const res = await getAllTemplates();
  templates.value = res.data.data;
};

const onDurationChange = () => {
  totalMinutes.value = (durationDays.value * 24 * 60) + (durationHours.value * 60) + durationMinutes.value;
  points.value = generateInitialData(totalMinutes.value, selectedType.value === 'voltage' ? initialVoltage.value : initialCurrent.value);
};

const onVoltageChange = (voltage) => {
  initialVoltage.value = voltage;
  points.value = generateInitialData(totalMinutes.value, voltage);
};

const onCurrentChange = (current) => {
  initialCurrent.value = current;
  points.value = generateInitialData(totalMinutes.value, current);
};

const onTypeChange = (type) => {
  selectedType.value = type;
  points.value = generateInitialData(totalMinutes.value, type === 'voltage' ? initialVoltage.value : initialCurrent.value);
};

const generateInitialData = (duration, initialValue) => {
  const data = [];
  for (let i = 0; i <= duration; i++) {
    data.push([i, initialValue.toFixed(2)]);
  }
  return data;
};

const updatePoints = (newPoints) => {
  points.value = newPoints;
};

const saveTemplate = async () => {
  if (!canSave.value) return;

  const templateData = {
    templateName: templateName.value,
    duration: totalMinutes.value,
    type: selectedType.value,
    initialValue: selectedType.value === 'voltage' ? initialVoltage.value : initialCurrent.value,
  };

  const configurationsData = points.value.map(([time, value, id]) => ({
    configurationID: id || null,
    timePoint: time,
    value: value,
  }));

  try {
    if (selectedTemplate.value) {
      await updateTemplateWithConfigurations(selectedTemplate.value, { template: templateData, configurations: configurationsData });
      message.success('模板更新成功');
      onTemplateChange(selectedTemplate.value);  // 回填修改后的内容
    } else {
      const res = await createTemplateWithConfigurations({ template: templateData, configurations: configurationsData });
      message.success('模板创建成功');
      selectedTemplate.value = res.data.data.templateID;  // 假设返回数据中有 templateID
      onTemplateChange(selectedTemplate.value);  // 回填创建后的内容
    }
    await getAllTemplatesInit();
  } catch (error) {
    message.error('操作失败');
  }
};

const onTemplateChange = async (templateID) => {
  let res = await getTemplateById(templateID);
  if (res.data && res.data.code === 1 && res.data.data) {
    const template = res.data.data;
    templateName.value = template.templateName;
    totalMinutes.value = template.duration;
    durationDays.value = Math.floor(totalMinutes.value / (24 * 60));
    durationHours.value = Math.floor((totalMinutes.value % (24 * 60)) / 60);
    durationMinutes.value = totalMinutes.value % 60;
    selectedType.value = template.type;

    if (template.type === 'voltage' || template.type === 'current') {
      const initialValueField = template.type === 'voltage' ? initialVoltage : initialCurrent;
      initialValueField.value = parseFloat(template.initialValue);
    }

    points.value = template.WorkConfigurations.map((config) => [
      parseInt(config.timePoint),
      parseFloat(config.value),
      config.configurationID
    ]);
  }
};

const deleteTemplate = () => {
  if (!selectedTemplate.value) return;
  confirmDelete();
};

const confirmDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: '您确定要删除该工况模板吗？',
    onOk: async (close) => {
      try {
        await deleteTemplateWithConfigurations(selectedTemplate.value);
        message.success('模板删除成功');
        await getAllTemplatesInit();
        clearTemplate();
        close();
      } catch (error) {
        message.error('删除失败');
        console.error('删除模板失败:', error);
        close();
      }
    },
    onCancel: () => {
      message.info('取消删除');
      Modal.destroyAll();
    }
  });
};

const clearTemplate = () => {
  templateName.value = '';
  initialVoltage.value = 3.0;
  initialCurrent.value = 1.0;
  durationDays.value = 0;
  durationHours.value = 0;
  durationMinutes.value = 0;
  totalMinutes.value = 0;
  selectedType.value = 'voltage';
  points.value = [];
  selectedTemplate.value = null;
};

const handleImport = (file) => {
  importData(file)
    .then((res) => {
      message.success('导入成功');
      const importedTemplateID = res.data.data.templateID;  // 假设返回数据中有 templateID
      selectedTemplate.value = importedTemplateID;
      onTemplateChange(importedTemplateID);  // 回填导入后的内容
      getAllTemplatesInit();
    })
    .catch(() => {
      message.error('导入失败');
    });
  return false; // Prevent default upload behavior
};

const exportTemplate = () => {
  if (!selectedTemplate.value) {
    message.error('请选择一个模板进行导出');
    return;
  }
  console.log(selectedTemplate.value)
  exportData(selectedTemplate.value)
    .then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'exported_data.xlsx'); // Change file name if needed
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    })
    .catch(() => {
      message.error('导出失败');
    });
};

const canSave = computed(() => {
  return templateName.value && totalMinutes.value && selectedType.value;
});

const canClear = computed(() => {
  return templateName.value || totalMinutes.value || selectedType.value !== 'voltage' || points.value.length > 0;
});

onMounted(() => {
  getAllTemplatesInit();
});
</script>

<style scoped>
.right.box {
  padding: 16px;
  /* background: #fff; */

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  overflow: auto;
  /* height: 87.5vh; */
}

.testTitle {
  color: #ffffff;
  font-size: 18px;
  width: 98%;
  margin: 0 auto;
  margin-top: 30px;
}

.testItem {
  margin-left: 8%;
  margin-top: 20px;
}

.title {
  color: #fff;
  border-left: #2ca6ff 3px solid;
  padding-left: 10px;
  margin-top: 10px;
  margin-left: 10px;
  font-size: 18px;
}
</style>