import loginRouter from './login';
import HomeRouter from './home';
import MenuRouter from './menu';
import SettingRouter from './setting';
import UserRouter from './user';
import WarrningRouter from './warrning';
import batteryRouter from './battery';
import workingtestRouter from './workingtest';
import pcsRouter from './pcs';
import batteryMonitorRouter from './battery-monitor';
import dispatchForecastRouter from './dispatch-forecast';
import frequencyControlRouter from './frequency-control';

const routes = [
	{
		path: '/',
		redirect: '/login'
	},
	...loginRouter,
	...HomeRouter,
	...MenuRouter,
	...SettingRouter,
	...UserRouter,
	...WarrningRouter,
	...batteryRouter,
	...workingtestRouter,
	...pcsRouter,
	...batteryMonitorRouter,
	...dispatchForecastRouter,
	...frequencyControlRouter
];

export default routes;
