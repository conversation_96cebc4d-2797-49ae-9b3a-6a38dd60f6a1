const Battery = () => import('@/pages/battery/index.vue');
const batteryMobile = () => import('@/pages/battery/mobile.vue');
const BatteryCharging = () => import('@/pages/battery/charging.vue');
const BatteryTesting = () => import('@/pages/battery/testing.vue');

const routes = [
	{
		path: '/battery',
		name: 'battery',
		component: Battery,
		meta: {
			title: '电池管理'
		}
	},
	{
		path: '/battery/charging',
		name: 'batteryCharging',
		component: BatteryCharging,
		meta: {
			title: '充电模式'
		}
	},
	{
		path: '/battery/testing',
		name: 'batteryTesting',
		component: BatteryTesting,
		meta: {
			title: '电池测试'
		}
	},
	{
		path: '/batteryMobile',
		name: 'warningMobile',
		component: batteryMobile,
		meta: {
			title: '电池管理'
		}
	}
];

export default routes;
