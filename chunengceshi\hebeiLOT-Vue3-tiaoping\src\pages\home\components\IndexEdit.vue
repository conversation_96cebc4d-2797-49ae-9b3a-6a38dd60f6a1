<template>
  <div class="index-edit">
    <div class="button-group">
      <div class="edit-button" @click="router.push('/workingtest')">
        <div class="icon-bg">
          <AimOutlined style="fontSize: 1.5rem" />
        </div>
        <div class="text">测试页面</div>
      </div>
      
      <div class="edit-button" @click="router.push('/setting')">
        <div class="icon-bg">
          <SettingOutlined style="fontSize: 1.5rem" />
        </div>
        <div class="text">系统设置</div>
      </div>
      
      <div class="edit-button" @click="router.push('/pcs')">
        <div class="icon-bg">
          <DashboardOutlined style="fontSize: 1.5rem" />
        </div>
        <div class="text">PCS监控</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AimOutlined, SettingOutlined, DashboardOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();
</script>

<style scoped lang="scss">
.index-edit {
  width: 100%;
  
  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .edit-button {
      flex: 1;
      min-width: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      padding: 10px;
      transition: all 0.3s;
      border-radius: 8px;
      
      &:hover {
        background-color: rgba(88, 135, 255, 0.3);
      }
      
      .icon-bg {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(0, 120, 255, 0.2);
        margin-bottom: 10px;
        color: #40a0ff;
      }
      
      .text {
        font-size: 14px;
        color: #e6e6e6;
      }
    }
  }
}
</style> 