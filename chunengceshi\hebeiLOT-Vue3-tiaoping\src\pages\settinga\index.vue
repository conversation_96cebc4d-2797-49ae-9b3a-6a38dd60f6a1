<template>
	<div class="home">
		<transition-loading :isShow="loadShow" />

		<div class="chart-list">
			<home-header />
			<div style="padding: 0 8px" class="chart-content">
				<!-- 整体布局 左 中 右 -->
				<a-row :gutter="[8, 8]" class="chart-content-row">
					<a-col :span="3">
						<div class="left box">
							<div
								:class="['left-li', selectLeftId == leftImet.id ? 'act' : '']"
								v-for="leftImet in leftList"
								:key="leftImet.id"
								@click="selectLeftEvnt(leftImet.id)"
							>
								<div class="text">{{ leftImet.text }}</div>
							</div>
						</div>
					</a-col>
					<a-col :span="21">
						<div class="right box">
							<div class="title">
								{{ tableTitle }}
							</div>
							<div class="edit">
								<div class="edit-left">
									<a-input-search v-model:value="searchValue" placeholder="请输入搜索关键词" enter-button />
								</div>
								<div class="edit-right">
									<div class="edit-right-item" @click="openAddEvnt">
										<a-button type="primary">新增</a-button>
									</div>
									<div class="edit-right-item">
										<DownloadOutlined style="font-size: 32px; color: #fff" />
									</div>
								</div>
							</div>
							<div class="table">
								<a-table :dataSource="dataSource" :columns="columns" />
							</div>
						</div>
					</a-col>
				</a-row>
				<!-- 背景地球 -->
				<earth-bg />
			</div>
		</div>
		<a-drawer
			v-model:visible="openAdd"
			class="custom-class"
			root-class-name="root-class-name"
			:root-style="{ color: 'blue' }"
			style="color: red"
			title="Basic Drawer"
			placement="right"
			:width="720"
		>
			<a-form :model="form" :rules="rules" layout="vertical">
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="Name" name="name">
							<a-input v-model:value="form.name" placeholder="Please enter user name" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="Url" name="url">
							<a-input
								v-model:value="form.url"
								style="width: 100%"
								addon-before="http://"
								addon-after=".com"
								placeholder="please enter url"
							/>
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="Owner" name="owner">
							<a-select v-model:value="form.owner" placeholder="Please a-s an owner">
								<a-select-option value="xiao">Xiaoxiao Fu</a-select-option>
								<a-select-option value="mao">Maomao Zhou</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="Type" name="type">
							<a-select v-model:value="form.type" placeholder="Please choose the type">
								<a-select-option value="private">Private</a-select-option>
								<a-select-option value="public">Public</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="Approver" name="approver">
							<a-select v-model:value="form.approver" placeholder="Please choose the approver">
								<a-select-option value="jack">Jack Ma</a-select-option>
								<a-select-option value="tom">Tom Liu</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="DateTime" name="dateTime">
							<a-date-picker
								v-model:value="form.dateTime"
								style="width: 100%"
								:get-popup-container="trigger => trigger.parentElement"
							/>
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="24">
						<a-form-item label="Description" name="description">
							<a-textarea v-model:value="form.description" :rows="4" placeholder="please enter url description" />
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
			<template #extra>
				<a-space>
					<a-button @click="onClose">Cancel</a-button>
					<a-button type="primary" @click="onClose">Submit</a-button>
				</a-space>
			</template>
		</a-drawer>
	</div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';

import HomeHeader from './components/home-header/index.vue';
import EarthBg from './components/earth-bg/index.vue';
import { DownloadOutlined } from '@ant-design/icons-vue';
const loadShow = ref(false);
const leftList = ref([{}]);
const selectLeftId = ref('1');
leftList.value = [
	{
		id: 1,
		text: '权限控制'
	},
	{
		id: 2,
		text: '日志系统'
	},
	{
		id: 3,
		text: '系统设置'
	},
	{
		id: 4,
		text: '数据设置'
	},
	{
		id: 6,
		text: '菜单设置'
	}
];
const selectLeftEvnt = id => {
	selectLeftId.value = id;
};
const tableTitle = ref('权限控制');
const searchValue = ref('');
const dataSource = [
	{
		key: '1',
		name: '胡彦斌',
		age: '维护组',
		address: 'sagsdqq'
	},
	{
		key: '2',
		name: '胡彦祖',
		age: '维护组',
		address: 'sagsdqq'
	}
];

const columns = [
	{
		title: '姓名',
		dataIndex: 'name',
		key: 'name'
	},
	{
		title: '用户名',
		dataIndex: 'address',
		key: 'address'
	},
	{
		title: '用户角色',
		dataIndex: 'age',
		key: 'age'
	}
];
const openAddEvnt = () => {
	console.log(1);
	openAdd.value = true;
};
const onClose = () => {
	openAdd.value = false;
};
const openAdd = ref(false);
import type { Rule } from 'ant-design-vue/es/form';
const form = reactive({
	name: '',
	url: '',
	owner: '',
	type: '',
	approver: '',
	dateTime: null,
	description: ''
});

const rules: Record<string, Rule[]> = {
	name: [{ required: true, message: 'Please enter user name' }],
	url: [{ required: true, message: 'please enter url' }],
	owner: [{ required: true, message: 'Please select an owner' }],
	type: [{ required: true, message: 'Please choose the type' }],
	approver: [{ required: true, message: 'Please choose the approver' }],
	dateTime: [{ required: true, message: 'Please choose the dateTime', type: 'object' }],
	description: [{ required: true, message: 'Please enter url description' }]
};
</script>
<style lang="scss" scoped>
.home {
	position: relative;
	width: 100%;
	height: 100%;
	background: url('@/assets/images/index-bg.png') no-repeat;
	background-size: 100% 100%;

	.chart-list {
		height: 100%;

		.chart-content {
			height: calc(100% - 77px);
			margin-top: 12px;

			.chart-content-row,
			.chart-content-col {
				height: 100%;
			}

			.chart-container {
				width: 100%;
				height: 100%;
			}

			.container {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				justify-content: space-around;
				margin-top: 20px;

				.li {
					width: 40%;
					height: 100px;
					background-color: #060c20;
					color: white;
					font-size: 25px;
					padding-top: 30px;
					padding-left: 10px;

					// display: flex;
					// align-items: center;
					.sw {
						transform: scale(1.5);
					}
				}
			}

			.virtual-list-content {
				display: flex;
				flex-direction: column;
				height: 98%;
				padding: 0 8px;

				.virtual-list-item {
					display: flex;
					gap: 8px;
					align-items: center;
					padding: 4px;
					color: rgb(255 255 255);
					cursor: pointer;

					&:hover {
						color: #68d8ff;
						background: rgb(255 255 255 / 10%);
					}

					&-col {
						width: 16%;
						overflow: hidden;
						text-align: center;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&-col:nth-child(1) {
						width: 19.5%;
						text-align: left;
					}
				}
			}

			&-left {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-center {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;

					.index-data {
						display: flex;
						flex-direction: column;
						height: 100%;
						margin: 0 16px;
					}
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-right {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item {
					flex: 1;
				}
			}
		}
	}

	.box {
		width: 100%;
		height: 95%;
		background-color: rgba($color: #04285a, $alpha: 0.5);
	}

	.left {
		&-li {
			margin-bottom: 10px;
			background-color: rgb(#5d9dae, 0.1);
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			// padding-left: 30px;
			cursor: pointer;

			.text {
				color: #279abd;
				font-size: 20px;
			}

			&.act {
				background-color: rgb(#01c2ff, 0.1);
				border: 3px #058dc1 solid;

				// background-image: url('/src/assets/images/bg1.png') ;
				// background-repeat: no-repeat;
				// background-size: 100%;
				.text {
					color: #01c2ff;
				}
			}
		}
	}

	.right {
		padding: 10px;

		.title {
			color: #fff;
			border-left: #2ca6ff 3px solid;
			padding-left: 10px;
			margin-top: 10px;
			margin-left: 10px;
			font-size: 18px;
		}

		.edit {
			margin: auto;
			margin-top: 20px;
			margin-bottom: 5px;
			width: 98%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-right {
				display: flex;
				gap: 15px;

				&-item {
					cursor: pointer;
				}
			}
		}
	}
}

// 小屏幕下的样式
@media (max-width: 576px) {
	.home {
		height: unset;
		background: #060c20;

		.chart-content {
			.chart-content-col:first-child {
				height: 1000px !important;
			}

			&-left,
			&-center {
				&-item {
					flex: 1 !important;
				}
			}

			.chart-content-col:nth-child(2) {
				height: 1500px !important;
			}

			.chart-content-col:nth-child(3) {
				height: 1500px !important;
			}
		}
	}
}
</style>

<style lang="scss">
.ant-tooltip-inner {
	min-height: unset;
}

.ant-table-thead > tr > th {
	color: #fff;
	background-color: rgba($color: #2ca6ff, $alpha: 0.5);
}

.ant-table-tbody {
	color: #fff;
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table-tbody > tr.ant-table-row:hover > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.7);
}

.ant-table-tbody > tr.ant-table-row > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table {
	background: none !important;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
