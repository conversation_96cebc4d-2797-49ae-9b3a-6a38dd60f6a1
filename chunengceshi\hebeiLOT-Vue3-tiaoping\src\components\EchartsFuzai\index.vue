<template>
    <div ref="chartContainer" class="chart-container"></div>
  </template>
  
  <script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import * as echarts from 'echarts';
  
  const chartContainer = ref(null);
  let chartInstance = null;
  
  const initChart = () => {
    if (chartContainer.value) {
      chartInstance = echarts.init(chartContainer.value);
  
      const option = {
        color: ['#4284ea'],
        textStyle: {
          color: '#fff'
        },
        tooltip: {
          confine: true,
          axisPointer: {
            lineStyle: {
              width: 2,
              color: '#ffeb7b'
            }
          },
          className: 'tooltip-review',
          formatter: (params) => {
            let resStr = `<div>${params[0].axisValueLabel}</div>`;
            params.forEach((item) => {
              resStr += `
              <div class="tooltip-item">
                <div class="tooltip-label-icon">
                  <span class="tooltip-icon" style="background-color: ${item.color}"></span>
                  <span class="tooltip-label">${item.seriesName}：</span>
                </div>
                <span class="tooltip-value">${item.value}</span>
              </div>
              `;
            });
            return resStr;
          },
          position: function (pos, _params, _dom, _rect, size) {
            let obj = { top: 60 };
            obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 5;
            return obj;
          },
          trigger: 'axis',
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          top: '20%',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            '00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'
          ],
          axisLabel: {
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          interval: 20,
          axisLabel: {
            fontSize: 12
          },
          nameTextStyle: {
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        series: [{
          name: '态势值',
          type: 'line',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 3
          },
          data: [60, 58, 62, 50, 72, 75, 80, 80, 68, 62, 65, 63]
        }]
      };
  
      chartInstance.setOption(option);
    }
  };
  
  const resizeChart = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };
  
  onMounted(() => {
    initChart();
    window.addEventListener('resize', resizeChart);
  });
  
  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose();
    }
    window.removeEventListener('resize', resizeChart);
  });
  </script>
  
  <style scoped>
  .chart-container {
    width: 100%;
    height: 400px;
  }
  </style>
  