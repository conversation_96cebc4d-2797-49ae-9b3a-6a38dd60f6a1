<template>
	<transition-loading :isShow="isLoad" />
	<div class="controlPanel-container right box">
		<div class="title">控制台</div>
		<div class="box">
			<a-row>
				<a-col>
					<div style="display: flex; align-items: center; height: 100%; padding-right: 10px">选择工步:</div>
				</a-col>
				<a-col :span="5">
					<a-select v-model:value="selectedStep" style="width: 300px; display: block" @change="loadStep"
						:disabled="isStartButtonOn">
						<a-select-option v-for="step in availableSteps" :key="step.design_id" :value="step.design_id">
							{{ step.step_name }}
						</a-select-option>
					</a-select>
				</a-col>
				<a-col :span="5">
					<a-button type="default" @click="clearStepSelection" v-if="!isStartButtonOn">清除</a-button>
				</a-col>
				<a-col :span="24">
					<div
						style="margin-top: 20px; margin-bottom: 0px; display: flex; align-items: center; gap: 30px; flex-wrap: wrap">
						<div style="display: flex; align-items: center; gap: 10px">
							<span>电池型号：</span>
							<a-select v-model:value="batteryModel" style="width: 120px" @change="selectBattery">
								<a-select-option v-for="battery in availableBatteries" :key="battery.battery_id"
									:value="battery.model">
									{{ battery.model }}
								</a-select-option>
							</a-select>
						</div>
						<div style="display: flex; align-items: center; gap: 10px">
							<span>额定电压：</span>
							{{ batteryInfo.rated_voltage }}
							<span>V</span>
						</div>
						<div style="display: flex; align-items: center; gap: 10px">
							<span>额定电流：</span>
							{{ batteryInfo.rated_current }}
							<span>A</span>
						</div>
						<div style="display: flex; align-items: center; gap: 10px">
							<span>最大充电电流：</span>
							{{ batteryInfo.max_charge_current }}
							<span>A</span>
						</div>
						<div style="display: flex; align-items: center; gap: 10px">
							<span>最大放电电流：</span>
							{{ batteryInfo.max_discharge_current }}
							<span>A</span>
						</div>
						<div style="display: flex; align-items: center; gap: 10px">
							<span>额定容量：</span>
							{{ batteryInfo.rated_capacity }}
							<span>Ah</span>
						</div>
					</div>
				</a-col>
			</a-row>
			<a-row>
				<a-col :span="18">
					<flow-mini :steps="steps" :highlightedStepId="highlightedStepId" />
				</a-col>
				<a-col :span="5" :offset="1">
					<a-button type="primary" @click="toggleStartButton" v-if="!isStartButtonOn">开始模拟</a-button>
					<a-button type="primary" danger @click="toggleStartButton" v-if="isStartButtonOn">停止模拟</a-button>
					<time-card :hours="hours" :minutes="minutes" :seconds="seconds" :milliseconds="milliseconds" />

				</a-col>
			</a-row>
			<a-row>
				<a-col :span="17">
					<echarts-test :startFetching="isStartButtonOn"></echarts-test>
				</a-col>
				<a-col :span="7">
					<battery :startFetching="isStartButtonOn" />
				</a-col>
			</a-row>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import FlowMini from './components/flow-mini/index.vue';
import TimeCard from './components/time-card/index.vue';
import { getAllBatterySimulations, getAllSteps, getStepDesignById } from '../flow/service';
import { message } from 'ant-design-vue';
import echartsTest from './components/echarts-test/index.vue';
import battery from './components/battery/index.vue';
import { getRunning, createRecord, changeRecordEndTime } from './service';

const steps = ref([]);
const selectedStep = ref('');
const availableSteps = ref([]);
const batteryModel = ref('');
const batteryInfo = ref({});
const availableBatteries = ref([]);
const hours = ref('00');
const minutes = ref('00');
const seconds = ref('00');
const milliseconds = ref('000');
const isStartButtonOn = ref(false);
let timer = null;
let runningInterval = null;
const highlightedStepId = ref(null);
const isLoad = ref(false);

const loadStep = async () => {
    try {
        const res = await getStepDesignById(selectedStep.value);
        const stepDesign = res.data.data;
        steps.value = stepDesign.stepdetails.map(detail => ({
            id: detail.detail_id,
            type: detail.step_type,
            parameter: detail.parameter,
            current: detail.current,
            voltage: detail.voltage,
            power: detail.power,
            endCondition: detail.end_condition,
            socValue: detail.soc_value,
            timeValue: detail.time_value,
            restTime: detail.rest_time,
            loopCount: detail.loop_count,
            loopStart: detail.loop_start,
            loopEnd: detail.loop_end,
            jumpTo: detail.jump_to,
            parentId: detail.parent_step_id,
            className: detail.class_name
        }));
    } catch (error) {
        message.error('加载工步设计失败');
        console.error('Error loading step design:', error);
    }
};

const clearStepSelection = () => {
    selectedStep.value = '';
    steps.value = [];
};

const selectBattery = value => {
    const selectedBattery = availableBatteries.value.find(battery => battery.model === value);
    if (selectedBattery) {
        batteryInfo.value = selectedBattery;
    }
};

const toggleStartButton = async () => {
    if (!selectedStep.value) {
        message.error('请先选择一个工步');
        return;
    }
    isLoad.value = true;
    isStartButtonOn.value = !isStartButtonOn.value;

    if (isStartButtonOn.value) {
        try {
            const res = await createRecord(selectedStep.value);
            if (res.data.code == 1) {
                message.success('模拟开始');
                highlightedStepId.value = parseInt(res.data.data.setup_id);
                startTimer();
            } else {
                message.error('模拟开始失败');
                message.error(res.data.message);
                isStartButtonOn.value = false;
            }
        } catch (error) {
            message.error('模拟开始请求失败');
            console.error('Error starting simulation:', error);
            isStartButtonOn.value = false;
        }
    } else {
        try {
            const res = await changeRecordEndTime('Record ID', new Date().toISOString()); // Replace 'Record ID' with the actual ID
            if (res.data.code == 1) {
                message.success('模拟结束');
                highlightedStepId.value = null;
                stopTimer();
            } else {
                message.error('模拟结束失败');
                isStartButtonOn.value = true;
            }
        } catch (error) {
            message.error('模拟结束请求失败');
            console.error('Error stopping simulation:', error);
            isStartButtonOn.value = true;
        }
    }
    isLoad.value = false;
};

const startTimer = () => {
    timer = setInterval(() => {
        let h = parseInt(hours.value, 10);
        let m = parseInt(minutes.value, 10);
        let s = parseInt(seconds.value, 10);
        let ms = parseInt(milliseconds.value, 10);
        ms += 100;
        if (ms >= 1000) {
            ms = 0;
            s++;
        }
        if (s >= 60) {
            s = 0;
            m++;
        }
        if (m >= 60) {
            m = 0;
            h++;
        }
        hours.value = String(h).padStart(2, '0');
        minutes.value = String(m).padStart(2, '0');
        seconds.value = String(s).padStart(2, '0');
        milliseconds.value = String(ms).padStart(3, '0');
    }, 100); // Increment every 100 milliseconds
};

const stopTimer = () => {
    clearInterval(timer);
};

const getRunningInit = async () => {
    try {
        const res = await getRunning();
        const runningRecord = res.data.data[0];
        if (runningRecord && runningRecord.isRunning) {
            isStartButtonOn.value = true;
            selectedStep.value = runningRecord.design_id;
            await loadStep();

            const durationParts = runningRecord.duration.split(':');
            let [h, m, s] = durationParts;

            h = String(h).padStart(2, '0');
            m = String(m).padStart(2, '0');
            s = String(s).padStart(2, '0');

            let ms = runningRecord.startTime.split('.')[1] || '000';
            ms = ms.substring(0, 3).padEnd(3, '0');

            hours.value = h;
            minutes.value = m;
            seconds.value = s;
            milliseconds.value = ms;

            startTimer();
        } else {
            highlightedStepId.value = null;
            stopTimer(); // Stop the timer if no running record
        }
    } catch (error) {
        message.error('获取运行记录失败');
        console.error('Error getting running records:', error);
    }
};

// Function to update running data every 3 seconds
const updateRunningData = () => {
    runningInterval = setInterval(async () => {
        const res = await getRunning();
        const runningRecord = res.data.data[0];
        if (runningRecord) {
            highlightedStepId.value = parseInt(runningRecord.setup_id);
        } else {
            highlightedStepId.value = null;
        }
    }, 3000);
};

onMounted(async () => {
    try {
        const res = await getAllBatterySimulations();
        availableBatteries.value = res.data;
        const res2 = await getAllSteps();
        availableSteps.value = res2.data.data;
        if (availableBatteries.value.length > 0) {
            batteryModel.value = availableBatteries.value[0].model;
            batteryInfo.value = availableBatteries.value[0];
        }
        await getRunningInit();
        updateRunningData(); // Start the interval for updating running data every 3 seconds
    } catch (error) {
        message.error('初始化数据失败');
        console.error('Error initializing data:', error);
    }
});

onBeforeUnmount(() => {
    clearInterval(runningInterval); // Clear the interval for updating running data
    stopTimer(); // Stop the timer if the component is unmounted
    clearInterval(timer); // This clears the updateRunningData interval
});


</script>




<style lang="scss" scoped>
.controlPanel-container {
	.title {
		color: #fff;
		border-left: #2ca6ff 3px solid;
		padding-left: 10px;
		margin-top: 10px;
		margin-left: 10px;
		font-size: 18px;
		margin-bottom: 3px;
	}

	.ant-row {
		margin-bottom: 20px;
	}

	.box {
		width: 98%;
		margin: auto;
		margin-top: 35px;
		color: #fff;
	}
}
</style>
