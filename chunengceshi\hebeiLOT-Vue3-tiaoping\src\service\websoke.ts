// websocket-client.js
class WebSocketClient {
	constructor(url, onMessageCallback) {
		this.url = url;
		this.socket = null;
		this.onMessageCallback = onMessageCallback;
		this.initialize();
	}

	initialize() {
		this.socket = new WebSocket(this.url);

		this.socket.onopen = () => {
			console.log(`Connected to ${this.url}`);
		};

		this.socket.onmessage = event => {
			console.log('Message from server:', event.data);
			if (this.onMessageCallback) {
				this.onMessageCallback(event.data);
			}
		};

		this.socket.onerror = error => {
			console.error('WebSocket error:', error);
		};

		this.socket.onclose = event => {
			console.log('WebSocket closed:', event);
			// 处理断线重连逻辑
			setTimeout(() => {
				this.initialize();
			}, 5000);
		};
	}

	send(message) {
		if (this.socket && this.socket.readyState === WebSocket.OPEN) {
			this.socket.send(message);
		} else {
			console.warn('WebSocket is not open. Ready state:', this.socket.readyState);
		}
	}

	close() {
		if (this.socket) {
			this.socket.close();
		}
	}
}

// 导出 WebSocketClient 类
export { WebSocketClient };
