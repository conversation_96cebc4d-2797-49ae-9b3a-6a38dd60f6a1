<template>
  <div class="dispatch-forecast-container">
    <div class="dispatch-header">
      <div class="system-title">台区储能调度与负荷预测</div>
      <div class="header-actions">
        <button class="custom-button return-button" @click="returnToMenu">
          <i class="return-icon"></i>返回菜单
        </button>
      </div>
    </div>
    
    <div class="dispatch-content">
      <div class="content-section">
        <!-- 左侧：调度管理部分 -->
        <div class="section-card">
          <div class="card-header">
            <div class="card-title">台区储能调度状态</div>
          </div>
          <div class="card-content">
            <div class="status-row">
              <div class="status-item">
                <div class="status-label">当前调度状态</div>
                <div class="status-value" :class="{'active': isDispatchActive}">
                  {{ isDispatchActive ? '调度中' : '未调度' }}
                </div>
              </div>
              <div class="status-item">
                <div class="status-label">设备连接状态</div>
                <div class="status-value" :class="{'connected': isDeviceConnected}">
                  {{ isDeviceConnected ? '已连接' : '未连接' }}
                </div>
              </div>
            </div>

            <div class="control-buttons">
              <button class="custom-button primary-button" @click="startDispatch" :disabled="isDispatchActive">
                <i class="start-icon"></i>开始调度
              </button>
              <button class="custom-button danger-button" @click="stopDispatch" :disabled="!isDispatchActive">
                <i class="stop-icon"></i>停止调度
              </button>
            </div>

            <div class="param-section">
              <div class="section-subtitle">调度参数设置</div>
              <a-form :model="dispatchForm" layout="vertical">
                <a-form-item label="调度模式">
                  <a-select v-model:value="dispatchForm.mode" placeholder="请选择调度模式">
                    <a-select-option value="auto">自动调度</a-select-option>
                    <a-select-option value="manual">手动调度</a-select-option>
                    <a-select-option value="smart">智能调度</a-select-option>
                  </a-select>
                </a-form-item>
                
                <!-- 新增控制策略部分 -->
                <a-form-item label="控制策略优先级设置">
                  <a-checkbox-group v-model:value="dispatchForm.strategies">
                    <div class="strategy-item">
                      <a-checkbox value="realtime">
                        <div class="strategy-name">优先级1：实时响应模式</div>
                        <div class="strategy-desc">实时监测负载馈线，处理电能质量问题（三相不平衡、功率因数偏低、电压越限等）</div>
                      </a-checkbox>
                    </div>
                    <div class="strategy-item">
                      <a-checkbox value="peakValley">
                        <div class="strategy-name">优先级2：削峰填谷模式</div>
                        <div class="strategy-desc">日前削峰填谷计划执行，在指定时段内按照计划出力</div>
                      </a-checkbox>
                    </div>
                    <div class="strategy-item">
                      <a-checkbox value="standby">
                        <div class="strategy-name">优先级3：待机模式</div>
                        <div class="strategy-desc">不满足上述条件时，系统自动进入待机模式</div>
                      </a-checkbox>
                    </div>
                  </a-checkbox-group>
                </a-form-item>
                
                <a-form-item label="台变负载率阈值 (%)">
                  <a-slider v-model:value="dispatchForm.loadThreshold" :min="60" :max="95" :step="1" />
                  <div class="threshold-info">当前设置: {{ dispatchForm.loadThreshold }}% (超过此值时触发实时响应)</div>
                </a-form-item>
                
                <a-collapse accordion class="strategy-detail-panel">
                  <a-collapse-panel key="1" header="实时响应模式参数">
                    <a-form layout="vertical">
                      <a-form-item label="功率因数下限">
                        <a-input-number v-model:value="strategyParams.realtime.powerFactorMin" :min="0.85" :max="1" :step="0.01" style="width: 100%" />
                      </a-form-item>
                      <a-form-item label="三相不平衡率上限 (%)">
                        <a-input-number v-model:value="strategyParams.realtime.unbalanceMax" :min="5" :max="30" :step="1" style="width: 100%" />
                      </a-form-item>
                      <a-form-item label="电压偏差范围 (%)">
                        <a-range-picker 
                          v-model:value="strategyParams.realtime.voltageRange" 
                          :min="-10" 
                          :max="10" 
                          range-separator="~"
                          style="width: 100%"
                        />
                      </a-form-item>
                    </a-form>
                  </a-collapse-panel>
                  
                  <a-collapse-panel key="2" header="削峰填谷模式参数">
                    <a-form layout="vertical">
                      <a-form-item label="削峰时段设置">
                        <a-time-range-picker v-model:value="strategyParams.peakValley.peakTime" format="HH:mm" style="width: 100%" />
                      </a-form-item>
                      <a-form-item label="填谷时段设置">
                        <a-time-range-picker v-model:value="strategyParams.peakValley.valleyTime" format="HH:mm" style="width: 100%" />
                      </a-form-item>
                      <a-form-item label="削峰功率 (kW)">
                        <a-input-number v-model:value="strategyParams.peakValley.peakPower" :min="0" :step="1" style="width: 100%" />
                      </a-form-item>
                      <a-form-item label="填谷功率 (kW)">
                        <a-input-number v-model:value="strategyParams.peakValley.valleyPower" :min="0" :step="1" style="width: 100%" />
                      </a-form-item>
                    </a-form>
                  </a-collapse-panel>
                </a-collapse>
              </a-form>
            </div>
          </div>
        </div>

        <!-- 设备监控部分 -->
        <div class="section-card">
          <div class="card-header">
            <div class="card-title">设备状态监控</div>
          </div>
          <div class="card-content">
            <a-table :dataSource="deviceList" :columns="deviceColumns" rowKey="id" class="custom-table">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === '在线' ? 'success' : 'error'">
                    {{ record.status }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'battery'">
                  <a-progress :percent="record.battery" :stroke-color="getStrokeColor(record.battery)" />
                </template>
                <template v-if="column.key === 'action'">
                  <button class="custom-button small-button" @click="controlDevice(record)">
                    <i class="control-icon"></i>控制
                  </button>
                  <button class="custom-button small-button primary-button" @click="viewDetail(record)">
                    <i class="detail-icon"></i>详情
                  </button>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>

      <!-- 右侧：预测部分 -->
      <div class="content-section">
        <div class="section-card">
          <div class="card-header">
            <div class="card-title">台区负荷预测</div>
            <div class="timeframe-selector">
              <a-radio-group v-model:value="forecastTimeFrame" size="small" button-style="solid">
                <a-radio-button value="day">日</a-radio-button>
                <a-radio-button value="week">周</a-radio-button>
                <a-radio-button value="month">月</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          <div class="card-content">
            <div class="chart-container" ref="loadForecastChart"></div>
          </div>
        </div>

        <div class="section-card">
          <div class="card-header">
            <div class="card-title">台区用电特性分析</div>
          </div>
          <div class="card-content">
            <div class="chart-row">
              <div class="chart-container" ref="timePatternChart"></div>
              <div class="chart-container" ref="peakValleyChart"></div>
            </div>
            <div class="stats-row">
              <div class="stat-item">
                <div class="stat-title">峰谷差率</div>
                <div class="stat-value">67.8%</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">日均负荷率</div>
                <div class="stat-value">53.2%</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">峰时段</div>
                <div class="stat-value">10:00-14:00, 18:00-22:00</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">谷时段</div>
                <div class="stat-value">00:00-06:00</div>
              </div>
            </div>
          </div>
        </div>

        <div class="section-card">
          <div class="card-header">
            <div class="card-title">调度效益分析</div>
          </div>
          <div class="card-content">
            <div class="benefit-grid">
              <div class="benefit-item">
                <div class="benefit-label">峰谷平衡率</div>
                <div class="benefit-value">76.5%</div>
              </div>
              <div class="benefit-item">
                <div class="benefit-label">经济效益</div>
                <div class="benefit-value">¥ 3,280</div>
              </div>
              <div class="benefit-item">
                <div class="benefit-label">碳减排量</div>
                <div class="benefit-value">875 kg</div>
              </div>
              <div class="benefit-item">
                <div class="benefit-label">台区可靠性提升</div>
                <div class="benefit-value">18.5%</div>
              </div>
            </div>
            <div class="chart-container" ref="benefitTrendChart"></div>
          </div>
        </div>

        <!-- 在右侧添加电能质量监控卡片 -->
        <div class="section-card">
          <div class="card-header">
            <div class="card-title">电能质量实时监控</div>
          </div>
          <div class="card-content">
            <div class="quality-grid">
              <div class="quality-item" :class="{'quality-warning': powerQualityStatus.powerFactor < strategyParams.realtime.powerFactorMin}">
                <div class="quality-icon">
                  <i class="quality-indicator"></i>
                </div>
                <div class="quality-data">
                  <div class="quality-title">功率因数</div>
                  <div class="quality-value">{{ powerQualityStatus.powerFactor }}</div>
                  <div class="quality-limit">下限：{{ strategyParams.realtime.powerFactorMin }}</div>
                </div>
              </div>
              
              <div class="quality-item" :class="{'quality-warning': powerQualityStatus.unbalanceRate > strategyParams.realtime.unbalanceMax}">
                <div class="quality-icon">
                  <i class="quality-indicator"></i>
                </div>
                <div class="quality-data">
                  <div class="quality-title">三相不平衡率</div>
                  <div class="quality-value">{{ powerQualityStatus.unbalanceRate }}%</div>
                  <div class="quality-limit">上限：{{ strategyParams.realtime.unbalanceMax }}%</div>
                </div>
              </div>
              
              <div class="quality-item" :class="{'quality-warning': Math.abs(powerQualityStatus.voltageDeviation) > Math.max(Math.abs(strategyParams.realtime.voltageRange[0]), Math.abs(strategyParams.realtime.voltageRange[1]))}">
                <div class="quality-icon">
                  <i class="quality-indicator"></i>
                </div>
                <div class="quality-data">
                  <div class="quality-title">电压偏差</div>
                  <div class="quality-value">{{ powerQualityStatus.voltageDeviation }}%</div>
                  <div class="quality-limit">范围：{{ strategyParams.realtime.voltageRange[0] }}% ~ {{ strategyParams.realtime.voltageRange[1] }}%</div>
                </div>
              </div>
              
              <div class="quality-item" :class="{'quality-warning': powerQualityStatus.currentLoadRate > dispatchForm.loadThreshold}">
                <div class="quality-icon">
                  <i class="quality-indicator"></i>
                </div>
                <div class="quality-data">
                  <div class="quality-title">台变负载率</div>
                  <div class="quality-value">{{ powerQualityStatus.currentLoadRate }}%</div>
                  <div class="quality-limit">阈值：{{ dispatchForm.loadThreshold }}%</div>
                </div>
              </div>
            </div>
            
            <div class="quality-log">
              <div class="section-subtitle">控制策略执行日志</div>
              <a-list class="log-list" :data-source="strategyLogs" size="small">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <div class="log-item">
                      <span class="log-time">{{ item.time }}</span>
                      <span class="log-type" :class="'log-type-' + item.type">{{ item.type }}</span>
                      <span class="log-message">{{ item.message }}</span>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备控制弹窗 -->
    <a-modal 
      title="设备控制" 
      v-model:visible="deviceControlDialogVisible" 
      :width="500"
      @ok="submitDeviceControl"
    >
      <div class="device-control-dialog">
        <div class="device-info">
          <div class="info-item">
            <span class="label">设备名称:</span>
            <span class="value">{{ currentDevice.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前状态:</span>
            <span class="value">{{ currentDevice.status }}</span>
          </div>
          <div class="info-item">
            <span class="label">电池容量:</span>
            <span class="value">{{ currentDevice.battery }}%</span>
          </div>
        </div>
        <div class="control-panel">
          <a-form layout="vertical">
            <a-form-item label="充放电控制">
              <a-select v-model:value="controlForm.mode" placeholder="请选择控制模式">
                <a-select-option value="charge">充电模式</a-select-option>
                <a-select-option value="discharge">放电模式</a-select-option>
                <a-select-option value="standby">待机模式</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="功率设置 (%)">
              <a-slider v-model:value="controlForm.power" :disabled="controlForm.mode === 'standby'" />
            </a-form-item>
            <a-form-item label="执行时间">
              <a-space>
                <a-time-picker
                  v-model:value="controlForm.startTime"
                  format="HH:mm"
                  placeholder="开始时间"
                />
                <a-time-picker
                  v-model:value="controlForm.endTime"
                  format="HH:mm"
                  placeholder="结束时间"
                />
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { message } from 'ant-design-vue'

const router = useRouter()

// 调度状态
const isDispatchActive = ref(false)
const isDeviceConnected = ref(true)

// 调度表单
const dispatchForm = reactive({
  mode: 'auto',
  cycle: '30',
  priority: 'medium',
  strategies: ['realtime', 'peakValley', 'standby'],
  loadThreshold: 85
})

// 控制策略详细参数
const strategyParams = reactive({
  realtime: {
    powerFactorMin: 0.92,
    unbalanceMax: 15,
    voltageRange: [-5, 5]
  },
  peakValley: {
    peakTime: null,
    valleyTime: null,
    peakPower: 50,
    valleyPower: 40
  }
})

// 设备列表表格列配置
const deviceColumns = [
  {
    title: '设备名称',
    dataIndex: 'name',
    key: 'name',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '电池容量',
    dataIndex: 'battery',
    key: 'battery'
  },
  {
    title: '操作',
    key: 'action'
  }
]

// 设备列表
const deviceList = ref([
  { id: 1, name: '台区储能设备1', status: '在线', battery: 78 },
  { id: 2, name: '台区储能设备2', status: '在线', battery: 65 },
  { id: 3, name: '台区储能设备3', status: '离线', battery: 42 },
  { id: 4, name: '变电站储能设备1', status: '在线', battery: 90 },
  { id: 5, name: '变电站储能设备2', status: '在线', battery: 53 }
])

// 预测时间范围
const forecastTimeFrame = ref('day')

// 图表引用
const loadForecastChart = ref(null)
const timePatternChart = ref(null)
const peakValleyChart = ref(null)
const benefitTrendChart = ref(null)

// 图表实例
let loadForecastChartInstance: any = null
let timePatternChartInstance: any = null
let peakValleyChartInstance: any = null
let benefitTrendChartInstance: any = null

// 设备控制相关
const deviceControlDialogVisible = ref(false)
const currentDevice = reactive({
  id: 0,
  name: '',
  status: '',
  battery: 0
})

const controlForm = reactive({
  mode: 'standby',
  power: 50,
  startTime: null,
  endTime: null
})

// 获取电池进度条颜色
const getStrokeColor = (percent: number) => {
  if (percent < 30) return '#f5222d'
  if (percent < 60) return '#faad14'
  return '#52c41a'
}

// 调度控制功能
const startDispatch = () => {
  isDispatchActive.value = true
  // 调用后端API启动调度
  message.success('调度系统已启动')
}

const stopDispatch = () => {
  isDispatchActive.value = false
  // 调用后端API停止调度
  message.success('调度系统已停止')
}

// 新增：更新电能质量状态面板
const powerQualityStatus = reactive({
  powerFactor: 0.94,
  unbalanceRate: 8.3,
  voltageDeviation: 2.1,
  currentLoadRate: 78
})

// 新增：定时更新电能质量数据
const updatePowerQualityData = () => {
  // 模拟数据波动
  powerQualityStatus.powerFactor = Math.round((0.91 + Math.random() * 0.08) * 100) / 100
  powerQualityStatus.unbalanceRate = Math.round((5 + Math.random() * 10) * 10) / 10
  powerQualityStatus.voltageDeviation = Math.round((Math.random() * 6 - 3) * 10) / 10
  powerQualityStatus.currentLoadRate = Math.round((70 + Math.random() * 20) * 10) / 10
  
  // 判断是否需要触发实时响应
  if (powerQualityStatus.currentLoadRate > dispatchForm.loadThreshold) {
    // 模拟实时响应触发
    if (isDispatchActive.value && dispatchForm.strategies.includes('realtime')) {
      message.info('检测到台变负载率超过阈值，触发实时响应模式')
    }
  }
}

// 保存调度参数增强版
const saveDispatchParams = () => {
  // 调用后端API保存调度参数
  message.success('调度参数保存成功，已更新控制策略优先级')
}

// 设备控制功能
const controlDevice = (device: any) => {
  currentDevice.id = device.id
  currentDevice.name = device.name
  currentDevice.status = device.status
  currentDevice.battery = device.battery
  
  // 重置控制表单
  controlForm.mode = 'standby'
  controlForm.power = 50
  controlForm.startTime = null
  controlForm.endTime = null
  
  deviceControlDialogVisible.value = true
}

const viewDetail = (device: any) => {
  // 查看设备详情
  message.info(`查看设备详情: ${device.name}`)
}

const submitDeviceControl = () => {
  // 提交设备控制指令
  message.success(`已发送控制指令到设备: ${currentDevice.name}`)
  deviceControlDialogVisible.value = false
}

// 初始化用电负荷预测图表
const initLoadForecastChart = () => {
  if (loadForecastChartInstance) {
    loadForecastChartInstance.dispose()
  }
  
  const chartDom = loadForecastChart.value
  if (!chartDom) return
  
  loadForecastChartInstance = echarts.init(chartDom)
  
  // 模拟数据
  const xData = forecastTimeFrame.value === 'day' 
    ? ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00']
    : forecastTimeFrame.value === 'week'
      ? ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      : ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['历史负荷', '预测负荷', '调度优化后'],
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,
      axisLine: {
        lineStyle: {
          color: '#4682B4'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'value',
      name: '负荷 (kW)',
      nameTextStyle: {
        color: '#fff'
      },
      axisLine: {
        lineStyle: {
          color: '#4682B4'
        }
      },
      axisLabel: {
        color: '#fff'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(70, 130, 180, 0.3)'
        }
      }
    },
    series: [
      {
        name: '历史负荷',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210, 120].slice(0, xData.length),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#4682B4'
        },
        itemStyle: {
          color: '#4682B4'
        }
      },
      {
        name: '预测负荷',
        type: 'line',
        data: [130, 142, 111, 144, 100, 240, 220, 130].slice(0, xData.length),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#00BFFF'
        },
        itemStyle: {
          color: '#00BFFF'
        }
      },
      {
        name: '调度优化后',
        type: 'line',
        data: [110, 122, 91, 124, 80, 210, 190, 110].slice(0, xData.length),
        smooth: true,
        lineStyle: {
          width: 3,
          type: 'dashed',
          color: '#7FFFD4'
        },
        itemStyle: {
          color: '#7FFFD4'
        }
      }
    ]
  }
  
  loadForecastChartInstance.setOption(option)
}

// 初始化分时段用电负荷图表
const initTimePatternChart = () => {
  if (timePatternChartInstance) {
    timePatternChartInstance.dispose()
  }
  
  const chartDom = timePatternChart.value
  if (!chartDom) return
  
  timePatternChartInstance = echarts.init(chartDom)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    title: {
      text: '分时段用电负荷',
      textStyle: {
        color: '#fff',
        fontSize: 14
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
      axisLine: {
        lineStyle: {
          color: '#4682B4'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'value',
      name: '负荷 (kW)',
      nameTextStyle: {
        color: '#fff'
      },
      axisLine: {
        lineStyle: {
          color: '#4682B4'
        }
      },
      axisLabel: {
        color: '#fff'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(70, 130, 180, 0.3)'
        }
      }
    },
    series: [
      {
        data: [45, 30, 120, 150, 110, 160],
        type: 'bar',
        name: '平均负荷',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00BFFF' },
            { offset: 1, color: '#4682B4' }
          ])
        }
      }
    ]
  }
  
  timePatternChartInstance.setOption(option)
}

// 初始化峰谷特性分析图表
const initPeakValleyChart = () => {
  if (peakValleyChartInstance) {
    peakValleyChartInstance.dispose()
  }
  
  const chartDom = peakValleyChart.value
  if (!chartDom) return
  
  peakValleyChartInstance = echarts.init(chartDom)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item'
    },
    title: {
      text: '峰谷特性分析',
      textStyle: {
        color: '#fff',
        fontSize: 14
      }
    },
    legend: {
      top: 'bottom',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '负荷分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#000D2C',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
            color: '#fff'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: 35, 
            name: '峰时段', 
            itemStyle: { color: '#00BFFF' } 
          },
          { 
            value: 25, 
            name: '平时段', 
            itemStyle: { color: '#4682B4' } 
          },
          { 
            value: 15, 
            name: '谷时段', 
            itemStyle: { color: '#003366' } 
          },
          { 
            value: 25, 
            name: '尖峰时段', 
            itemStyle: { color: '#7FFFD4' } 
          }
        ]
      }
    ]
  }
  
  peakValleyChartInstance.setOption(option)
}

// 初始化效益趋势图表
const initBenefitTrendChart = () => {
  if (benefitTrendChartInstance) {
    benefitTrendChartInstance.dispose()
  }
  
  const chartDom = benefitTrendChart.value
  if (!chartDom) return
  
  benefitTrendChartInstance = echarts.init(chartDom)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['电费节省', '峰谷差价收益'],
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLine: {
        lineStyle: {
          color: '#4682B4'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: {
      type: 'value',
      name: '金额 (¥)',
      nameTextStyle: {
        color: '#fff'
      },
      axisLine: {
        lineStyle: {
          color: '#4682B4'
        }
      },
      axisLabel: {
        color: '#fff'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(70, 130, 180, 0.3)'
        }
      }
    },
    series: [
      {
        name: '电费节省',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 191, 255, 0.8)' },
            { offset: 1, color: 'rgba(70, 130, 180, 0.2)' }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        lineStyle: {
          width: 2,
          color: '#00BFFF'
        },
        itemStyle: {
          color: '#00BFFF'
        },
        data: [1200, 1320, 1010, 1340, 900, 2300]
      },
      {
        name: '峰谷差价收益',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(127, 255, 212, 0.8)' },
            { offset: 1, color: 'rgba(70, 130, 180, 0.2)' }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        lineStyle: {
          width: 2,
          color: '#7FFFD4'
        },
        itemStyle: {
          color: '#7FFFD4'
        },
        data: [820, 932, 901, 934, 1290, 1330]
      }
    ]
  }
  
  benefitTrendChartInstance.setOption(option)
}

// 监听预测时间范围变化
watch(forecastTimeFrame, () => {
  initLoadForecastChart()
})

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  loadForecastChartInstance?.resize()
  timePatternChartInstance?.resize()
  peakValleyChartInstance?.resize()
  benefitTrendChartInstance?.resize()
}

onMounted(() => {
  initLoadForecastChart()
  initTimePatternChart()
  initPeakValleyChart()
  initBenefitTrendChart()
  
  window.addEventListener('resize', handleResize)
  
  // 每10秒更新一次电能质量数据
  setInterval(updatePowerQualityData, 10000)
  // 初始化更新一次
  updatePowerQualityData()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  loadForecastChartInstance?.dispose()
  timePatternChartInstance?.dispose()
  peakValleyChartInstance?.dispose()
  benefitTrendChartInstance?.dispose()
})

// 添加返回菜单功能
const returnToMenu = () => {
  router.push('/') // 假设首页是菜单页，根据实际路由调整
}
</script>

<style scoped lang="scss">
.dispatch-forecast-container {
  width: 100%;
  height: 100vh;
  background-color: #000D2C;
  color: #fff;
  overflow: auto;
  padding: 20px;
}

.dispatch-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .system-title {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
  }
  
  .header-actions {
    display: flex;
    gap: 15px;
  }
}

.dispatch-content {
  display: flex;
  gap: 20px;
  
  .content-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.section-card {
  background-color: #001A40;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  
  .card-header {
    background-color: #002255;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 16px;
      font-weight: bold;
      color: #fff;
    }
  }
  
  .card-content {
    padding: 15px;
  }
}

.section-subtitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #fff;
  padding-bottom: 5px;
  border-bottom: 1px solid #4682B4;
}

.status-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  
  .status-item {
    background-color: #001533;
    border: 1px solid #4682B4;
    border-radius: 4px;
    padding: 15px;
    flex: 1;
    
    .status-label {
      color: #B0C4DE;
      margin-bottom: 10px;
    }
    
    .status-value {
      font-size: 18px;
      font-weight: bold;
      color: #FF6B6B;
      
      &.active, &.connected {
        color: #4CAF50;
      }
    }
  }
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  
  .custom-button {
    min-width: 120px;
  }
}

.param-section {
  background-color: #001533;
  border: 1px solid #4682B4;
  border-radius: 4px;
  padding: 15px;
  margin-top: 20px;
}

// 覆盖表格样式，确保在深色背景下清晰可见
.custom-table {
  :deep(.ant-table) {
    background: transparent;
  }
  
  :deep(.ant-table-thead > tr > th) {
    background-color: #002255;
    color: #fff;
    border-bottom: 1px solid #4682B4;
  }
  
  :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid #4682B4;
    color: #fff;
    background-color: #001533;
  }
  
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #002866;
  }
  
  :deep(.ant-table-row-expand-icon) {
    color: #fff;
    border-color: #4682B4;
  }
  
  :deep(.ant-empty-description) {
    color: #B0C4DE;
  }
  
  :deep(.ant-pagination-item-link) {
    color: #B0C4DE;
    border-color: #4682B4;
    background-color: transparent;
  }
  
  :deep(.ant-pagination-item) {
    background-color: transparent;
    border-color: #4682B4;
    
    a {
      color: #B0C4DE;
    }
  }
  
  :deep(.ant-pagination-item-active) {
    background-color: #4682B4;
    
    a {
      color: #fff;
    }
  }
}

.chart-container {
  height: 300px;
  margin-bottom: 15px;
}

.chart-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  
  .chart-container {
    flex: 1;
    height: 250px;
  }
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  
  .stat-item {
    background-color: #001533;
    border: 1px solid #4682B4;
    border-radius: 4px;
    padding: 10px;
    
    .stat-title {
      color: #B0C4DE;
      font-size: 14px;
      margin-bottom: 5px;
    }
    
    .stat-value {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

.benefit-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  
  .benefit-item {
    background-color: #001533;
    border: 1px solid #4682B4;
    border-radius: 4px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    .benefit-label {
      color: #B0C4DE;
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .benefit-value {
      color: #00BFFF;
      font-size: 20px;
      font-weight: bold;
    }
  }
}

.device-control-dialog {
  .device-info {
    background-color: #001533;
    border: 1px solid #4682B4;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    
    .info-item {
      margin-bottom: 10px;
      
      .label {
        color: #B0C4DE;
        margin-right: 10px;
      }
      
      .value {
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

// 自定义 Modal 样式，适配深色主题
:deep(.ant-modal-content) {
  background-color: #001A40;
  color: #fff;
}

:deep(.ant-modal-header) {
  background-color: #002255;
  border-bottom: 1px solid #4682B4;
}

:deep(.ant-modal-title) {
  color: #fff;
}

:deep(.ant-modal-close) {
  color: #B0C4DE;
  
  &:hover {
    color: #fff;
  }
}

:deep(.ant-form-item-label > label) {
  color: #B0C4DE;
}

:deep(.ant-select-selector) {
  background-color: #001533 !important;
  border-color: #4682B4 !important;
  color: #fff !important;
}

:deep(.ant-select-arrow) {
  color: #B0C4DE;
}

:deep(.ant-select-selection-item) {
  color: #fff !important;
}

:deep(.ant-slider-rail) {
  background-color: #002255;
}

:deep(.ant-slider-track) {
  background-color: #00BFFF;
}

:deep(.ant-slider-handle) {
  border-color: #00BFFF;
  background-color: #4682B4;
}

:deep(.ant-picker) {
  background-color: #001533;
  border-color: #4682B4;
  
  input {
    color: #fff;
  }
  
  .ant-picker-suffix {
    color: #B0C4DE;
  }
}

:deep(.ant-radio-button-wrapper) {
  background-color: #001533;
  border-color: #4682B4;
  color: #B0C4DE;
  
  &:hover {
    color: #fff;
  }
}

:deep(.ant-radio-button-wrapper-checked) {
  background-color: #4682B4 !important;
  border-color: #00BFFF !important;
  color: #fff !important;
}

:deep(.ant-radio-button-wrapper-checked::before) {
  background-color: #00BFFF !important;
}

// 响应式布局
@media screen and (max-width: 1200px) {
  .dispatch-content {
    flex-direction: column;
  }
  
  .chart-row {
    flex-direction: column;
    
    .chart-container {
      margin-bottom: 20px;
    }
  }
  
  .stats-row,
  .benefit-grid {
    grid-template-columns: 1fr;
  }
}

.strategy-item {
  background-color: #001533;
  border: 1px solid #4682B4;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 10px;
  
  .strategy-name {
    font-weight: bold;
    color: #00BFFF;
    margin-bottom: 5px;
  }
  
  .strategy-desc {
    color: #B0C4DE;
    font-size: 12px;
  }
}

.threshold-info {
  text-align: center;
  color: #B0C4DE;
  margin-top: 5px;
}

.strategy-detail-panel {
  margin-top: 15px;
  background-color: #001533;
  
  :deep(.ant-collapse-header) {
    color: #00BFFF !important;
    background-color: #001533;
    border-bottom: 1px solid #4682B4;
  }
  
  :deep(.ant-collapse-content) {
    background-color: #001533;
    border-top: 1px solid #4682B4;
  }
  
  :deep(.ant-collapse-item) {
    border-bottom: 1px solid #4682B4;
  }
  
  :deep(.ant-collapse-content-box) {
    padding: 15px;
  }
}

:deep(.ant-checkbox-wrapper) {
  color: #fff;
}

:deep(.ant-checkbox-inner) {
  background-color: #001533;
  border-color: #4682B4;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #00BFFF;
  border-color: #00BFFF;
}

:deep(.ant-input-number) {
  background-color: #001533;
  border-color: #4682B4;
  color: #fff;
  
  input {
    color: #fff;
  }
  
  .ant-input-number-handler-wrap {
    background-color: #001533;
    
    .ant-input-number-handler {
      border-color: #4682B4;
      
      .ant-input-number-handler-up-inner,
      .ant-input-number-handler-down-inner {
        color: #B0C4DE;
      }
    }
  }
}

.quality-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.quality-item {
  display: flex;
  align-items: center;
  background-color: #001533;
  border: 1px solid #4682B4;
  border-radius: 4px;
  padding: 15px;
  
  &.quality-warning {
    border-color: #FF6B6B;
    
    .quality-indicator {
      background-color: #FF6B6B;
    }
    
    .quality-value {
      color: #FF6B6B;
    }
  }
}

.quality-icon {
  margin-right: 15px;
  
  .quality-indicator {
    display: block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #4CAF50;
  }
}

.quality-data {
  flex: 1;
  
  .quality-title {
    color: #B0C4DE;
    font-size: 14px;
    margin-bottom: 5px;
  }
  
  .quality-value {
    color: #00BFFF;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .quality-limit {
    color: #B0C4DE;
    font-size: 12px;
  }
}

.quality-log {
  background-color: #001533;
  border: 1px solid #4682B4;
  border-radius: 4px;
  padding: 15px;
}

.log-list {
  max-height: 200px;
  overflow-y: auto;
  
  :deep(.ant-list-item) {
    border-bottom: 1px dashed #4682B4;
    padding: 8px 0;
  }
}

.log-item {
  display: flex;
  align-items: center;
  width: 100%;
  
  .log-time {
    color: #B0C4DE;
    margin-right: 10px;
    font-size: 12px;
    min-width: 70px;
  }
  
  .log-type {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 10px;
    min-width: 60px;
    text-align: center;
    
    &.log-type-realtime {
      background-color: #4682B4;
      color: #fff;
    }
    
    &.log-type-peakValley {
      background-color: #7FFFD4;
      color: #002255;
    }
    
    &.log-type-standby {
      background-color: #B0C4DE;
      color: #002255;
    }
  }
  
  .log-message {
    color: #fff;
    flex: 1;
  }
}

// 自定义按钮样式
.custom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  height: 38px;
  border-radius: 4px;
  border: 1px solid #4682B4;
  background-color: rgba(0, 26, 64, 0.6);
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  
  i {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
  }
  
  &:hover {
    background-color: rgba(70, 130, 180, 0.2);
    box-shadow: 0 0 10px rgba(0, 191, 255, 0.3);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: rgba(0, 26, 64, 0.4);
    
    &:hover {
      box-shadow: none;
    }
  }
  
  &.primary-button {
    background-color: rgba(0, 191, 255, 0.2);
    border-color: #00BFFF;
    
    &:hover {
      background-color: rgba(0, 191, 255, 0.3);
      box-shadow: 0 0 10px rgba(0, 191, 255, 0.5);
    }
  }
  
  &.danger-button {
    background-color: rgba(255, 107, 107, 0.2);
    border-color: #FF6B6B;
    
    &:hover {
      background-color: rgba(255, 107, 107, 0.3);
      box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    }
  }
  
  &.small-button {
    height: 30px;
    padding: 0 10px;
    font-size: 12px;
    
    i {
      width: 14px;
      height: 14px;
    }
  }
  
  &.return-button {
    background-color: rgba(0, 38, 77, 0.6);
    border-color: #B0C4DE;
    
    &:hover {
      background-color: rgba(70, 130, 180, 0.3);
    }
  }
}

// 图标样式
.return-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23B0C4DE"><path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/></svg>');
}

.start-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300BFFF"><path d="M8 5v14l11-7z"/></svg>');
}

.stop-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF6B6B"><path d="M6 6h12v12H6z"/></svg>');
}

.control-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23B0C4DE"><path d="M3 17v2h6v-2H3zM3 5v2h10V5H3zm10 16v-2h8v-2h-8v-2h-2v6h2zM7 9v2H3v2h4v2h2V9H7zm14 4v-2H11v2h10zm-6-4h2V7h4V5h-4V3h-2v6z"/></svg>');
}

.detail-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300BFFF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>');
}
</style>