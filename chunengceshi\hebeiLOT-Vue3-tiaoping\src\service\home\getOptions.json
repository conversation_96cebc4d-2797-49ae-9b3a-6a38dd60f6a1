{"msg": "操作成功", "data": [{"value": 1, "label": "AdguardTeam/AdguardFilters"}, {"value": 2, "label": "airbytehq/airbyte"}, {"value": 3, "label": "alibaba/nacos"}, {"value": 4, "label": "angular/angular"}, {"value": 5, "label": "angular/components"}, {"value": 6, "label": "ankidroid/Anki-Android"}, {"value": 7, "label": "ansible/ansible"}, {"value": 8, "label": "ant-design/ant-design"}, {"value": 9, "label": "apache/airflow"}, {"value": 10, "label": "apache/apisix"}, {"value": 11, "label": "apache/arrow"}, {"value": 12, "label": "apache/beam"}, {"value": 13, "label": "apache/dolphinscheduler"}, {"value": 14, "label": "apache/doris"}, {"value": 17, "label": "apache/iceberg"}, {"value": 18, "label": "apache/pulsar"}, {"value": 19, "label": "apache/shardingsphere"}, {"value": 21, "label": "apache/superset"}, {"value": 22, "label": "apache/tvm"}, {"value": 23, "label": "apple/swift"}, {"value": 24, "label": "appsmithorg/appsmith"}, {"value": 26, "label": "ArduPilot/ardupilot"}, {"value": 27, "label": "argoproj/argo-cd"}, {"value": 29, "label": "Automattic/jetpack"}, {"value": 30, "label": "Automattic/wp-calypso"}, {"value": 31, "label": "aws-amplify/amplify-cli"}, {"value": 32, "label": "aws/aws-cdk"}, {"value": 33, "label": "azerothcore/azerothcore-wotlk"}, {"value": 34, "label": "Azure/azure-cli"}, {"value": 35, "label": "Azure/azure-powershell"}, {"value": 36, "label": "Azure/azure-rest-api-specs"}, {"value": 37, "label": "Azure/azure-sdk-for-java"}, {"value": 38, "label": "Azure/azure-sdk-for-js"}, {"value": 39, "label": "Azure/azure-sdk-for-net"}, {"value": 40, "label": "Azure/azure-sdk-for-python"}, {"value": 41, "label": "backstage/backstage"}, {"value": 42, "label": "bevyengine/bevy"}, {"value": 43, "label": "bioconda/bioconda-recipes"}, {"value": 44, "label": "bitcoin/bitcoin"}, {"value": 45, "label": "bitnami/charts"}, {"value": 48, "label": "ccxt/ccxt"}, {"value": 50, "label": "Chia-Network/chia-blockchain"}, {"value": 51, "label": "cilium/cilium"}, {"value": 52, "label": "CleverRaven/Cataclysm-DDA"}, {"value": 53, "label": "ClickHouse/ClickHouse"}, {"value": 54, "label": "cloudflare/cloudflare-docs"}, {"value": 55, "label": "cms-sw/cmssw"}, {"value": 56, "label": "cockroachdb/cockroach"}, {"value": 57, "label": "conan-io/conan-center-index"}, {"value": 58, "label": "conda-forge/staged-recipes"}, {"value": 59, "label": "containers/podman"}, {"value": 61, "label": "cypress-io/cypress"}, {"value": 62, "label": "darktable-org/darktable"}, {"value": 63, "label": "DataDog/datadog-agent"}, {"value": 65, "label": "DefinitelyTyped/DefinitelyTyped"}, {"value": 66, "label": "demisto/content"}, {"value": 67, "label": "denoland/deno"}, {"value": 68, "label": "department-of-veterans-affairs/va.gov-team"}, {"value": 69, "label": "desktop/desktop"}, {"value": 72, "label": "dotnet/AspNetCore.Docs"}, {"value": 73, "label": "dotnet/aspnetcore"}, {"value": 75, "label": "dotnet/efcore"}, {"value": 77, "label": "dotnet/roslyn"}, {"value": 78, "label": "dotnet/runtime"}, {"value": 80, "label": "elastic/elasticsearch"}, {"value": 81, "label": "elastic/kibana"}, {"value": 82, "label": "electron/electron"}, {"value": 84, "label": "element-plus/element-plus"}, {"value": 85, "label": "elementor/elementor"}, {"value": 86, "label": "envoyproxy/envoy"}, {"value": 87, "label": "espressif/esp-idf"}, {"value": 88, "label": "ethereum/ethereum-org-website"}, {"value": 89, "label": "Expensify/App"}, {"value": 90, "label": "expo/expo"}, {"value": 91, "label": "facebook/react-native"}, {"value": 92, "label": "facebook/react"}, {"value": 94, "label": "files-community/Files"}, {"value": 95, "label": "firebase/firebase-android-sdk"}, {"value": 96, "label": "firebase/flutterfire"}, {"value": 98, "label": "flathub/flathub"}, {"value": 100, "label": "flutter/flutter"}, {"value": 102, "label": "flybywiresim/a32nx"}, {"value": 104, "label": "gatsbyjs/gatsby"}, {"value": 106, "label": "getsentry/sentry"}, {"value": 107, "label": "github/codeql"}, {"value": 110, "label": "go-gitea/gitea"}, {"value": 111, "label": "godotengine/godot"}, {"value": 114, "label": "GoogleChrome/developer.chrome.com"}, {"value": 116, "label": "gradle/gradle"}, {"value": 117, "label": "grafana/grafana"}, {"value": 118, "label": "grafana/loki"}, {"value": 119, "label": "gravitational/teleport"}, {"value": 120, "label": "grpc/grpc"}, {"value": 121, "label": "hashicorp/terraform-provider-aws"}, {"value": 122, "label": "hashicorp/terraform-provider-azurerm"}, {"value": 123, "label": "hashicorp/vault"}, {"value": 124, "label": "helium/denylist"}, {"value": 125, "label": "helix-editor/helix"}, {"value": 126, "label": "home-assistant/core"}, {"value": 127, "label": "home-assistant/frontend"}, {"value": 128, "label": "home-assistant/home-assistant.io"}, {"value": 129, "label": "Homebrew/homebrew-cask"}, {"value": 130, "label": "Homebrew/homebrew-core"}, {"value": 131, "label": "huggingface/transformers"}, {"value": 133, "label": "influxdata/telegraf"}, {"value": 134, "label": "IntelRealSense/librealsense"}, {"value": 135, "label": "istio/istio"}, {"value": 138, "label": "jitsi/jitsi-meet"}, {"value": 140, "label": "joomla/joomla-cms"}, {"value": 141, "label": "JuliaLang/julia"}, {"value": 144, "label": "keycloak/keycloak"}, {"value": 147, "label": "kubernetes/minikube"}, {"value": 148, "label": "kubernetes/test-infra"}, {"value": 149, "label": "kubernetes/website"}, {"value": 150, "label": "kubevirt/kubevirt"}, {"value": 151, "label": "laravel/framework"}, {"value": 152, "label": "leanprover-community/mathlib"}, {"value": 154, "label": "lensapp/lens"}, {"value": 155, "label": "Lightning-AI/lightning"}, {"value": 157, "label": "logseq/logseq"}, {"value": 159, "label": "magento/magento2"}, {"value": 160, "label": "MarlinFirmware/Marlin"}, {"value": 161, "label": "mastodon/mastodon"}, {"value": 162, "label": "matplotlib/matplotlib"}, {"value": 163, "label": "matrix-org/synapse"}, {"value": 165, "label": "mdn/content"}, {"value": 166, "label": "mdn/translated-content"}, {"value": 167, "label": "metabase/metabase"}, {"value": 169, "label": "MetaMask/metamask-extension"}, {"value": 171, "label": "MicrosoftDocs/azure-docs"}, {"value": 172, "label": "MicrosoftDocs/microsoft-365-docs"}, {"value": 173, "label": "MicrosoftDocs/msteams-docs"}, {"value": 174, "label": "microsoftgraph/microsoft-graph-docs"}, {"value": 175, "label": "microsoft/azuredatastudio"}, {"value": 176, "label": "microsoft/fluentui"}, {"value": 177, "label": "microsoft/onnxruntime"}, {"value": 178, "label": "microsoft/playwright"}, {"value": 179, "label": "microsoft/PowerToys"}, {"value": 180, "label": "microsoft/terminal"}, {"value": 181, "label": "microsoft/TypeScript"}, {"value": 182, "label": "microsoft/vcpkg"}, {"value": 183, "label": "microsoft/vscode-jupyter"}, {"value": 184, "label": "microsoft/vscode"}, {"value": 185, "label": "microsoft/winget-pkgs"}, {"value": 187, "label": "mlflow/mlflow"}, {"value": 188, "label": "mozilla-mobile/fenix"}, {"value": 189, "label": "mrdoob/three.js"}, {"value": 190, "label": "mui/material-ui"}, {"value": 191, "label": "mui/mui-x"}, {"value": 192, "label": "neovim/neovim"}, {"value": 193, "label": "newrelic/docs-website"}, {"value": 194, "label": "nextcloud/desktop"}, {"value": 195, "label": "nextcloud/server"}, {"value": 196, "label": "NixOS/nixpkgs"}, {"value": 199, "label": "nrwl/nx"}, {"value": 200, "label": "nuxt/framework"}, {"value": 201, "label": "o3de/o3de"}, {"value": 202, "label": "obsproject/obs-studio"}, {"value": 203, "label": "odoo/odoo"}, {"value": 204, "label": "open-mmlab/mmdetection"}, {"value": 205, "label": "open-telemetry/opentelemetry-collector-contrib"}, {"value": 206, "label": "OpenAPITools/openapi-generator"}, {"value": 207, "label": "opencv/opencv"}, {"value": 208, "label": "openhab/openhab-addons"}, {"value": 211, "label": "openshift/openshift-docs"}, {"value": 212, "label": "openshift/release"}, {"value": 213, "label": "openssl/openssl"}, {"value": 215, "label": "openwrt/openwrt"}, {"value": 216, "label": "oppia/oppia"}, {"value": 217, "label": "PaddlePaddle/Paddle"}, {"value": 218, "label": "PaddlePaddle/PaddleOCR"}, {"value": 219, "label": "pandas-dev/pandas"}, {"value": 220, "label": "php/php-src"}, {"value": 221, "label": "pingcap/tidb"}, {"value": 224, "label": "PowerShell/PowerShell"}, {"value": 225, "label": "ppy/osu"}, {"value": 226, "label": "PrestaShop/PrestaShop"}, {"value": 227, "label": "prisma/prisma"}, {"value": 228, "label": "project-chip/connectedhomeip"}, {"value": 230, "label": "python/cpython"}, {"value": 231, "label": "pytorch/pytorch"}, {"value": 232, "label": "qbittorrent/qBittorrent"}, {"value": 233, "label": "qgis/QGIS"}, {"value": 234, "label": "qmk/qmk_firmware"}, {"value": 235, "label": "quarkusio/quarkus"}, {"value": 236, "label": "rails/rails"}, {"value": 237, "label": "rancher/rancher"}, {"value": 238, "label": "rapid7/metasploit-framework"}, {"value": 239, "label": "ray-project/ray"}, {"value": 240, "label": "raycast/extensions"}, {"value": 243, "label": "remix-run/remix"}, {"value": 244, "label": "renovatebot/renovate"}, {"value": 245, "label": "RocketChat/Rocket.Chat"}, {"value": 246, "label": "RPCS3/rpcs3"}, {"value": 247, "label": "rstudio/rstudio"}, {"value": 248, "label": "ruffle-rs/ruffle"}, {"value": 249, "label": "rust-lang/rust"}, {"value": 250, "label": "scikit-learn/scikit-learn"}, {"value": 251, "label": "scipy/scipy"}, {"value": 253, "label": "SerenityOS/serenity"}, {"value": 256, "label": "solana-labs/solana"}, {"value": 258, "label": "sourcegraph/sourcegraph"}, {"value": 259, "label": "spack/spack"}, {"value": 260, "label": "spyder-ide/spyder"}, {"value": 261, "label": "storybookjs/storybook"}, {"value": 262, "label": "strapi/strapi"}, {"value": 263, "label": "sveltejs/kit"}, {"value": 264, "label": "symfony/symfony"}, {"value": 265, "label": "systemd/systemd"}, {"value": 266, "label": "tachiyomiorg/tachiyomi-extensions"}, {"value": 285, "label": "vectordotdev/vector"}, {"value": 286, "label": "vercel/next.js"}, {"value": 287, "label": "vitejs/vite"}, {"value": 288, "label": "void-linux/void-packages"}, {"value": 291, "label": "woocommerce/woocommerce"}, {"value": 292, "label": "WordPress/gutenberg"}, {"value": 293, "label": "xamarin/xamarin-macios"}, {"value": 294, "label": "xbmc/xbmc"}, {"value": 295, "label": "yt-dlp/yt-dlp"}, {"value": 296, "label": "yuzu-emu/yuzu"}, {"value": 297, "label": "zephyrproject-rtos/zephyr"}, {"value": 299, "label": "ziglang/zig"}, {"value": 300, "label": "zulip/zulip"}], "code": 200}