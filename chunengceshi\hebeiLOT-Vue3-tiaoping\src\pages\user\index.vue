<template>
	<div class="home">
		<transition-loading :isShow="loadShow" />
		<div class="chart-list">
			<home-header />
			<div style="padding: 0 8px" class="chart-content">
				<a-row :gutter="[8, 8]" class="chart-content-row">
					<a-col :span="3">
						<div class="left box">
							<div
								:class="['left-li', selectLeftId == leftItem.id ? 'act' : '']"
								v-for="leftItem in leftList"
								:key="leftItem.id"
								@click="selectLeftEvent(leftItem.id)"
							>
								<div class="text">{{ leftItem.table_name }}</div>
							</div>
						</div>
					</a-col>
					<a-col :span="21">
						<div class="right box">
							<div class="title">{{ tableTitle }}</div>
							<div class="edit">
								<div class="edit-left">
									<a-input-search v-model:value="searchValue" placeholder="请输入搜索关键词" enter-button />
								</div>
								<div class="edit-right">
									<div class="edit-right-item" @click="openAddEvent">
										<a-button type="primary">新增</a-button>
									</div>
									<div class="edit-right-item">
										<DownloadOutlined style="font-size: 32px; color: #fff" />
									</div>
								</div>
							</div>
							<div class="table">
								<a-table :dataSource="dataSource" :columns="columns" :rowKey="record => record.user_id">
									<template #bodyCell="{ text, record, column }">
										<template v-if="column.key === 'role'">
											{{ record.role === '0' ? '管理员' : '普通用户' }}
										</template>
										<template v-else-if="column.key === 'action'">
											<a-space size="middle">
												<a-button @click="editRecord(record)">编辑</a-button>
												<a-popconfirm
													title="确定删除这条记录吗?"
													@confirm="() => deleteRecord(record.user_id)"
													okText="是"
													cancelText="否"
												>
													<a-button type="danger">删除</a-button>
												</a-popconfirm>
											</a-space>
										</template>
										<template v-else>
											{{ text }}
										</template>
									</template>
								</a-table>
							</div>
						</div>
					</a-col>
				</a-row>
				<earth-bg />
			</div>
		</div>
		<a-drawer
			v-model:visible="openAdd"
			class="custom-class"
			root-class-name="root-class-name"
			:root-style="{ color: 'blue' }"
			style="color: red"
			title=""
			placement="right"
			:width="720"
		>
			<a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="用户名" name="username">
							<a-input v-model:value="form.username" placeholder="请输入用户名" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="全名" name="full_name">
							<a-input v-model:value="form.full_name" placeholder="请输入全名" />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="密码" name="password">
							<a-input v-model:value="form.password" type="password" placeholder="请输入密码" />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="角色" name="role">
							<a-select v-model:value="form.role" placeholder="请选择角色">
								<a-select-option value="0">普通用户</a-select-option>
								<a-select-option value="1">管理员</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
			<template #extra>
				<a-space>
					<a-button @click="onClose">取消</a-button>
					<a-button type="primary" @click="submitForm">提交</a-button>
				</a-space>
			</template>
		</a-drawer>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import HomeHeader from './components/home-header/index.vue';
import EarthBg from './components/earth-bg/index.vue';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { getTableName, getTableData, addRecord, updateRecord, deleteRecord as deleteRecordAPI } from './service';

const loadShow = ref(false);
const leftList = ref([]);
const selectLeftId = ref<string | number>('');
const dataSource = ref([]);
const openAdd = ref(false);
const tableTitle = ref('权限控制');
const searchValue = ref('');
const formRef = ref(null);

const route = useRoute();
const id = route.query.id;

const fetchTableName = async () => {
	const res = await getTableName(id as string);
	leftList.value = res.data.data;
	if (leftList.value.length > 0) {
		selectLeftEvent(leftList.value[0].id);
		switchTable(leftList.value[0].api_url);
	}
};

const selectLeftEvent = (id: any) => {
	selectLeftId.value = id;
	tableTitle.value = leftList.value.find((item: any) => item.id === id)?.table_name;
	switchTable(leftList.value.find((item: any) => item.id === id)?.api_url);
};

const switchTable = async (url: string, search?: string) => {
	const res = await getTableData(url, search);
	dataSource.value = res.data.data;
};

const columns = [
	{
		title: '用户名',
		dataIndex: 'username',
		key: 'username'
	},
	{
		title: '姓名',
		dataIndex: 'full_name',
		key: 'full_name'
	},
	{
		title: '用户角色',
		dataIndex: 'role',
		key: 'role'
	},
	{
		title: '操作',
		key: 'action'
	}
];

const form = reactive({
	user_id: null,
	username: '',
	full_name: '',
	password: '',
	role: null
});

const rules = ref({
	username: [{ required: true, message: '请输入用户名' }],
	full_name: [{ required: true, message: '请输入全名' }],
	password: [
		{ required: true, message: '请输入密码' },
		{ min: 6, message: '密码长度必须大于6个字符' }
	],
	role: [{ required: true, message: '请选择角色' }]
});

const openAddEvent = () => {
	form.user_id = null;
	form.username = '';
	form.full_name = '';
	form.password = '';
	form.role = null;
	rules.value = {
		username: [{ required: true, message: '请输入用户名' }],
		full_name: [{ required: true, message: '请输入全名' }],
		password: [
			{ required: true, message: '请输入密码' },
			{ min: 6, message: '密码长度必须大于6个字符' }
		],
		role: [{ required: true, message: '请选择角色' }]
	};
	openAdd.value = true;
};

const editRecord = (record: any) => {
	form.user_id = record.user_id;
	form.username = record.username;
	form.full_name = record.full_name;
	form.password = ''; // 编辑时不显示密码
	form.role = record.role;
	rules.value = {
		username: [{ required: true, message: '请输入用户名' }],
		full_name: [{ required: true, message: '请输入全名' }],
		password: [{ required: false }],
		role: [{ required: true, message: '请选择角色' }]
	};
	openAdd.value = true;
};

const onClose = () => {
	openAdd.value = false;
};

const submitForm = async () => {
	try {
		await formRef.value.validate();
		if (form.user_id) {
			await updateRecord(form);
		} else {
			const { user_id, ...addForm } = form; // 删除 user_id 字段
			await addRecord(addForm);
		}
		openAdd.value = false;
		switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value).api_url);
	} catch (error) {
		console.error('表单校验失败:', error);
	}
};

const deleteRecord = async (user_id: number) => {
	try {
		await deleteRecordAPI(user_id);
		switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value).api_url);
	} catch (error) {
		console.error('删除记录失败:', error);
	}
};

watch(searchValue, newVal => {
	switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value)?.api_url, newVal);
});
// 页面跳转方法
const router = useRouter();
const handleResize = () => {
	if (window.innerWidth < 480) {
		router.push({ name: 'userMobile', query: route.query });
	}
};
onMounted(() => {
	fetchTableName();
	handleResize();
	window.addEventListener('resize', handleResize); // 添加这个监听器
});
onBeforeUnmount(() => {
	window.removeEventListener('resize', handleResize); // 移除这个监听器
});
</script>

<style lang="scss" scoped>
.home {
	position: relative;
	width: 100%;
	height: 100%;
	background: url('@/assets/images/index-bg.png') no-repeat;
	background-size: 100% 100%;

	.chart-list {
		height: 100%;

		.chart-content {
			height: calc(100% - 77px);
			margin-top: 12px;

			.chart-content-row,
			.chart-content-col {
				height: 100%;
			}

			.chart-container {
				width: 100%;
				height: 100%;
			}

			.container {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				justify-content: space-around;
				margin-top: 20px;

				.li {
					width: 40%;
					height: 100px;
					background-color: #060c20;
					color: white;
					font-size: 25px;
					padding-top: 30px;
					padding-left: 10px;

					// display: flex;
					// align-items: center;
					.sw {
						transform: scale(1.5);
					}
				}
			}

			.virtual-list-content {
				display: flex;
				flex-direction: column;
				height: 98%;
				padding: 0 8px;

				.virtual-list-item {
					display: flex;
					gap: 8px;
					align-items: center;
					padding: 4px;
					color: rgb(255 255 255);
					cursor: pointer;

					&:hover {
						color: #68d8ff;
						background: rgb(255 255 255 / 10%);
					}

					&-col {
						width: 16%;
						overflow: hidden;
						text-align: center;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&-col:nth-child(1) {
						width: 19.5%;
						text-align: left;
					}
				}
			}

			&-left {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-center {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item:nth-child(1) {
					flex: 2;

					.index-data {
						display: flex;
						flex-direction: column;
						height: 100%;
						margin: 0 16px;
					}
				}

				&-item:nth-child(2) {
					flex: 1;
				}
			}

			&-right {
				flex-direction: column;
				row-gap: 8px !important;
				height: 100%;

				&-item {
					flex: 1;
				}
			}
		}
	}

	.box {
		width: 100%;
		height: 95%;
		background-color: rgba($color: #04285a, $alpha: 0.5);
	}

	.left {
		&-li {
			margin-bottom: 10px;
			background-color: rgb(#5d9dae, 0.1);
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			// padding-left: 30px;
			cursor: pointer;

			.text {
				color: #279abd;
				font-size: 20px;
			}

			&.act {
				background-color: rgb(#01c2ff, 0.1);
				border: 3px #058dc1 solid;

				// background-image: url('/src/assets/images/bg1.png') ;
				// background-repeat: no-repeat;
				// background-size: 100%;
				.text {
					color: #01c2ff;
				}
			}
		}
	}

	.right {
		padding: 10px;

		.title {
			color: #fff;
			border-left: #2ca6ff 3px solid;
			padding-left: 10px;
			margin-top: 10px;
			margin-left: 10px;
			font-size: 18px;
		}

		.edit {
			margin: auto;
			margin-top: 20px;
			margin-bottom: 5px;
			width: 98%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-right {
				display: flex;
				gap: 15px;

				&-item {
					cursor: pointer;
				}
			}
		}
	}
}

// 小屏幕下的样式
@media (max-width: 576px) {
	.home {
		height: unset;
		background: #060c20;

		.chart-content {
			.chart-content-col:first-child {
				height: 1000px !important;
			}

			&-left,
			&-center {
				&-item {
					flex: 1 !important;
				}
			}

			.chart-content-col:nth-child(2) {
				height: 1500px !important;
			}

			.chart-content-col:nth-child(3) {
				height: 1500px !important;
			}
		}
	}
}
</style>

<style lang="scss">
.ant-tooltip-inner {
	min-height: unset;
}

.ant-table-thead > tr > th {
	color: #fff;
	background-color: rgba($color: #2ca6ff, $alpha: 0.5);
}

.ant-table-tbody {
	color: #fff;
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table-tbody > tr.ant-table-row:hover > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.7);
}

.ant-table-tbody > tr.ant-table-row > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table {
	background: none !important;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
