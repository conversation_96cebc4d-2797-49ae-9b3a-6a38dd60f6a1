import { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'

const dispatchForecastRoutes: Array<RouteRecordRaw> = [
  {
    path: '/dispatch-forecast',
    name: 'DispatchForecast',
    component: Layout,
    redirect: '/dispatch-forecast/index',
    meta: {
      title: '调度与预测',
      icon: 'chart',
    },
    children: [
      {
        path: 'index',
        name: 'DispatchForecastIndex',
        component: () => import('@/pages/dispatch-forecast/index.vue'),
        meta: {
          title: '调度与预测',
          icon: 'chart',
        },
      },
    ],
  },
]

export default dispatchForecastRoutes 