/* 兼容火狐浏览器 */
* {
	scrollbar-color: #88abda #fff;
	scrollbar-width: thin;
  }
  
  /* 全局滚动条样式 */
  ::-webkit-scrollbar {
	width: 15px;
  }
  
  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
	background: rgb(239, 239, 239);
	border-radius: 2px;
  }
  
  /* 小滑块 */
  ::-webkit-scrollbar-thumb {
	background: #40a0ff49;
	border-radius: 2px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
	background: #208bf6;
  }
  
  /* @font-face {
	font-family: 'ali';
	src: url('/src/assets/font/AlimamaFangYuanTiVF/AlimamaFangYuanTiVF-Thin.ttf');
  } */
  
  :root {
	/* font-family: 'ali'; */
	--module-bg: rgb(4 32 103 / 50%);
	--module-bg-hover: rgb(88 135 255 / 50%);
  }
  
  html,
  body {
	min-width: 375px;
	min-height: 650px;
	padding: 0;
	margin: 0;
  }
  
  #app {
	width: 100%;
	height: 100%;
	font-family: Avenir, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
  }
  
  /* 以前注释掉的样式 */
  /* 滚动条的宽度 */
  /* ::-webkit-scrollbar {
	width: 6px;
	height: 6px;
	padding-right: 2px;
	background-color: transparent;
  } */
  
  /* 外层轨道 */
  /* ::-webkit-scrollbar-track {
	width: 5px;
	border-radius: 50px;
  } */
  
  /* 滚动条的设置 */
  /* ::-webkit-scrollbar-thumb {
	background: #eeeeee;
	border: 0;
	border-radius: 10px;
  } */
  
  /* 滚动条移上去的背景 */
  /* ::-webkit-scrollbar-thumb:hover {
	background: #eeeeee;
  } */
  