<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import { getPCSLogsByIdForECharts } from '../../service.ts';

const chartContainer = ref(null);
let chartInstance = null;
let intervalId = null;

// Define the prop that will be passed from the parent
const props = defineProps({
  startFetching: {
    type: Boolean,
    default: false
  },
  recordId:{
    type:Number
  }
});

const initChart = () => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value);

    const option = {
      color: ['#4284ea', '#f24c4c'], // 两个系列的颜色
      textStyle: {
        color: '#fff'
      },
      tooltip: {
        confine: true,
        axisPointer: {
          lineStyle: {
            width: 2,
            color: '#ffeb7b'
          }
        },
        className: 'tooltip-review',
        formatter: (params) => {
          let resStr = `<div>${params[0].axisValueLabel}</div>`;
          params.forEach((item) => {
            resStr += `
            <div class="tooltip-item">
              <div class="tooltip-label-icon">
                <span class="tooltip-icon" style="background-color: ${item.color}"></span>
                <span class="tooltip-label">${item.seriesName}：</span>
              </div>
              <span class="tooltip-value">${item.value}${item.seriesName === '电压' ? 'V' : 'A'}</span>
            </div>
            `;
          });
          return resStr;
        },
        position: function (pos, _params, _dom, _rect, size) {
          let obj = { top: 60 };
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 5;
          return obj;
        },
        trigger: 'axis',
        textStyle: {
          fontSize: 12
        }
      },
      grid: {
        top: '20%',
        left: '5%',
        right: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '电压 (V)',
          min: 200,
          max: 240,
          interval: 10,
          axisLabel: {
            fontSize: 12
          },
          nameTextStyle: {
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        {
          type: 'value',
          name: '电流 (A)',
          min: 0,
          max: 20,
          interval: 5,
          axisLabel: {
            fontSize: 12
          },
          nameTextStyle: {
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        }
      ],
      series: [
        {
          name: '电压',
          type: 'line',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 3
          },
          yAxisIndex: 0,
          data: []
        },
        {
          name: '电流',
          type: 'line',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 3
          },
          yAxisIndex: 1,
          data: []
        }
      ]
    };

    chartInstance.setOption(option);
  }
};

const updateChart = (data) => {
  if (chartInstance) {
    chartInstance.setOption({
      xAxis: {
        data: data.time
      },
      series: [
        {
          name: '电压',
          data: data.voltage
        },
        {
          name: '电流',
          data: data.current
        }
      ]
    });
  }
};

const fetchDataAndUpdateChart = async () => {
  try {
    let response = await getPCSLogsByIdForECharts(recordId);
    response = response.data.data;
    console.log(response);
    updateChart(response);
  } catch (error) {
    console.error('Failed to fetch PCS logs data:', error);
  }
};

const startFetchingData = () => {
  fetchDataAndUpdateChart();
  intervalId = setInterval(fetchDataAndUpdateChart, 3000); // 每3秒钟获取一次数据
};

const stopFetchingData = () => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener('resize', resizeChart);
  stopFetchingData();
});

// Watch for changes in the prop
watch(() => props.startFetching, (newVal) => {
  if (newVal) {
    startFetchingData();
  } else {
    stopFetchingData();
  }
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 300px;
}
</style>
