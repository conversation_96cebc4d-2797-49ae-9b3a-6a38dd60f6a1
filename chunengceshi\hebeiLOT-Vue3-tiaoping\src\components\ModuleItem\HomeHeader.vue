<template>
  <div class="home-header">
    <div class="home-header-title">电池管理</div>
    <div class="home-header-menu" @click="toPage('/menu')">返回主页</div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();
function toPage(url: string) {
  router.push(url);
}
</script>

<style lang="scss" scoped>
.home-header {
  position: relative;
  height: 65px;
  padding-top: 3px;
  text-align: center;
  background: linear-gradient(135deg, #0a54ea 0%, #0d47a1 100%);
  background-size: 100% 100%;
  &-menu {
    position: absolute;
    right: 0%;
    top: 25px;
    color: #ffffff;
    background-color: rgba($color: #2384dd, $alpha: 0.6);
    width: 150px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 18px;
  }

  &-title {
    font-size: 26px;
    font-weight: bold;
    color: #ffffff;
  }
}

@media (max-width: 600px) {
  .home-header {
    height: 70px;
  }
}

@media (max-width: 450px) {
  .home-header {
    height: 80px;
    padding-top: 4px;
  }
}
</style>