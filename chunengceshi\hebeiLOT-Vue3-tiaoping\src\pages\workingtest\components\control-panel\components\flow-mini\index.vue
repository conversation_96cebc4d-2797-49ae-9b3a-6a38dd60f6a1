<template>
	<a-card class="flowBox">
		<transition-group name="fade" tag="div">
			<div
				v-for="(step, index) in steps"
				:key="step.id"
				class="flowBox-row"
				:class="{ highlight: index === highlightedStepId }"
			>
				<div class="flowBox-row-item" v-if="step.result !== ''">
					<p>{{ step.result }}</p>
				</div>
				<div class="flowBox-row-item">
					<p class="flowBox-row-item-number">{{ index + 1 }}</p>
				</div>
				<div class="flowBox-row-item">
					<p>步骤类型：</p>
					<span>{{ step.type }}</span>
				</div>
				<div class="flowBox-row-item" v-if="step.type === '充电' || step.type === '放电'">
					<p>参数：</p>
					<span>{{ step.parameter }}</span>
				</div>
				<div
					class="flowBox-row-item"
					v-if="(step.type === '充电' || step.type === '放电') && step.parameter === '恒流'"
				>
					<p>电流：</p>
					<span>{{ step.current }} A</span>
				</div>
				<div
					class="flowBox-row-item"
					v-if="(step.type === '充电' || step.type === '放电') && step.parameter === '恒流'"
				>
					<p>电压：</p>
					<span>{{ step.voltage }} V</span>
				</div>
				<div class="flowBox-row-item" v-if="step.parameter === '恒功率'">
					<p>功率：</p>
					<span>{{ step.power }} W</span>
				</div>
				<div class="flowBox-row-item" v-if="step.type === '充电' || step.type === '放电'">
					<p>结束条件：</p>
					<span>{{ step.endCondition }}</span>
				</div>
				<div class="flowBox-row-item" v-if="step.endCondition === 'SOC状态'">
					<p>SOC数值：</p>
					<span>{{ step.socValue }} %</span>
				</div>
				<div class="flowBox-row-item" v-if="step.endCondition === '时间'">
					<p>时间：</p>
					<span>{{ step.timeValue }} 分钟</span>
				</div>
				<div class="flowBox-row-item" v-if="step.type === '静置'">
					<p>静置时间：</p>
					<span>{{ step.restTime }} 分钟</span>
				</div>
				<div class="flowBox-row-item" v-if="step.type === '循环'">
					<p>循环次数：</p>
					<span>{{ step.loopCount }}</span>
					<p>从步骤：</p>
					<span>{{ step.loopStart }}</span>
					<p>到步骤：</p>
					<span>{{ step.loopEnd }}</span>
				</div>
				<div class="flowBox-row-item" v-if="step.type === '跳转'">
					<p>跳转到步骤：</p>
					<span>{{ step.jumpTo }}</span>
				</div>
			</div>
		</transition-group>
	</a-card>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';

const props = defineProps({
	steps: {
		type: Array,
		required: true
	},
	highlightedStepId: {
		type: Number,
		default: null
	}
});
</script>

<style lang="scss" scoped>
.flowBox {
	// height: 70vh;
	// cursor: pointer;

	.flowBox-row {
		display: flex;
		align-items: center;
		height: 100px;
		width: 99%;
		padding: 0 4%;
		background-color: #000033;
		border: #e5d014 solid 2px;
		border-bottom: none;
		cursor: default;
		position: relative;
		gap: 25px;
		transition: all 0.3s ease;
		overflow: hidden;
		border-radius: 10px;

		&.sub-node {
			margin-left: 50px;
			width: 90%;
		}

		&.highlight {
			// border-color: transparent;
			border: rgba(0, 183, 255, 0.7) solid 2px;
			&::before {
				content: '';
				position: absolute;
				top: -50%;
				left: -50%;
				width: 200%;
				height: 200%;
				background-image: linear-gradient(180deg, rgba(0, 183, 255, 0.7), rgba(47, 0, 255, 0.7));
				animation: rotBGimg 1s ease-in-out infinite;
				transition: all 0.2s linear;
			}

			&::after {
				content: '';
				position: absolute;
				background: #000033;
				inset: 5px;
				border-radius: 5px;
			}
		}

		.flowBox-row-item {
			display: flex;
			color: #fff;
			align-items: center;
			z-index: 1;

			.flowBox-row-item-number {
				font-size: 50px;
				border: 5px solid #25439d;
				border-radius: 50%;
				width: 80px;
				height: 80px;
				text-align: center;
				line-height: 70px;
			}
			p {
				margin-bottom: 0;
			}
		}
	}

	.flowBox-row:last-child {
		border-bottom: solid 2px #e5d014;
	}

	.remind {
		position: absolute;
		top: 50vh;
		left: 50%;
		transform: translate(-50%, 0%);
		color: #9c9c9c;
		font-size: 22px;
	}
}

.ant-card {
	background-color: #000033;
	border: #058dc1 dashed 2px;
	height: 30vh;
	overflow-y: auto;
	padding-bottom: 35vh;
}

.fade-enter-active,
.fade-leave-active {
	transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
	transform: scale(0);
	opacity: 0;
}

@keyframes rotBGimg {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(180deg);
	}
}
</style>

<!-- <template>
  <a-card class="flowBox">
    <transition-group name="fade" tag="div">
      <div v-for="(step, index) in steps" :key="step.id" class="flowBox-row" :class="{
        'highlight': step.id === highlightedStepId,
        'charging': step.type === '充电',
        'discharging': step.type === '放电',
        'resting': step.type === '静置',
        'looping': step.type === '循环',
        'jumping': step.type === '跳转'
      }">
        <div class="flowBox-row-item" v-if="step.result !== ''">
          <p>{{ step.result }}</p>
        </div>
        <div class="flowBox-row-item">
          <p class="flowBox-row-item-number">{{ index + 1 }}</p>
        </div>
        <div class="flowBox-row-item">
          <p>步骤类型：</p>
          <span>{{ step.type }}</span>
        </div>
        <div class="flowBox-row-item" v-if="step.type === '充电' || step.type === '放电'">
          <p>参数：</p>
          <span>{{ step.parameter }}</span>
        </div>
        <div class="flowBox-row-item" v-if="(step.type === '充电' || step.type === '放电') && step.parameter === '恒流'">
          <p>电流：</p>
          <span>{{ step.current }} A</span>
        </div>
        <div class="flowBox-row-item" v-if="(step.type === '充电' || step.type === '放电') && step.parameter === '恒流'">
          <p>电压：</p>
          <span>{{ step.voltage }} V</span>
        </div>
        <div class="flowBox-row-item" v-if="step.parameter === '恒功率'">
          <p>功率：</p>
          <span>{{ step.power }} W</span>
        </div>
        <div class="flowBox-row-item" v-if="step.type === '充电' || step.type === '放电'">
          <p>结束条件：</p>
          <span>{{ step.endCondition }}</span>
        </div>
        <div class="flowBox-row-item" v-if="step.endCondition === 'SOC状态'">
          <p>SOC数值：</p>
          <span>{{ step.socValue }} %</span>
        </div>
        <div class="flowBox-row-item" v-if="step.endCondition === '时间'">
          <p>时间：</p>
          <span>{{ step.timeValue }} 分钟</span>
        </div>
        <div class="flowBox-row-item" v-if="step.type === '静置'">
          <p>静置时间：</p>
          <span>{{ step.restTime }} 分钟</span>
        </div>
        <div class="flowBox-row-item" v-if="step.type === '循环'">
          <p>循环次数：</p>
          <span>{{ step.loopCount }}</span>
          <p>从步骤：</p>
          <span>{{ step.loopStart }}</span>
          <p>到步骤：</p>
          <span>{{ step.loopEnd }}</span>
        </div>
        <div class="flowBox-row-item" v-if="step.type === '跳转'">
          <p>跳转到步骤：</p>
          <span>{{ step.jumpTo }}</span>
        </div>
      </div>
    </transition-group>
  </a-card>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';

const props = defineProps({
  steps: {
    type: Array,
    required: true
  },
  highlightedStepId: {
    type: Number,
    default: null
  }
});
</script>

<style lang="scss" scoped>
.flowBox {
  // height: 70vh;
  // cursor: pointer;

  .flowBox-row {
    display: flex;
    align-items: center;
    height: 100px;
    width: 99%;
    padding: 0 4%;
    background-color: #000033;
    border: #e5d014 solid 2px;
    border-bottom: none;
    cursor: default;
    position: relative;
    gap: 25px;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 10px;

    &.sub-node {
      margin-left: 50px;
      width: 90%;
    }

    &.highlight {
      border: rgba(0, 183, 255, 0.7) solid 2px;

      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background-image: linear-gradient(180deg, rgba(0, 183, 255, 0.7), rgba(47, 0, 255, 0.7));
        animation: rotBGimg 1s ease-in-out infinite;
        transition: all 0.2s linear;
      }

      &::after {
        content: '';
        position: absolute;
        background: #000033;
        inset: 5px;
        border-radius: 5px;
      }
    }

    &.charging {
      background-color: #ffcc00;
    }

    &.discharging {
      background-color: #ff6600;
    }

    &.resting {
      background-color: #66ccff;
    }

    &.looping {
      background-color: #cc66ff;
    }

    &.jumping {
      background-color: #66ff66;
    }

    .flowBox-row-item {
      display: flex;
      color: #fff;
      align-items: center;
      z-index: 1;

      .flowBox-row-item-number {
        font-size: 50px;
        border: 5px solid #25439d;
        border-radius: 50%;
        width: 80px;
        height: 80px;
        text-align: center;
        line-height: 70px;
      }

      p {
        margin-bottom: 0;
      }
    }
  }

  .flowBox-row:last-child {
    border-bottom: solid 2px #e5d014;
  }

  .remind {
    position: absolute;
    top: 50vh;
    left: 50%;
    transform: translate(-50%, 0%);
    color: #9c9c9c;
    font-size: 22px;
  }
}

.ant-card {
  background-color: #000033;
  border: #058dc1 dashed 2px;
  height: 30vh;
  overflow-y: auto;
  padding-bottom: 35vh;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  transform: scale(0);
  opacity: 0;
}

@keyframes rotBGimg {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(180deg);
  }
}
</style> -->
