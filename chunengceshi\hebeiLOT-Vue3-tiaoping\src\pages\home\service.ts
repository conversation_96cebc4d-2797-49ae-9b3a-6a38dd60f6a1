import { GET } from '@/service/api';

export const getOptions = () => {
	return GET('/home/<USER>');
};

export const getInit = () => {
	return GET('/home/<USER>');
};

export const getGithubData = (params: { page: number; page_size: number }) => {
	return GET('/home/<USER>', params);
};

export const getProjectData = (params: { type: string; project_id: number }) => {
	return GET('/home/<USER>', params);
};
