import { GET, POST, PUT, DELETE, uploadFile } from '@/service/api';

// 获取所有电池信息
export const getRunning = () => {
  return GET('/records/running');
};

// 获取所有不包括正在运行的记录
export const getNonRunningRecords = () => {
  return GET('/records/non-running');
};

// 新增记录并启动计时器
export const createRecord = (designId: string) => {
  return POST('/records', { designId });
};

// 修改记录的名称
export const changeRecordName = (id: number, name: string) => {
  return PUT(`/records/${id}/name`, { name });
};

// 结束计时器并更新记录的结束时间
export const changeRecordEndTime = (id: number, endTime: string) => {
  return PUT(`/records/${id}/end-time`, { endTime });
};

// 获取PCS日志数据
export const getPCSLogsData = () => {
  return GET('/records/pcs-logs');
};

// 获取BMS日志数据
export const getBMSLogsData = () => {
  return GET('/records/bms-logs');
};

