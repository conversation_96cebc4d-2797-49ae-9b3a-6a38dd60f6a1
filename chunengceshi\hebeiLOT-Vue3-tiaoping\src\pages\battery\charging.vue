<template>
  <div class="charging-page">
    <home-header />
    <div class="charging-container">
      <div class="charging-status">
        <div class="info-left">
          <div class="info-item">
            <div class="info-label">- km/hr</div>
            <div class="info-value">{{ chargingSpeed }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">- kW</div>
            <div class="info-value">{{ currentPower }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">- A</div>
            <div class="info-value">{{ currentAmp }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">- V</div>
            <div class="info-value">{{ currentVoltage }}</div>
          </div>
        </div>
        
        <div class="main-display">
          <div class="charge-value">{{ remainDistance }}</div>
          <div class="charge-unit">km</div>
          <div class="charge-status">{{ isCharging ? '时间校准中' : '放电中' }}</div>
          <div class="battery-model">
            <div class="battery-image" :class="{ 'charging': isCharging, 'discharging': !isCharging }">
              <div class="battery-connector" v-if="isCharging"></div>
              <div class="battery-level" :style="{width: `${batteryPercentage}%`}"></div>
            </div>
          </div>
        </div>
        
        <div class="info-right">
          <div class="charge-percentage">{{ batteryPercentage }}%</div>
          <div class="time-remaining">
            <template v-if="isCharging">
              <div class="time-label">预计充满时间</div>
              <div class="time-value">{{ estimatedFullTime }}</div>
            </template>
            <template v-else>
              <div class="time-label">预计可用时间</div>
              <div class="time-value">{{ estimatedEmptyTime }}</div>
            </template>
          </div>
        </div>
      </div>
      
      <div class="charging-controls">
        <div class="control-button" @click="toggleCharging">
          {{ isCharging ? '滑动停止充电' : '开始充电' }}
        </div>
        <div class="back-button" @click="goBack">
          返回电池管理页面
        </div>
      </div>
      
      <div class="battery-details">
        <div class="detail-title">电池详情</div>
        <div class="detail-grid">
          <div class="detail-item">
            <div class="detail-label">电池健康</div>
            <div class="detail-value">{{ batteryHealth }}%</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">温度</div>
            <div class="detail-value">{{ batteryTemp }}°C</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">循环次数</div>
            <div class="detail-value">{{ cycleCount }}次</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">总容量</div>
            <div class="detail-value">{{ totalCapacity }}Ah</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">剩余容量</div>
            <div class="detail-value">{{ remainingCapacity }}Ah</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">电压差异</div>
            <div class="detail-value">{{ voltageDifference }}V</div>
          </div>
        </div>
      </div>
      
      <div class="charging-history">
        <div class="history-title">充放电历史记录</div>
        <div class="history-chart" ref="historyChartRef"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import HomeHeader from '../../components/ModuleItem/HomeHeader.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 充电状态数据
const isCharging = ref(true);
const batteryPercentage = ref(76);
const remainDistance = ref(65);
const chargingSpeed = ref(32);
const currentPower = ref(7.8);
const currentAmp = ref(15.2);
const currentVoltage = ref(380);
const estimatedFullTime = ref('2小时23分钟');
const estimatedEmptyTime = ref('4小时56分钟');

// 电池详情数据
const batteryHealth = ref(92);
const batteryTemp = ref(36.2);
const cycleCount = ref(126);
const totalCapacity = ref(24.5);
const remainingCapacity = ref(18.7);
const voltageDifference = ref(0.27);

// 图表引用
const historyChartRef = ref<HTMLElement | null>(null);
let historyChart: echarts.ECharts | null = null;

// 充放电历史数据
const chargeHistoryData = [
  { time: '08:00', power: 6.8 },
  { time: '10:00', power: 7.2 },
  { time: '12:00', power: 7.8 },
  { time: '14:00', power: 7.5 },
  { time: '16:00', power: 7.0 },
  { time: '18:00', power: 6.5 }
];

const dischargeHistoryData = [
  { time: '08:00', power: -2.3 },
  { time: '10:00', power: -3.1 },
  { time: '12:00', power: -2.8 },
  { time: '14:00', power: -3.5 },
  { time: '16:00', power: -2.9 },
  { time: '18:00', power: -2.2 }
];

// 模拟充电过程
let chargingInterval: number | null = null;

const initChargingSimulation = () => {
  if (chargingInterval) {
    clearInterval(chargingInterval);
  }
  
  if (isCharging.value) {
    chargingInterval = setInterval(() => {
      if (batteryPercentage.value < 100) {
        batteryPercentage.value += 1;
        remainDistance.value += 2;
        updateChargingStats();
      } else {
        clearInterval(chargingInterval as number);
        chargingInterval = null;
      }
    }, 5000) as unknown as number;
  } else {
    chargingInterval = setInterval(() => {
      if (batteryPercentage.value > 0) {
        batteryPercentage.value -= 1;
        remainDistance.value -= 2;
        updateChargingStats();
      } else {
        clearInterval(chargingInterval as number);
        chargingInterval = null;
      }
    }, 5000) as unknown as number;
  }
};

const updateChargingStats = () => {
  if (isCharging.value) {
    chargingSpeed.value = 30 + Math.floor(Math.random() * 5);
    currentPower.value = 7.5 + Math.random() * 0.8;
    currentAmp.value = 14.8 + Math.random() * 1;
  } else {
    chargingSpeed.value = 0;
    currentPower.value = -(2.5 + Math.random() * 0.5);
    currentAmp.value = -(5.8 + Math.random() * 0.7);
  }
  
  // 随机小波动更新温度
  batteryTemp.value = 35 + Math.random() * 2;
  // 更新剩余容量
  remainingCapacity.value = (batteryPercentage.value / 100) * totalCapacity.value;
};

// 初始化充放电历史图表
const initHistoryChart = () => {
  if (historyChartRef.value) {
    historyChart = echarts.init(historyChartRef.value);
    
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: '',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['充电功率', '放电功率'],
        textStyle: {
          color: '#fff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: chargeHistoryData.map(item => item.time),
          axisLine: {
            lineStyle: {
              color: '#aaa'
            }
          },
          axisLabel: {
            color: '#ddd'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '功率(kW)',
          nameTextStyle: {
            color: '#ddd'
          },
          axisLine: {
            lineStyle: {
              color: '#aaa'
            }
          },
          axisLabel: {
            color: '#ddd'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        }
      ],
      series: [
        {
          name: '充电功率',
          type: 'line',
          stack: 'Total',
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 255, 153, 0.8)' },
              { offset: 1, color: 'rgba(0, 255, 153, 0.1)' }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: chargeHistoryData.map(item => item.power),
          lineStyle: {
            width: 3,
            color: '#00ff99'
          },
          symbol: 'circle',
          symbolSize: 8
        },
        {
          name: '放电功率',
          type: 'line',
          stack: 'Total',
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(65, 105, 225, 0.8)' },
              { offset: 1, color: 'rgba(65, 105, 225, 0.1)' }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: dischargeHistoryData.map(item => item.power),
          lineStyle: {
            width: 3,
            color: '#4169e1'
          },
          symbol: 'circle',
          symbolSize: 8
        }
      ]
    };
    
    historyChart.setOption(option);
  }
};

// 切换充放电状态
const toggleCharging = () => {
  isCharging.value = !isCharging.value;
  initChargingSimulation();
};

// 跳转到电池管理页面
const goBack = () => {
  router.push('/battery');
};

// 组件挂载和销毁时的处理
onMounted(() => {
  initChargingSimulation();
  initHistoryChart();
  
  // 响应窗口大小变化
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chargingInterval) {
    clearInterval(chargingInterval);
    chargingInterval = null;
  }
  
  if (historyChart) {
    historyChart.dispose();
    historyChart = null;
  }
  
  window.removeEventListener('resize', handleResize);
});

// 处理窗口大小变化
const handleResize = () => {
  if (historyChart) {
    historyChart.resize();
  }
};
</script>

<style lang="scss" scoped>
.charging-page {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1a237e 0%, #0d47a1 50%, #0277bd 100%);
  color: #fff;
  overflow-y: auto;
}

.charging-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.charging-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin-bottom: 30px;
}

.info-left {
  width: 20%;
}

.info-item {
  margin-bottom: 25px;
}

.info-label {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5px;
}

.info-value {
  font-size: 22px;
  font-weight: 600;
}

.main-display {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.charge-value {
  font-size: 150px;
  font-weight: 700;
  line-height: 1;
  color: white;
}

.charge-unit {
  font-size: 24px;
  margin-top: -10px;
  margin-bottom: 15px;
}

.charge-status {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30px;
}

.battery-model {
  width: 100%;
  height: 180px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.battery-image {
  width: 70%;
  height: 100%;
  background: url('/assets/car.svg') no-repeat center;
  background-size: contain;
  position: relative;
}

.battery-connector {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 80px;
  background: #00FF99;
  border-radius: 5px;
  box-shadow: 0 0 15px rgba(0, 255, 153, 0.7);
}

.battery-level {
  position: absolute;
  bottom: 25px;
  left: 25%;
  height: 18px;
  background: linear-gradient(90deg, #00ff99, #33ffcc);
  border-radius: 10px;
  transition: width 1s ease;
}

.info-right {
  width: 20%;
  text-align: right;
}

.charge-percentage {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
}

.time-label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5px;
}

.time-value {
  font-size: 20px;
  font-weight: 600;
}

.charging-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.control-button {
  padding: 15px 40px;
  background: rgba(0, 255, 153, 0.2);
  border: 2px solid #00ff99;
  border-radius: 30px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 255, 153, 0.4);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }
}

.back-button {
  padding: 15px 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid #fff;
  border-radius: 30px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 20px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }
}

.battery-details {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.detail-title, .history-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.detail-item {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
}

.detail-label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.detail-value {
  font-size: 22px;
  font-weight: 600;
}

.charging-history {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.history-chart {
  width: 100%;
  height: 300px;
}

// 动画效果
.charging .battery-level {
  animation: pulse 2s infinite;
}

.discharging .battery-level {
  animation: discharge 3s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 153, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 255, 153, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 153, 0);
  }
}

@keyframes discharge {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .charging-status {
    flex-direction: column;
    padding: 20px;
  }
  
  .info-left, .info-right {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .info-right {
    text-align: left;
    margin-top: 20px;
  }
  
  .main-display {
    width: 100%;
  }
  
  .charge-value {
    font-size: 100px;
  }
  
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .charge-value {
    font-size: 80px;
  }
}
</style> 