version: '1.0'
name: master-pipeline
displayName: MasterPipeline
triggers:
  trigger: auto
  push:
    branches:
      include:
        - master
stages:
  - name: compile
    displayName: 编译
    strategy: naturally
    trigger: auto
    steps:
      - step: build@nodejs
        name: build_nodejs
        displayName: Nodejs 构建
        nodeVersion: 16.14.2
        commands:
          - npm install && rm -rf ./dist && npm run build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./dist
        caches:
          - ~/.npm
        strategy: {}
      - step: publish@general_artifacts
        name: publish_general_artifacts
        displayName: 上传制品
        dependArtifact: BUILD_ARTIFACT
        artifactName: output
        strategy: {}
        dependsOn: build_nodejs
  - name: release
    displayName: 发布
    strategy: naturally
    trigger: auto
    steps:
      - step: publish@release_artifacts
        name: publish_release_artifacts
        displayName: 发布
        dependArtifact: output
        version: *******
        autoIncrement: true
        strategy: {}
  - name: stage-890acdae
    displayName: 部署
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: deploy@agent
        name: deploy_agent
        displayName: 主机部署
        hostGroupID:
          ID: DM_Screen
          hostID:
            - f8913531-f147-4633-bd9b-c9fdd8090d83
        deployArtifact:
          - source: build
            name: output
            target: ~/gitee_go/deploy
            dependArtifact: BUILD_ARTIFACT
        script:
          - '# 功能：部署脚本会在部署主机组的每台机器上执行'
          - '# 使用场景：先将制品包解压缩到指定目录中，再执行启动脚本deploy.sh，脚本示例地址：https://gitee.com/gitee-go/spring-boot-maven-deploy-case/blob/master/deploy.sh'
          - '# mkdir -p /home/<USER>/app'
          - '# tar zxvf ~/gitee_go/deploy/output.tar.gz -C /home/<USER>/app'
          - '# sh /home/<USER>/app/deploy.sh restart'
          - '# 如果你是php之类的无需制品包的制品方式，可以使用 git clone 或者 git pull 将源代码更新到服务器，再执行其他命令'
          - '# git clone ***@***.git'
          - ''
          - cd /usr/local/DM-Screen
          - git pull
          - rm -rf ./dist
          - tar zxvf ~/gitee_go/deploy/output.tar.gz -C /usr/local/DM-Screen
          - echo 'Hello Gitee Go!'
          - ''
        notify: []
        strategy:
          retry: '0'
