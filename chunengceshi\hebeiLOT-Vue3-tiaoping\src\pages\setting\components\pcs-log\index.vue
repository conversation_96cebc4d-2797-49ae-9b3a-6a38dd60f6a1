<!-- PcsLog.vue -->
<template>
	<div class="right box">
		<div class="title">{{ tableTitle }}</div>
		<div class="edit">
			<div class="edit-left">
				<a-range-picker
					v-model:value="dateRange"
					:show-time="{ format: 'HH:mm' }"
					format="YYYY-MM-DD HH:mm"
					:placeholder="['开始时间', '结束时间']"
					style="margin-left: 8px"
				/>
				<a-select v-model:value="sortOrder" style="width: 120px; margin-left: 8px" @change="handleSortChange">
					<a-select-option value="desc">时间倒序</a-select-option>
					<a-select-option value="asc">时间正序</a-select-option>
				</a-select>
			</div>
			<div class="edit-right">
				<div class="edit-right-item">
					<DownloadOutlined style="font-size: 32px; color: #fff" @click="handleDownload" />
				</div>
			</div>
		</div>
		<div class="table">
			<a-table
				:dataSource="dataSource"
				:columns="columns"
				:rowKey="record => record.id"
				:scroll="{ x: 3500, y: 550 }"
				:pagination="false"
			>
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'created_date'">
						<a-tag :bordered="false" color="processing">
							{{ record.created_date }}
						</a-tag>
					</template>
				</template>
			</a-table>
		</div>
		<a-pagination
			:current="pagination.current"
			:total="pagination.total"
			:page-size="pagination.pageSize"
			@change="handlePageChange"
			@show-size-change="handlePageSizeChange"
			show-size-changer
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import dayjs from 'dayjs';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { getPCSLogs, downloadPCSLogs } from '../../service';
import message from 'ant-design-vue/es/message';

const tableTitle = ref('设备日志');
const dataSource = ref([]);
const dateRange = ref([]);
const sortOrder = ref('desc');
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0
});

const switchTable = async () => {
	const formatDayjs = date => {
		if (date) {
			return dayjs(date).toISOString();
		}
		return undefined;
	};

	const params = {
		page: pagination.current,
		pageSize: pagination.pageSize,
		startTime: dateRange.value ? formatDayjs(dateRange.value[0]) : undefined,
		endTime: dateRange.value ? formatDayjs(dateRange.value[1]) : undefined,
		sortOrder: sortOrder.value
	};

	let res = await getPCSLogs(params);
	res = res.data;
	dataSource.value = res.data.logs.map(log => ({
		...log,
		created_date: dayjs(log.created_at).format('YYYY-MM-DD'),
		created_time: dayjs(log.created_at).format('HH:mm:ss.SSS')
	}));
	pagination.total = res.data.totalLogs;
	pagination.pageSize = res.data.pageSize;
};

const handleSortChange = (value: string) => {
	sortOrder.value = value;
	switchTable();
};

const handlePageSizeChange = (current: number, size: number) => {
	pagination.pageSize = size;
	pagination.current = current;
	switchTable();
};

const handlePageChange = (page: number) => {
	pagination.current = page;
	switchTable();
};

const handleDownload = async () => {
	try {
		const queryParams: any = {
			sortOrder: sortOrder.value
		};

		if (dateRange.value && dateRange.value.length === 2) {
			queryParams.startTime = dayjs(dateRange.value[0]).toISOString();
			queryParams.endTime = dayjs(dateRange.value[1]).toISOString();
		}

		await downloadPCSLogs(queryParams);
	} catch (error) {
		message.error('下载日志失败');
	}
};

watch(dateRange, () => {
	switchTable();
});

const columns = [
	{ title: '记录日期', dataIndex: 'created_date', key: 'created_date', width: 150 },
	{ title: '记录时间', dataIndex: 'created_time', key: 'created_time', width: 150 },
	{ title: '数据头', dataIndex: 'perx', key: 'perx' },
	{ title: '交流电网电压A (0.1V)', dataIndex: 'grid_ua', key: 'grid_ua' },
	{ title: '交流电网电压B (0.1V)', dataIndex: 'grid_ub', key: 'grid_ub' },
	{ title: '交流电网电压C (0.1V)', dataIndex: 'grid_uc', key: 'grid_uc' },
	{ title: '交流电流Ia (0.01A)', dataIndex: 'grid_ia', key: 'grid_ia' },
	{ title: '交流电流Ib (0.01A)', dataIndex: 'grid_ib', key: 'grid_ib' },
	{ title: '交流电流Ic (0.01A)', dataIndex: 'grid_ic', key: 'grid_ic' },
	{ title: '电网频率 (0.01Hz)', dataIndex: 'grid_fre', key: 'grid_fre' },
	{ title: '电网功率因数', dataIndex: 'grid_pf', key: 'grid_pf' },
	{ title: '交流功率总和', dataIndex: 'p_ac_sum', key: 'p_ac_sum' },
	{ title: '直流母线电压 (0.1V)', dataIndex: 'vp_bus', key: 'vp_bus' },
	{ title: '母线电压 (0.1V)', dataIndex: 'vm_bus', key: 'vm_bus' },
	{ title: '直流电流 (0.1A)', dataIndex: 'i_dc', key: 'i_dc' },
	{ title: '直流功率总和', dataIndex: 'p_dc_sum', key: 'p_dc_sum' },
	{ title: '逆变器温度 (°C)', dataIndex: 'temp_inverter', key: 'temp_inverter' },
	{ title: '散热器1温度 (°C)', dataIndex: 'temp_fin1', key: 'temp_fin1' },
	{ title: '散热器2温度 (°C)', dataIndex: 'temp_fin2', key: 'temp_fin2' },
	{ title: '交流泄漏电流 (mA)', dataIndex: 'iac_leak', key: 'iac_leak' },
	{ title: 'PI电流设定值', dataIndex: 'pi_i_set', key: 'pi_i_set' },
	{ title: 'D轴电压 (V)', dataIndex: 'd_v', key: 'd_v' },
	{ title: 'Q轴电压 (V)', dataIndex: 'q_v', key: 'q_v' },
	{ title: 'D轴电流 (A)', dataIndex: 'd_i', key: 'd_i' },
	{ title: 'Q轴电流 (A)', dataIndex: 'q_i', key: 'q_i' },
	{ title: '相位', dataIndex: 'phase', key: 'phase' },
	{ title: '启动状态', dataIndex: 'start_status', key: 'start_status' },
	{ title: '维护状态', dataIndex: 'maintain_status', key: 'maintain_status' }
];

switchTable();
</script>

<style scoped>
.right.box {
	padding: 16px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.title {
	color: #fff;
	border-left: #2ca6ff 3px solid;
	padding-left: 10px;
	margin-top: 10px;
	margin-left: 10px;
	font-size: 18px;
	margin-bottom: 20px;
}

.edit {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16px;
}

.edit-left {
	display: flex;
	align-items: center;
}

.edit-right {
	display: flex;
	align-items: center;
}

.edit-right-item {
	cursor: pointer;
}

.table {
	margin-top: 16px;
}

a-table {
	background: #fff;
}
</style>
