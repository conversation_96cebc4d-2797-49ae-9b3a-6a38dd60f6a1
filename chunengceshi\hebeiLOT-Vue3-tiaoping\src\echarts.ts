import * as echarts from 'echarts/core';
import {
	ToolboxComponent,
	TooltipComponent,
	GridComponent,
	LegendComponent,
	VisualMapComponent,
	TitleComponent,
	DataZoomComponent
} from 'echarts/components';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts';
import { <PERSON><PERSON><PERSON> } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
	ToolboxComponent,
	TooltipComponent,
	GridComponent,
	LegendComponent,
	VisualMapComponent,
	Bar<PERSON>hart,
	<PERSON><PERSON>hart,
	DataZoomComponent,
	CanvasRenderer,
	RadarChart,
	UniversalTransition,
	PieChart,
	TitleComponent
]);

export default echarts;
