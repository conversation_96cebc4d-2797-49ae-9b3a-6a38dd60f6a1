<template>
  <div class="battery-monitor-container">
    <div class="header">
      <div class="title">独立储能辅助服务系统</div>
      <div class="home-button" @click="goToHomepage">
        <left-outlined />
        <span>返回主页</span>
      </div>
    </div>
    
    <div class="content">
      <div class="left-panel">
        <div class="nav-title">
          <div class="title-text">储能首页</div>
        </div>
        
        <div class="power-chart-section">
          <div class="section-header">
            <div class="title">功率曲线</div>
            <div class="subtitle">单位(kW)</div>
          </div>
          <div class="chart-container" ref="powerChartRef"></div>
        </div>
        
        <div class="info-tabs">
          <div class="tab-header">
            <div class="tab-item active">交流信息</div>
            <div class="tab-item">电池信息</div>
          </div>
          <div class="tab-content">
            <div class="power-info-table">
              <div class="table-header">
                <div class="header-cell">功率 (kW)</div>
                <div class="header-cell">无功 (kVar)</div>
                <div class="header-cell">视在 (kVA)</div>
                <div class="header-cell">功率因数</div>
                <div class="header-cell">电流 (A)</div>
                <div class="header-cell">电压 (V)</div>
              </div>
              <div class="table-row">
                <div class="cell">A</div>
                <div class="cell value negative">-84.1</div>
                <div class="cell value">1.7</div>
                <div class="cell value">84</div>
                <div class="cell value">0.99</div>
                <div class="cell value negative">-370.5</div>
                <div class="cell value">0</div>
              </div>
              <div class="table-row">
                <div class="cell">B</div>
                <div class="cell value negative">-82.9</div>
                <div class="cell value">1.5</div>
                <div class="cell value">83</div>
                <div class="cell value">0.99</div>
                <div class="cell value negative">-367.8</div>
                <div class="cell value">0</div>
              </div>
              <div class="table-row">
                <div class="cell">C</div>
                <div class="cell value negative">-83.1</div>
                <div class="cell value">1.3</div>
                <div class="cell value">83</div>
                <div class="cell value">0.99</div>
                <div class="cell value negative">-366.1</div>
                <div class="cell value">0</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="right-panel">
        <div class="selection-row">
          <div class="station-select">
            <div class="label">站点:</div>
            <div class="select-box">
              <div class="selected-value">双太阳能系统</div>
              <down-outlined />
            </div>
          </div>
          <div class="time-select">
            <div class="label">分组:</div>
            <div class="select-box">
              <div class="selected-value">储能</div>
              <down-outlined />
            </div>
          </div>
        </div>
        
        <div class="status-cards">
          <div class="card">
            <div class="card-header">总有功功率</div>
            <div class="card-value">250<span class="unit">kW</span></div>
            <div class="card-icon">
              <thunderbolt-outlined />
            </div>
          </div>
          
          <div class="status-row">
            <div class="status-card">
              <div class="status-icon">
                <thunderbolt-outlined />
              </div>
              <div class="status-info">
                <div class="status-label">总无功功率</div>
                <div class="status-value">4.6 kVar</div>
              </div>
            </div>
            
            <div class="status-card">
              <div class="status-icon">
                <dashboard-outlined />
              </div>
              <div class="status-info">
                <div class="status-label">总视在功率</div>
                <div class="status-value">250.1kVA</div>
              </div>
            </div>
            
            <div class="status-card">
              <div class="status-icon">
                <line-chart-outlined />
              </div>
              <div class="status-info">
                <div class="status-label">频率</div>
                <div class="status-value">49.98Hz</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="battery-info">
          <div class="info-row">
            <div class="info-card">
              <div class="info-title">总充电量</div>
              <div class="info-value">159153<span class="unit">kWh</span></div>
              <div class="energy-icon charging"></div>
            </div>
            
            <div class="info-card">
              <div class="info-title">总放电量</div>
              <div class="info-value">157973<span class="unit">kWh</span></div>
              <div class="energy-icon discharging"></div>
            </div>
            
            <div class="info-card">
              <div class="info-title">放电量转化率</div>
              <div class="info-value special">99<span class="unit">%</span></div>
              <div class="energy-icon efficiency"></div>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-card">
              <div class="info-title">今日充电量</div>
              <div class="info-value">808.32<span class="unit">kWh</span></div>
              <div class="energy-icon today-charging">
                <svg class="battery-svg" viewBox="0 0 24 24" width="24" height="24">
                  <path d="M15,4V3H9V4H4V21H20V4H15M12,18L7,13H10V9H14V13H17L12,18" 
                        fill="#00c8ff"/>
                </svg>
              </div>
            </div>
            
            <div class="info-card">
              <div class="info-title">今日放电量</div>
              <div class="info-value">403.32<span class="unit">kWh</span></div>
              <div class="energy-icon today-discharging">
                <svg class="battery-svg" viewBox="0 0 24 24" width="24" height="24">
                  <path d="M15,4V3H9V4H4V21H20V4H15M12,9L7,14H10V18H14V14H17L12,9" 
                        fill="#00c8ff"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        <div class="temperature-info">
          <div class="temp-row">
            <div class="temp-label">PCS模块温度</div>
            <div class="temp-value">61°C</div>
          </div>
          <div class="temp-row">
            <div class="temp-label">PCS环境温度</div>
            <div class="temp-value">39°C</div>
          </div>
        </div>
        
        <div class="system-status">
          <div class="status-header">系统运行状态</div>
          <div class="status-grid">
            <div class="status-item">
              <div class="status-label">运行状态</div>
              <div class="status-indicator-container">
                <div class="status-indicator normal" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">正常</span>
                </div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">系统在管</div>
              <div class="status-indicator-container">
                <div class="status-indicator normal" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">正常</span>
                </div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">远程控制</div>
              <div class="status-indicator-container">
                <div class="status-indicator normal" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">正常</span>
                </div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">系统故障</div>
              <div class="status-indicator-container">
                <div class="status-indicator normal" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">正常</span>
                </div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">并网</div>
              <div class="status-indicator-container">
                <div class="status-indicator normal" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">正常</span>
                </div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">PCS通讯状态</div>
              <div class="status-indicator-container">
                <div class="status-indicator error" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">异常</span>
                </div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">BMS通讯状态</div>
              <div class="status-indicator-container">
                <div class="status-indicator error" title="点击切换状态">
                  <div class="indicator-light"></div>
                  <span class="indicator-text">异常</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { BellOutlined, FullscreenOutlined, UserOutlined, DownOutlined, ThunderboltOutlined, DashboardOutlined, LineChartOutlined, LeftOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { useRouter } from 'vue-router';

const router = useRouter();
const powerChartRef = ref<HTMLElement | null>(null);
let powerChart: echarts.ECharts | null = null;

// 返回主页函数
const goToHomepage = () => {
  router.push('/menu');
};

// 添加状态指示灯的交互效果
const statusTooltips = {
  normal: '系统正常运行中',
  error: '系统异常，请检查'
};

// 模拟状态变化
const toggleStatus = (event: Event) => {
  const indicator = event.currentTarget as HTMLElement;
  if (indicator.classList.contains('normal')) {
    indicator.classList.remove('normal');
    indicator.classList.add('warning');
    const text = indicator.querySelector('.indicator-text') as HTMLElement;
    if (text) text.innerText = '警告';
  } else if (indicator.classList.contains('warning')) {
    indicator.classList.remove('warning');
    indicator.classList.add('error');
    const text = indicator.querySelector('.indicator-text') as HTMLElement;
    if (text) text.innerText = '异常';
  } else if (indicator.classList.contains('error')) {
    indicator.classList.remove('error');
    indicator.classList.add('normal');
    const text = indicator.querySelector('.indicator-text') as HTMLElement;
    if (text) text.innerText = '正常';
  }
};

onMounted(() => {
  initPowerChart();
  window.addEventListener('resize', handleResize);
  
  // 为状态指示灯添加点击事件
  const indicators = document.querySelectorAll('.status-indicator');
  indicators.forEach(indicator => {
    indicator.addEventListener('click', toggleStatus as EventListener);
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (powerChart) {
    powerChart.dispose();
  }
  
  // 移除事件监听器
  const indicators = document.querySelectorAll('.status-indicator');
  indicators.forEach(indicator => {
    indicator.removeEventListener('click', toggleStatus as EventListener);
  });
});

const handleResize = () => {
  if (powerChart) {
    powerChart.resize();
  }
};

const initPowerChart = () => {
  if (powerChartRef.value) {
    powerChart = echarts.init(powerChartRef.value);
    
    // 生成X轴时间数据
    const hours = [];
    for (let i = 0; i <= 24; i++) {
      const hour = i.toString().padStart(2, '0') + ':00';
      hours.push(hour);
    }
    
    // 生成Y轴功率数据 - 模拟图片中的曲线
    const powerData = [
      0, 0, 0, 0, 0, 0, 
      150, 150, 150, 150, 100, 0, 
      0, 0, 0, 0, 0, -250, 
      -250, -250, 0, 0, 0, 0, 0
    ];
    
    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: hours,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#999',
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        min: -300,
        max: 200,
        interval: 100,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          color: '#999',
          fontSize: 10
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: '功率',
          type: 'line',
          sampling: 'average',
          symbol: 'none',
          itemStyle: {
            color: '#00f0ff'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(0, 240, 255, 0.5)'
              },
              {
                offset: 1,
                color: 'rgba(0, 240, 255, 0)'
              }
            ])
          },
          lineStyle: {
            width: 2
          },
          data: powerData
        }
      ]
    };
    
    powerChart.setOption(option);
  }
};
</script>

<style scoped lang="scss">
.battery-monitor-container {
  width: 100%;
  height: 100vh;
  background-color: #081537;
  color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background-color: #081537;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  .title {
    font-size: 22px;
    font-weight: bold;
    color: white;
  }
  
  .home-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background-color: rgba(0, 127, 255, 0.2);
    border: 1px solid rgba(0, 127, 255, 0.4);
    border-radius: 4px;
    color: #00c8ff;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(0, 127, 255, 0.3);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .notification-icon, .fullscreen-icon, .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
    
    .notification-icon {
      position: relative;
      
      .badge {
        position: absolute;
        top: 0;
        right: 0;
        width: 16px;
        height: 16px;
        background-color: #f00;
        border-radius: 50%;
        font-size: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.content {
  flex: 1;
  display: flex;
  overflow: auto;
}

.left-panel {
  width: 70%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  
  .nav-title {
    padding: 10px;
    
    .title-text {
      font-size: 18px;
      font-weight: bold;
      position: relative;
      display: inline-block;
      padding-left: 15px;
      
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background-color: #007fff;
      }
    }
  }
  
  .power-chart-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 10px;
      
      .title {
        font-size: 16px;
        font-weight: bold;
      }
      
      .subtitle {
        font-size: 12px;
        color: #999;
      }
    }
    
    .chart-container {
      flex: 1;
      min-height: 300px;
    }
  }
  
  .info-tabs {
    height: 200px;
    margin-top: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    
    .tab-header {
      display: flex;
      background-color: rgba(255, 255, 255, 0.05);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      .tab-item {
        padding: 10px 20px;
        cursor: pointer;
        
        &.active {
          background-color: #0d2350;
          border-bottom: 2px solid #007fff;
        }
      }
    }
    
    .tab-content {
      padding: 10px;
      height: calc(100% - 40px);
      overflow: auto;
    }
    
    .power-info-table {
      width: 100%;
      
      .table-header {
        display: grid;
        grid-template-columns: 20px repeat(6, 1fr);
        background-color: rgba(0, 127, 255, 0.1);
        padding: 8px 0;
        
        .header-cell {
          text-align: center;
          font-size: 14px;
          font-weight: bold;
        }
      }
      
      .table-row {
        display: grid;
        grid-template-columns: 20px repeat(6, 1fr);
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        
        .cell {
          text-align: center;
          font-size: 14px;
          
          &.value {
            color: #00a8ff;
            
            &.negative {
              color: #ff5b5b;
            }
          }
        }
      }
    }
  }
}

.right-panel {
  width: 30%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 100%;
  overflow-y: auto;
  
  .selection-row {
    display: flex;
    justify-content: space-between;
    
    .station-select, .time-select {
      display: flex;
      align-items: center;
      
      .label {
        margin-right: 10px;
        font-size: 14px;
      }
      
      .select-box {
        padding: 5px 10px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .selected-value {
          margin-right: 10px;
        }
      }
    }
  }
  
  .status-cards {
    .card {
      background-color: rgba(0, 127, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      position: relative;
      
      .card-header {
        font-size: 14px;
        color: #999;
      }
      
      .card-value {
        font-size: 32px;
        font-weight: bold;
        margin-top: 5px;
        
        .unit {
          font-size: 16px;
          margin-left: 5px;
        }
      }
      
      .card-icon {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 24px;
        color: #007fff;
      }
    }
    
    .status-row {
      display: flex;
      gap: 10px;
      
      .status-card {
        flex: 1;
        background-color: rgba(0, 127, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        display: flex;
        align-items: center;
        
        .status-icon {
          font-size: 20px;
          color: #007fff;
          margin-right: 10px;
        }
        
        .status-info {
          .status-label {
            font-size: 12px;
            color: #999;
          }
          
          .status-value {
            font-size: 14px;
            font-weight: bold;
            margin-top: 5px;
          }
        }
      }
    }
  }
  
  .battery-info {
    .info-row {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
      
      .info-card {
        flex: 1;
        background-color: rgba(0, 127, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        position: relative;
        
        .info-title {
          font-size: 12px;
          color: #999;
        }
        
        .info-value {
          font-size: 20px;
          font-weight: bold;
          margin-top: 5px;
          color: #00c8ff;
          
          &.special {
            color: #00ff00;
          }
          
          .unit {
            font-size: 12px;
            margin-left: 2px;
          }
        }
        
        .energy-icon {
          position: absolute;
          bottom: 5px;
          right: 5px;
          width: 40px;
          height: 40px;
          background-image: linear-gradient(135deg, rgba(0, 200, 255, 0.2), rgba(0, 127, 255, 0.2));
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          
          .battery-svg {
            width: 28px;
            height: 28px;
            filter: drop-shadow(0 0 2px rgba(0, 200, 255, 0.7));
          }
          
          &::before, &::after {
            content: "";
            position: absolute;
          }
          
          &.charging::before {
            width: 20px;
            height: 10px;
            border-left: 2px solid #00c8ff;
            border-bottom: 2px solid #00c8ff;
            transform: rotate(45deg);
          }
          
          &.discharging::before {
            width: 20px;
            height: 10px;
            border-left: 2px solid #00c8ff;
            border-top: 2px solid #00c8ff;
            transform: rotate(135deg);
          }
          
          &.efficiency::before {
            width: 20px;
            height: 20px;
            border: 2px solid #00ff00;
            border-radius: 50%;
          }
          
          &.today-charging, &.today-discharging {
            background-image: linear-gradient(135deg, rgba(0, 200, 255, 0.3), rgba(0, 127, 255, 0.3));
          }
        }
      }
    }
  }
  
  .temperature-info {
    background-color: rgba(0, 127, 255, 0.1);
    border-radius: 8px;
    padding: 8px;
    
    .temp-row {
      display: flex;
      justify-content: space-between;
      padding: 5px 0;
      
      .temp-label {
        font-size: 14px;
      }
      
      .temp-value {
        font-size: 14px;
        font-weight: bold;
        color: #00c8ff;
      }
    }
  }
  
  .system-status {
    background-color: rgba(0, 127, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    
    .status-header {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 12px;
      color: #00c8ff;
      position: relative;
      padding-left: 15px;
      
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background-color: #00c8ff;
      }
    }
    
    .status-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      
      .status-item {
        display: flex;
        flex-direction: column;
        
        .status-label {
          font-size: 14px;
          color: #bbb;
          margin-bottom: 5px;
        }
        
        .status-indicator-container {
          height: 28px;
          
          .status-indicator {
            display: flex;
            align-items: center;
            height: 100%;
            border-radius: 15px;
            padding: 0 10px;
            width: fit-content;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }
            
            &:active {
              transform: translateY(0);
            }
            
            &::before {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
            }
            
            .indicator-light {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin-right: 8px;
              position: relative;
              
              &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border-radius: 50%;
                animation: pulse 2s infinite;
              }
            }
            
            .indicator-text {
              font-size: 12px;
              font-weight: 500;
              transition: all 0.3s ease;
            }
            
            &.normal {
              background-color: rgba(0, 220, 130, 0.15);
              border: 1px solid rgba(0, 220, 130, 0.4);
              
              .indicator-light {
                background-color: #00dc82;
                box-shadow: 0 0 10px #00dc82;
                
                &::after {
                  box-shadow: 0 0 15px 2px rgba(0, 220, 130, 0.7);
                  animation: pulse-normal 2s infinite;
                }
              }
              
              .indicator-text {
                color: #00dc82;
              }
            }
            
            &.warning {
              background-color: rgba(255, 204, 0, 0.15);
              border: 1px solid rgba(255, 204, 0, 0.4);
              
              .indicator-light {
                background-color: #ffcc00;
                box-shadow: 0 0 10px #ffcc00;
                
                &::after {
                  box-shadow: 0 0 15px 2px rgba(255, 204, 0, 0.7);
                  animation: pulse-warning 1.5s infinite;
                }
              }
              
              .indicator-text {
                color: #ffcc00;
              }
            }
            
            &.error {
              background-color: rgba(255, 59, 48, 0.15);
              border: 1px solid rgba(255, 59, 48, 0.4);
              
              .indicator-light {
                background-color: #ff3b30;
                box-shadow: 0 0 10px #ff3b30;
                
                &::after {
                  box-shadow: 0 0 15px 2px rgba(255, 59, 48, 0.7);
                  animation: pulse-error 1s infinite;
                }
              }
              
              .indicator-text {
                color: #ff3b30;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes pulse-normal {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.5);
  }
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

@keyframes pulse-warning {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.7);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes pulse-error {
  0% {
    opacity: 0.9;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.9);
  }
  100% {
    opacity: 0.9;
    transform: scale(1);
  }
}
</style> 