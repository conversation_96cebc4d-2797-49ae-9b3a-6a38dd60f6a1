<template>
	<div ref="chartRef" style="width: 100%; height: 400px"></div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const chartRef = ref(null);
let chartInstance = null;

const chartOptions = ref({
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'shadow'
		}
	},
	legend: {
		data: ['充电', '放电']
	},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true
	},
	xAxis: {
		type: 'category',
		data: ['7-07', '7-08', '7-09', '7-10', '7-11', '7-12', '7-13']
	},
	yAxis: {
		type: 'value'
	},
	series: [
		{
			name: '充电',
			type: 'bar',
			data: [1200, 1320, 1010, 1340, 900, 230, 210],
			itemStyle: {
				color: '#4284ea'
			}
		},
		{
			name: '放电',
			type: 'bar',
			data: [1500, 2320, 2010, 1540, 1920, 930, 720],
			itemStyle: {
				color: '#ff7f50'
			}
		}
	]
});

onMounted(() => {
	if (chartRef.value) {
		chartInstance = echarts.init(chartRef.value);
		chartInstance.setOption(chartOptions.value);
	}
});

watch(
	chartOptions,
	newOptions => {
		if (chartInstance) {
			chartInstance.setOption(newOptions);
		}
	},
	{ deep: true }
);
</script>

<style scoped></style>
