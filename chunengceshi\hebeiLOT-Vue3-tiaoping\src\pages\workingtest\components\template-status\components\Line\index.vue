<template>
  <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, defineEmits, defineProps } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  duration: {
    type: Number,
    default: 30,
  },
  initialVoltage: {
    type: Number,
    default: 3.0,
  },
  initialCurrent: {
    type: Number,
    default: 1.0,
  },
  type: {
    type: String,
    default: 'voltage', // 或 'current'
  },
  points: {
    type: Array,
    default: () => [],
  },
  draggable: {
    type: Boolean,
    default: true, // 新增可拖拽的属性
  },
});

const emit = defineEmits(['update:points']);

const chartContainer = ref(null);
let chartInstance = null;
let dragging = false;
let dragPointIndex = -1;

const generateXAxisData = (duration) => {
  const data = [];
  for (let i = 0; i <= duration; i++) {
    data.push(`${i}min`);
  }
  return data;
};

const generateInitialData = (duration, initialValue) => {
  const data = [];
  for (let i = 0; i <= duration; i++) {
    data.push([i, initialValue.toFixed(2)]);
  }
  return data;
};

const chartOption = ref({
  tooltip: {
    trigger: 'axis',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: generateXAxisData(props.duration),
  },
  yAxis: {
    type: 'value',
    max: (value) => value.max + 2, // 设置 Y 轴最大值
  },
  series: [
    {
      name: props.type === 'voltage' ? '电压' : '电流',
      type: 'line',
      data: props.points.length ? props.points : generateInitialData(props.duration, props.type === 'voltage' ? props.initialVoltage : props.initialCurrent),
      symbolSize: 20,
      symbol: 'circle',
      itemStyle: {
        color: '#ffb900',
      },
      label: {
        show: true,
        formatter: props.type === 'voltage' ? '{@[1]}V' : '{@[1]}A',
      },
      emphasis: {
        focus: 'series',
      },
    },
  ],
});

const updateChartOption = (duration, initialValue, points) => {
  const newData = points.length ? points : generateInitialData(duration, initialValue);
  const maxValue = Math.max(...newData.map((item) => parseFloat(item[1])));

  chartOption.value.xAxis.data = generateXAxisData(duration);
  chartOption.value.series[0].data = newData;
  chartOption.value.yAxis.max = maxValue + 2;

  if (chartInstance) {
    chartInstance.setOption(chartOption.value);
  }
  emit('update:points', newData); // 每次更新时发送新的数据
};

watch(
  () => [props.duration, props.initialVoltage, props.initialCurrent, props.type, props.points],
  ([newDuration, newInitialVoltage, newInitialCurrent, newType, newPoints]) => {
    const initialValue = newType === 'voltage' ? newInitialVoltage : newInitialCurrent;
    updateChartOption(newDuration, initialValue, newPoints);
  },
  { immediate: true }
);

const onMouseMove = (event) => {
  if (props.draggable && dragging && dragPointIndex >= 0) {
    const point = chartInstance.convertFromPixel({ seriesIndex: 0 }, [event.offsetX, event.offsetY]);
    const newValue = point[1].toFixed(2);
    chartOption.value.series[0].data[dragPointIndex][1] = newValue; // 仅更新值

    const maxValue = Math.max(...chartOption.value.series[0].data.map((item) => parseFloat(item[1])));
    chartOption.value.yAxis.max = maxValue + 2;

    chartInstance.setOption(chartOption.value);
  }
};

const onMouseUp = () => {
  if (props.draggable && dragging && dragPointIndex >= 0) {
    emit('update:points', chartOption.value.series[0].data); // 发送更新后的数据
  }
  dragging = false;
  dragPointIndex = -1;
};

const onChartClick = (params) => {
  if (props.draggable && params.componentType === 'series' && params.seriesType === 'line') {
    dragging = true;
    dragPointIndex = params.dataIndex;
  }
};

onMounted(() => {
  chartInstance = echarts.init(chartContainer.value);
  chartInstance.setOption(chartOption.value);

  chartInstance.getZr().on('mousemove', onMouseMove);
  chartInstance.getZr().on('mouseup', onMouseUp);
  chartInstance.on('mousedown', onChartClick);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.getZr().off('mousemove', onMouseMove);
    chartInstance.getZr().off('mouseup', onMouseUp);
    chartInstance.off('mousedown', onChartClick);
    chartInstance.dispose();
  }
});
</script>
