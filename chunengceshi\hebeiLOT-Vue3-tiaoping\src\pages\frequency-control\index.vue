<template>
  <div class="frequency-control-container">
    <transition-loading :isShow="loadShow" />
    <div class="header">
      <div class="header-left">
        <a-button class="back-button" @click="goHome">
          <template #icon><left-outlined /></template>
          返回主页
        </a-button>
        <h1>一次调频控制系统</h1>
      </div>
      <div class="control-buttons">
        <a-button type="primary" @click="startSystem">启动系统</a-button>
        <a-button danger @click="stopSystem">紧急停止</a-button>
        <a-button @click="resetSystem">系统重置</a-button>
      </div>
    </div>
    
    <div class="content">
      <div class="left-panel">
        <div class="system-status">
          <h2>系统状态</h2>
          <div class="status-item">
            <span>系统运行状态:</span>
            <a-tag :color="systemRunning ? 'success' : 'error'">
              {{ systemRunning ? '运行中' : '已停止' }}
            </a-tag>
          </div>
          <div class="status-item">
            <span>电网频率:</span>
            <span class="value">{{ gridFrequency.toFixed(2) }} Hz</span>
            <div class="frequency-gauge">
              <div class="gauge-inner" :style="{ width: getFrequencyPercentage() + '%' }"></div>
            </div>
          </div>
          <div class="status-item">
            <span>频率偏差:</span>
            <span :class="['value', getFrequencyDeviationClass()]">{{ frequencyDeviation.toFixed(3) }} Hz</span>
          </div>
        </div>
        
        <div class="control-mode">
          <h2>控制模式</h2>
          <a-radio-group v-model:value="controlMode">
            <a-radio-button value="auto">自动控制</a-radio-button>
            <a-radio-button value="manual">手动控制</a-radio-button>
          </a-radio-group>
          
          <div class="parameters" v-if="controlMode === 'auto'">
            <h3>自动控制参数</h3>
            <div class="param-item">
              <span>死区:</span>
              <a-input-number v-model:value="deadband" :min="0" :max="0.5" :step="0.01" />
              <span>Hz</span>
            </div>
            <div class="param-item">
              <span>调节速率:</span>
              <a-input-number v-model:value="regulationRate" :min="1" :max="100" :step="1" />
              <span>%/Hz</span>
            </div>
            <div class="action-button-container">
              <a-button type="primary" size="small" @click="applyAutoSettings">下发指令</a-button>
            </div>
          </div>
          
          <div class="manual-control" v-if="controlMode === 'manual'">
            <h3>手动控制</h3>
            <div class="manual-item">
              <span>电池功率设定:</span>
              <a-slider v-model:value="batteryPowerSetting" :min="-100" :max="100" />
              <a-input-number v-model:value="batteryPowerSetting" :min="-100" :max="100" />
              <span>%</span>
            </div>
            <div class="manual-item">
              <span>飞轮功率设定:</span>
              <a-slider v-model:value="flywheelPowerSetting" :min="-100" :max="100" />
              <a-input-number v-model:value="flywheelPowerSetting" :min="-100" :max="100" />
              <span>%</span>
            </div>
            <div class="action-button-container">
              <a-button type="primary" size="small" @click="applyManualSettings">下发指令</a-button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="right-panel">
        <div class="storage-status">
          <h2>储能状态</h2>
          
          <div class="storage-unit battery">
            <h3>电池储能</h3>
            <div class="battery-main-content">
              <div class="battery-container">
                <div class="battery-icon">
                  <div class="battery-level" :style="{ height: batterySOC + '%' }"></div>
                </div>
                <div class="battery-info">
                  <div class="info-item">
                    <span>SOC:</span>
                    <span class="value">{{ batterySOC.toFixed(1) }}%</span>
                  </div>
                  <div class="info-item">
                    <span>功率:</span>
                    <span class="value" :class="{'positive': batteryPower > 0, 'negative': batteryPower < 0}">
                      {{ batteryPower.toFixed(2) }} MW
                    </span>
                  </div>
                  <div class="info-item">
                    <span>响应时间:</span>
                    <span class="value">{{ batteryResponseTime.toFixed(1) }} ms</span>
                  </div>
                  <div class="info-item">
                    <span>健康状态:</span>
                    <span class="value" :class="getBatteryHealthClass()">{{ batteryHealth.toFixed(1) }}%</span>
                  </div>
                  <div class="info-item">
                    <span>剩余时间:</span>
                    <span class="value">{{ batteryRemainingTime }}</span>
                  </div>
                </div>
              </div>
              <div class="battery-chart-container">
                <div ref="batteryChartRef" class="mini-chart"></div>
              </div>
            </div>
            <div class="battery-status-bar">
              <div class="status-item">
                <span>充放电循环:</span>
                <span class="value">{{ batteryCycles }}</span>
              </div>
              <div class="status-item">
                <span>温度:</span>
                <span class="value" :class="getBatteryTempClass()">{{ batteryTemperature.toFixed(1) }}°C</span>
              </div>
            </div>
            <div class="control-panel">
              <a-button size="small" @click="enableBattery = !enableBattery" :type="enableBattery ? 'primary' : 'default'">
                {{ enableBattery ? '已启用' : '已禁用' }}
              </a-button>
            </div>
          </div>
          
          <div class="storage-unit flywheel">
            <h3>飞轮储能</h3>
            <div class="flywheel-main-content">
              <div class="flywheel-container">
                <div class="flywheel-icon" :class="{ 'spinning': flywheelSpeed > 0 }">
                  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#2196F3" stroke-width="5"/>
                    <circle cx="50" cy="50" r="20" fill="#2196F3"/>
                    <line x1="50" y1="50" x2="50" y2="5" stroke="#fff" stroke-width="3"/>
                  </svg>
                </div>
                <div class="flywheel-info">
                  <div class="info-item">
                    <span>转速:</span>
                    <span class="value">{{ flywheelSpeed.toFixed(0) }} rpm</span>
                  </div>
                  <div class="info-item">
                    <span>功率:</span>
                    <span class="value" :class="{'positive': flywheelPower > 0, 'negative': flywheelPower < 0}">
                      {{ flywheelPower.toFixed(2) }} MW
                    </span>
                  </div>
                  <div class="info-item">
                    <span>响应时间:</span>
                    <span class="value">{{ flywheelResponseTime.toFixed(1) }} ms</span>
                  </div>
                  <div class="info-item">
                    <span>能量储存:</span>
                    <span class="value">{{ flywheelEnergy.toFixed(2) }} MWh</span>
                  </div>
                  <div class="info-item">
                    <span>效率:</span>
                    <span class="value">{{ flywheelEfficiency.toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
              <div class="flywheel-chart-container">
                <div ref="flywheelChartRef" class="mini-chart"></div>
              </div>
            </div>
            <div class="flywheel-status-bar">
              <div class="status-item">
                <span>轴承温度:</span>
                <span class="value" :class="getFlywheelTempClass()">{{ flywheelTemperature.toFixed(1) }}°C</span>
              </div>
              <div class="status-item">
                <span>运行时间:</span>
                <span class="value">{{ flywheelRunningTime }}</span>
              </div>
            </div>
            <div class="control-panel">
              <a-button size="small" @click="enableFlywheel = !enableFlywheel" :type="enableFlywheel ? 'primary' : 'default'">
                {{ enableFlywheel ? '已启用' : '已禁用' }}
              </a-button>
            </div>
          </div>
          
          <div class="storage-metrics">
            <h3>储能系统综合指标</h3>
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-title">总储能容量</div>
                <div class="metric-value">{{ totalStorageCapacity.toFixed(2) }} MWh</div>
              </div>
              <div class="metric-item">
                <div class="metric-title">调频响应速度</div>
                <div class="metric-value">{{ avgResponseTime.toFixed(0) }} ms</div>
              </div>
              <div class="metric-item">
                <div class="metric-title">系统稳定性</div>
                <div class="metric-value">{{ systemStability.toFixed(1) }}%</div>
              </div>
              <div class="metric-item">
                <div class="metric-title">当前总功率</div>
                <div class="metric-value" :class="{'positive': totalPower > 0, 'negative': totalPower < 0}">
                  {{ totalPower.toFixed(2) }} MW
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="frequency-chart">
          <h2>频率曲线</h2>
          <div ref="frequencyChartRef" class="chart-container"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import * as echarts from 'echarts';
import { LeftOutlined } from '@ant-design/icons-vue';
import TransitionLoading from '@/components/TransitionLoading/index.vue';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 加载状态
const loadShow = ref(false);

// 系统状态
const systemRunning = ref(false);
const gridFrequency = ref(50.00);
const frequencyDeviation = ref(0.00);

// 控制模式
const controlMode = ref('auto');
const deadband = ref(0.1);
const regulationRate = ref(30);

// 手动控制参数
const batteryPowerSetting = ref(0);
const flywheelPowerSetting = ref(0);

// 电池储能状态
const enableBattery = ref(true);
const batterySOC = ref(80);
const batteryPower = ref(0);
const batteryResponseTime = ref(100);
const batteryHealth = ref(92);
const batteryTemperature = ref(28);
const batteryCycles = ref(324);
const batteryRemainingTime = ref('5小时12分钟');

// 飞轮储能状态
const enableFlywheel = ref(true);
const flywheelSpeed = ref(3000);
const flywheelPower = ref(0);
const flywheelResponseTime = ref(20);
const flywheelEnergy = ref(1.5);
const flywheelEfficiency = ref(95);
const flywheelTemperature = ref(35);
const flywheelRunningTime = ref('12小时36分钟');

// 频率历史数据
const frequencyHistory = ref<number[]>([]);
const powerHistory = ref<{battery: number, flywheel: number}[]>([]);
const historyTimeLabels = ref<string[]>([]);
let frequencyChart: echarts.ECharts | null = null;
const frequencyChartRef = ref<HTMLElement | null>(null);

// 电池和飞轮历史数据
const batterySOCHistory = ref<number[]>([]);
const flywheelSpeedHistory = ref<number[]>([]);
let batteryChart: echarts.ECharts | null = null;
let flywheelChart: echarts.ECharts | null = null;
const batteryChartRef = ref<HTMLElement | null>(null);
const flywheelChartRef = ref<HTMLElement | null>(null);

// 储能系统综合指标
const totalStorageCapacity = ref(5.2);
const avgResponseTime = ref(60);
const systemStability = ref(98.5);
const totalPower = computed(() => batteryPower.value + flywheelPower.value);

// 模拟数据更新定时器
let simulationTimer: number | null = null;

// 返回主页
const goHome = () => {
  router.push('/menu');
};

// 系统控制功能
const startSystem = () => {
  if (systemRunning.value) {
    message.warning('系统已经在运行中');
    return;
  }
  
  if (!enableBattery.value && !enableFlywheel.value) {
    message.error('请至少启用一种储能设备');
    return;
  }
  
  // 显示加载状态
  loadShow.value = true;
  
  // 延迟模拟系统启动过程
  setTimeout(() => {
    systemRunning.value = true;
    message.success('系统已启动');
    startSimulation();
    loadShow.value = false;
  }, 1500);
};

const stopSystem = () => {
  // 显示加载状态
  loadShow.value = true;
  
  // 延迟模拟系统关闭过程
  setTimeout(() => {
    systemRunning.value = false;
    message.success('系统已停止');
    stopSimulation();
    
    // 清零功率输出
    batteryPower.value = 0;
    flywheelPower.value = 0;
    
    loadShow.value = false;
  }, 1000);
};

const resetSystem = () => {
  // 显示加载状态
  loadShow.value = true;
  
  // 延迟模拟系统重置过程
  setTimeout(() => {
    stopSystem();
    
    // 恢复默认参数
    controlMode.value = 'auto';
    deadband.value = 0.1;
    regulationRate.value = 30;
    batteryPowerSetting.value = 0;
    flywheelPowerSetting.value = 0;
    
    // 清空历史数据
    frequencyHistory.value = [];
    powerHistory.value = [];
    historyTimeLabels.value = [];
    
    updateChart();
    message.success('系统已重置');
    
    loadShow.value = false;
  }, 2000);
};

// 计算频率偏差状态类名
const getFrequencyDeviationClass = () => {
  const deviation = Math.abs(frequencyDeviation.value);
  if (deviation <= deadband.value) return 'normal';
  if (deviation <= 0.3) return 'warning';
  return 'danger';
};

// 计算频率百分比（用于仪表盘）
const getFrequencyPercentage = () => {
  // 49-51Hz映射到0-100%
  return ((gridFrequency.value - 49) / 2) * 100;
};

// 模拟系统运行
const startSimulation = () => {
  stopSimulation();
  
  simulationTimer = window.setInterval(() => {
    if (!systemRunning.value) return;
    
    // 模拟电网频率波动 (49.7 - 50.3)
    const randomVariation = (Math.random() * 0.6 - 0.3) / 10;
    gridFrequency.value = Math.max(49.7, Math.min(50.3, gridFrequency.value + randomVariation));
    frequencyDeviation.value = gridFrequency.value - 50.0;
    
    // 记录历史数据
    const now = new Date();
    const timeLabel = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;
    
    if (frequencyHistory.value.length >= 60) {
      frequencyHistory.value.shift();
      powerHistory.value.shift();
      historyTimeLabels.value.shift();
    }
    
    frequencyHistory.value.push(gridFrequency.value);
    historyTimeLabels.value.push(timeLabel);
    
    // 计算功率响应
    updatePowerOutput();
    
    // 更新储能状态
    updateStorageStatus();
    
    // 更新图表
    updateChart();
  }, 1000);
};

const stopSimulation = () => {
  if (simulationTimer !== null) {
    clearInterval(simulationTimer);
    simulationTimer = null;
  }
};

const updatePowerOutput = () => {
  if (controlMode.value === 'auto') {
    // 自动控制模式
    if (Math.abs(frequencyDeviation.value) <= deadband.value) {
      // 在死区内，不输出功率
      batteryPower.value = 0;
      flywheelPower.value = 0;
    } else {
      // 超出死区，计算功率
      const powerAdjustment = -frequencyDeviation.value * regulationRate.value;
      
      if (enableFlywheel.value && enableBattery.value) {
        // 两种储能都启用，飞轮优先响应，电池补充
        if (Math.abs(powerAdjustment) <= 10) {
          // 小功率调整由飞轮单独处理
          flywheelPower.value = powerAdjustment;
          batteryPower.value = 0;
        } else {
          // 大功率调整由两者共同处理
          flywheelPower.value = Math.sign(powerAdjustment) * 10;
          batteryPower.value = powerAdjustment - flywheelPower.value;
        }
      } else if (enableFlywheel.value) {
        // 只启用飞轮
        flywheelPower.value = Math.max(-10, Math.min(10, powerAdjustment));
        batteryPower.value = 0;
      } else if (enableBattery.value) {
        // 只启用电池
        batteryPower.value = powerAdjustment;
        flywheelPower.value = 0;
      }
    }
  } else {
    // 手动控制模式
    if (enableBattery.value) {
      batteryPower.value = batteryPowerSetting.value / 10;
    } else {
      batteryPower.value = 0;
    }
    
    if (enableFlywheel.value) {
      flywheelPower.value = flywheelPowerSetting.value / 10;
    } else {
      flywheelPower.value = 0;
    }
  }
  
  // 记录功率历史
  powerHistory.value.push({
    battery: batteryPower.value,
    flywheel: flywheelPower.value
  });
};

const updateStorageStatus = () => {
  // 更新电池SOC
  if (batteryPower.value < 0) {
    // 放电
    batterySOC.value = Math.max(0, batterySOC.value - Math.abs(batteryPower.value) * 0.05);
  } else if (batteryPower.value > 0) {
    // 充电
    batterySOC.value = Math.min(100, batterySOC.value + batteryPower.value * 0.03);
  }
  
  // 更新飞轮转速
  if (flywheelPower.value < 0) {
    // 放电，转速下降
    flywheelSpeed.value = Math.max(0, flywheelSpeed.value - Math.abs(flywheelPower.value) * 100);
  } else if (flywheelPower.value > 0) {
    // 充电，转速上升
    flywheelSpeed.value = Math.min(6000, flywheelSpeed.value + flywheelPower.value * 100);
  }
  
  // 更新历史数据
  if (batterySOCHistory.value.length >= 30) {
    batterySOCHistory.value.shift();
  }
  batterySOCHistory.value.push(batterySOC.value);
  
  if (flywheelSpeedHistory.value.length >= 30) {
    flywheelSpeedHistory.value.shift();
  }
  flywheelSpeedHistory.value.push(flywheelSpeed.value);
  
  // 更新电池和飞轮图表
  updateBatteryChart();
  updateFlywheelChart();
  
  // 更新飞轮能量
  flywheelEnergy.value = (flywheelSpeed.value / 6000) * 3; // 最大3MWh
  
  // 更新电池剩余时间
  updateBatteryRemainingTime();
  
  // 随机波动一些参数以模拟真实情况
  batteryTemperature.value = Math.max(25, Math.min(50, batteryTemperature.value + (Math.random() - 0.5) * 0.2));
  flywheelTemperature.value = Math.max(30, Math.min(60, flywheelTemperature.value + (Math.random() - 0.5) * 0.3));
  flywheelEfficiency.value = Math.max(85, Math.min(98, 95 - Math.abs(flywheelPower.value) * 0.2));
  
  // 更新系统稳定性
  const frequencyOffset = Math.abs(frequencyDeviation.value);
  systemStability.value = Math.max(70, 100 - frequencyOffset * 50);
  
  // 计算平均响应时间
  if (enableBattery.value && enableFlywheel.value) {
    avgResponseTime.value = (batteryResponseTime.value + flywheelResponseTime.value) / 2;
  } else if (enableBattery.value) {
    avgResponseTime.value = batteryResponseTime.value;
  } else if (enableFlywheel.value) {
    avgResponseTime.value = flywheelResponseTime.value;
  }
};

// 获取电池健康状态类名
const getBatteryHealthClass = () => {
  if (batteryHealth.value >= 90) return 'normal';
  if (batteryHealth.value >= 70) return 'warning';
  return 'danger';
};

// 获取电池温度状态类名
const getBatteryTempClass = () => {
  if (batteryTemperature.value <= 35) return 'normal';
  if (batteryTemperature.value <= 45) return 'warning';
  return 'danger';
};

// 获取飞轮温度状态类名
const getFlywheelTempClass = () => {
  if (flywheelTemperature.value <= 40) return 'normal';
  if (flywheelTemperature.value <= 55) return 'warning';
  return 'danger';
};

// 更新电池剩余时间
const updateBatteryRemainingTime = () => {
  if (batteryPower.value < 0) {
    // 放电
    const hoursRemaining = (batterySOC.value / 100) * 5 / Math.abs(batteryPower.value);
    const hours = Math.floor(hoursRemaining);
    const minutes = Math.floor((hoursRemaining - hours) * 60);
    batteryRemainingTime.value = `${hours}小时${minutes}分钟`;
  } else if (batteryPower.value > 0) {
    // 充电
    const hoursToFull = ((100 - batterySOC.value) / 100) * 5 / batteryPower.value;
    const hours = Math.floor(hoursToFull);
    const minutes = Math.floor((hoursToFull - hours) * 60);
    batteryRemainingTime.value = `充满剩余${hours}小时${minutes}分钟`;
  } else {
    // 无功率
    batteryRemainingTime.value = '静置';
  }
};

// 初始化和更新图表
const initChart = () => {
  if (frequencyChartRef.value) {
    frequencyChart = echarts.init(frequencyChartRef.value);
    updateChart();
  }
  
  if (batteryChartRef.value) {
    batteryChart = echarts.init(batteryChartRef.value);
    updateBatteryChart();
  }
  
  if (flywheelChartRef.value) {
    flywheelChart = echarts.init(flywheelChartRef.value);
    updateFlywheelChart();
  }
};

const updateChart = () => {
  if (!frequencyChart) return;
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['电网频率', '电池功率', '飞轮功率'],
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: historyTimeLabels.value,
      axisLine: {
        lineStyle: {
          color: '#8ecbff'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '频率(Hz)',
        min: 49.7,
        max: 50.3,
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5470C6'
          }
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff'
        }
      },
      {
        type: 'value',
        name: '功率(MW)',
        min: -10,
        max: 10,
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#91CC75'
          }
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff'
        }
      }
    ],
    series: [
      {
        name: '电网频率',
        type: 'line',
        yAxisIndex: 0,
        data: frequencyHistory.value,
        lineStyle: {
          width: 2
        },
        markLine: {
          silent: true,
          lineStyle: {
            color: '#fff',
            opacity: 0.5
          },
          data: [
            {
              yAxis: 50,
              label: {
                formatter: '额定频率',
                color: '#fff'
              }
            },
            {
              yAxis: 50 + deadband.value,
              label: {
                formatter: '上死区',
                color: '#fff'
              }
            },
            {
              yAxis: 50 - deadband.value,
              label: {
                formatter: '下死区',
                color: '#fff'
              }
            }
          ]
        }
      },
      {
        name: '电池功率',
        type: 'line',
        yAxisIndex: 1,
        data: powerHistory.value.map(item => item.battery),
        lineStyle: {
          width: 2
        }
      },
      {
        name: '飞轮功率',
        type: 'line',
        yAxisIndex: 1,
        data: powerHistory.value.map(item => item.flywheel),
        lineStyle: {
          width: 2
        }
      }
    ]
  };
  
  frequencyChart.setOption(option);
};

// 更新电池状态图表
const updateBatteryChart = () => {
  if (!batteryChart) return;
  
  const option = {
    animation: false,
    grid: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5,
      show: false
    },
    xAxis: {
      type: 'category',
      data: historyTimeLabels.value,
      show: false
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      show: false
    },
    series: [
      {
        data: batterySOCHistory.value,
        type: 'line',
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#00ff75'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 255, 117, 0.5)' },
              { offset: 1, color: 'rgba(0, 255, 117, 0.1)' }
            ]
          }
        }
      }
    ]
  };
  
  batteryChart.setOption(option);
};

// 更新飞轮状态图表
const updateFlywheelChart = () => {
  if (!flywheelChart) return;
  
  const option = {
    animation: false,
    grid: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5,
      show: false
    },
    xAxis: {
      type: 'category',
      data: historyTimeLabels.value,
      show: false
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 6000,
      show: false
    },
    series: [
      {
        data: flywheelSpeedHistory.value,
        type: 'line',
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#01c2ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(1, 194, 255, 0.5)' },
              { offset: 1, color: 'rgba(1, 194, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  };
  
  flywheelChart.setOption(option);
};

// 响应式调整图表大小
const handleResize = () => {
  if (frequencyChart) {
    frequencyChart.resize();
  }
  if (batteryChart) {
    batteryChart.resize();
  }
  if (flywheelChart) {
    flywheelChart.resize();
  }
};

// 组件生命周期钩子
onMounted(() => {
  // 初始化数据
  for (let i = 0; i < 30; i++) {
    batterySOCHistory.value.push(80);
    flywheelSpeedHistory.value.push(3000);
  }
  
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  stopSimulation();
  window.removeEventListener('resize', handleResize);
  if (frequencyChart) {
    frequencyChart.dispose();
    frequencyChart = null;
  }
  if (batteryChart) {
    batteryChart.dispose();
    batteryChart = null;
  }
  if (flywheelChart) {
    flywheelChart.dispose();
    flywheelChart = null;
  }
});

// 监听控制模式变化
watch(controlMode, () => {
  // 切换到手动模式时，如果系统正在运行，显示提示
  if (controlMode.value === 'manual' && systemRunning.value) {
    message.warning('切换到手动模式，请谨慎操作');
  }
});

// 监听死区变化
watch(deadband, () => {
  updateChart();
});

// 新增：下发自动控制指令
const applyAutoSettings = () => {
  if (!systemRunning.value) {
    message.error('系统未运行，无法下发指令');
    return;
  }
  // 实际下发指令的逻辑，这里仅作提示
  message.success(`自动控制参数已下发：死区 ${deadband.value} Hz, 调节速率 ${regulationRate.value} %/Hz`);
};

// 新增：下发手动控制指令
const applyManualSettings = () => {
  if (!systemRunning.value) {
    message.error('系统未运行，无法下发指令');
    return;
  }
  // 实际下发指令的逻辑，这里仅作提示
  message.success(`手动控制参数已下发：电池功率 ${batteryPowerSetting.value}%, 飞轮功率 ${flywheelPowerSetting.value}%`);
  // 可能需要调用 updatePowerOutput 来立即应用手动设置
  if (controlMode.value === 'manual') {
    updatePowerOutput();
  }
};
</script>

<style lang="scss" scoped>
.frequency-control-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('@/assets/images/index-bg.png') no-repeat;
  background-size: 100% 100%;
  color: #fff;
  overflow: hidden;
  
  .header {
    height: 77px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      
      .back-button {
        margin-right: 16px;
        background: rgba(1, 194, 255, 0.2);
        border: 1px solid rgba(1, 194, 255, 0.3);
        color: #fff;
        
        &:hover {
          background: rgba(1, 194, 255, 0.4);
          color: #fff;
        }
      }
      
      h1 {
        margin: 0;
        font-size: 20px;
        color: #fff;
      }
    }
    
    .control-buttons {
      display: flex;
      gap: 8px;
      
      .ant-btn {
        background: rgba(1, 194, 255, 0.3);
        border: 1px solid rgba(1, 194, 255, 0.5);
        color: #01c2ff;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(1, 194, 255, 0.5);
          box-shadow: 0 0 10px rgba(1, 194, 255, 0.3);
        }
        
        &[type="primary"] {
          background: rgba(0, 255, 117, 0.3);
          border: 1px solid rgba(0, 255, 117, 0.5);
          color: #00ff75;
          
          &:hover {
            background: rgba(0, 255, 117, 0.5);
            box-shadow: 0 0 10px rgba(0, 255, 117, 0.3);
          }
        }
        
        &[danger] {
          background: rgba(255, 110, 118, 0.3);
          border: 1px solid rgba(255, 110, 118, 0.5);
          color: #ff6e76;
          
          &:hover {
            background: rgba(255, 110, 118, 0.5);
            box-shadow: 0 0 10px rgba(255, 110, 118, 0.3);
          }
        }
      }
    }
  }
  
  .content {
    flex: 1;
    display: flex;
    padding: 0 20px 20px;
    gap: 20px;
    overflow: auto;
    min-height: 0;
    
    .left-panel {
      width: 30%;
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .system-status, .control-mode {
        background: rgba(4, 40, 90, 0.5);
        border-radius: 10px;
        padding: 16px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
      
      h2 {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 16px;
        color: #01c2ff;
        padding-left: 10px;
        border-left: 3px solid #2ca6ff;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(1, 194, 255, 0.3);
      }
      
      h3 {
        margin-top: 16px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #fff;
      }
      
      .status-item {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        
        span {
          margin-right: 8px;
          color: #8ecbff;
        }
        
        .value {
          font-weight: bold;
          color: #fff;
          
          &.normal {
            color: #00ff75;
          }
          
          &.warning {
            color: #ffcc00;
          }
          
          &.danger {
            color: #ff6e76;
          }
          
          &.positive {
            color: #00ff75;
          }
          
          &.negative {
            color: #ff6e76;
          }
        }
        
        .frequency-gauge {
          width: 100%;
          height: 8px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          margin-top: 8px;
          
          .gauge-inner {
            height: 100%;
            background-color: #01c2ff;
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }
      }
      
      .parameters, .manual-control {
        margin-top: 16px;
        
        .param-item, .manual-item {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          
          span {
            margin-right: 8px;
            color: #8ecbff;
          }
        }
        
        .manual-item {
          flex-direction: column;
          align-items: flex-start;
          
          .ant-slider {
            width: 100%;
            margin: 8px 0;
          }
          
          .ant-input-number {
            margin-right: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(1, 194, 255, 0.3);
            color: #fff;
            
            &:focus, &-focused {
              border-color: rgba(1, 194, 255, 0.8);
              box-shadow: 0 0 5px rgba(1, 194, 255, 0.5);
            }
            
            .ant-input-number-handler-wrap {
              background: rgba(0, 0, 0, 0.2);
            }
            
            .ant-input-number-input {
              color: #fff;
            }
          }
        }
      }
    }
    
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow: hidden;
      min-height: 0;
      
      .storage-status {
        background: rgba(4, 40, 90, 0.5);
        border-radius: 10px;
        padding: 16px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        flex: 2 1 0;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        min-height: 0;
      }
      
      h2 {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 16px;
        color: #01c2ff;
        padding-left: 10px;
        border-left: 3px solid #2ca6ff;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(1, 194, 255, 0.3);
      }
      
      .storage-status {
        display: flex;
        flex-direction: column;
        
        .storage-unit {
          margin-bottom: 16px;
          background: rgba(1, 194, 255, 0.05);
          border-radius: 8px;
          padding: 12px;
          
          h3 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 14px;
            color: #fff;
          }
          
          .battery-main-content, .flywheel-main-content {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
          }
          
          .battery-container, .flywheel-container {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 200px;
            
            .battery-icon, .flywheel-icon {
              margin-right: 16px;
              width: 60px;
              height: 100px;
              display: flex;
              justify-content: center;
              align-items: center;
              
              .battery-level {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                background-color: #01c2ff;
                transition: height 0.3s ease;
              }
            }
            
            .battery-icon {
              border: 2px solid #01c2ff;
              border-radius: 4px;
              position: relative;
            }
            
            .flywheel-icon {
              svg {
                width: 100%;
                height: 100%;
              }
              
              &.spinning {
                animation: spin 2s linear infinite;
              }
            }
            
            .battery-info, .flywheel-info {
              flex: 1;
              
              .info-item {
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                
                span {
                  margin-right: 8px;
                  color: #8ecbff;
                }
                
                .value {
                  font-weight: bold;
                  color: #fff;
                  
                  &.positive {
                    color: #00ff75;
                  }
                  
                  &.negative {
                    color: #ff6e76;
                  }
                }
              }
            }
          }
          
          .battery-chart-container, .flywheel-chart-container {
            flex: 1;
            min-width: 150px;
            height: 150px;
            background: rgba(4, 21, 48, 0.5);
            border-radius: 6px;
            padding: 4px;
            border: 1px solid rgba(1, 194, 255, 0.2);
            
            .mini-chart {
              height: 100%;
              width: 100%;
              border-radius: 4px;
            }
          }
          
          .battery-chart-container {
            box-shadow: 0 0 8px rgba(0, 255, 117, 0.1);
          }
          
          .flywheel-chart-container {
            box-shadow: 0 0 8px rgba(1, 194, 255, 0.1);
          }
          
          .battery-status-bar, .flywheel-status-bar {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px dashed rgba(1, 194, 255, 0.2);
            
            .status-item {
              display: flex;
              align-items: center;
              
              span {
                margin-right: 8px;
                color: #8ecbff;
                font-size: 12px;
              }
              
              .value {
                font-weight: bold;
                color: #fff;
                font-size: 12px;
                
                &.normal {
                  color: #00ff75;
                }
                
                &.warning {
                  color: #ffcc00;
                }
                
                &.danger {
                  color: #ff6e76;
                }
              }
            }
          }
          
          .control-panel {
            margin-top: 8px;
            display: flex;
            justify-content: flex-end;
            
            .ant-btn {
              background: rgba(1, 194, 255, 0.3);
              border: 1px solid rgba(1, 194, 255, 0.5);
              color: #01c2ff;
              font-size: 12px;
              padding: 2px 8px;
              height: auto;
              
              &:hover {
                background: rgba(1, 194, 255, 0.5);
                box-shadow: 0 0 10px rgba(1, 194, 255, 0.3);
              }
              
              &[type="primary"] {
                background: rgba(0, 255, 117, 0.3);
                border: 1px solid rgba(0, 255, 117, 0.5);
                color: #00ff75;
                
                &:hover {
                  background: rgba(0, 255, 117, 0.5);
                  box-shadow: 0 0 10px rgba(0, 255, 117, 0.3);
                }
              }
            }
          }
        }
        
        .storage-metrics {
          margin-top: 8px;
          background: rgba(1, 194, 255, 0.05);
          border-radius: 8px;
          padding: 12px;
          
          h3 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 14px;
            color: #fff;
          }
          
          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            
            .metric-item {
              background: rgba(4, 21, 48, 0.5);
              padding: 10px;
              border-radius: 6px;
              
              .metric-title {
                font-size: 12px;
                color: #8ecbff;
                margin-bottom: 5px;
              }
              
              .metric-value {
                font-size: 16px;
                font-weight: bold;
                color: #fff;
                
                &.positive {
                  color: #00ff75;
                }
                
                &.negative {
                  color: #ff6e76;
                }
              }
            }
          }
        }
      }
      
      .frequency-chart {
        flex: 1 1 0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: rgba(4, 40, 90, 0.5);
        border-radius: 10px;
        .chart-container {
          flex: 1;
          width: 100%;
          min-height: 0;
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .frequency-control-container {
    .content {
      flex-direction: column;
      padding: 0 10px 10px;
      
      .left-panel {
        width: 100%;
        margin-bottom: 10px;
      }
      
      .right-panel {
        gap: 10px;
      }
    }
  }
}
</style> 