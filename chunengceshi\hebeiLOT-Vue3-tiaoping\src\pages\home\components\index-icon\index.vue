<template>
	<div class="index-num">
		<a-row class="index-row-value">
			<a-col class="index-col">
				<img src="@/assets/images/power-grid.png" class="img-icon" alt="" />
				<a-col class="index-col">{{ loadPower }}</a-col>
				<a-col class="type">负载功率（kW）</a-col>
			</a-col>

			<a-col class="index-col">
				<img src="@/assets/images/power-station.png" class="img-icon" alt="" />
				<a-col class="index-col">{{ gridPower }}</a-col>
				<a-col class="type">并网功率（kW）</a-col>
			</a-col>
			<a-col class="index-col">
				<img src="@/assets/images/serve.png" class="img-icon" alt="" />
				<a-col class="index-col">{{ storagePower }}</a-col>
				<a-col class="type">储能功率（kW）</a-col>
			</a-col>
		</a-row>
	</div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';

// 定义 props
const props = defineProps({
	loadPower: {
		type: Number,
		default: 0
	},
	gridPower: {
		type: Number,
		default: 0
	},
	storagePower: {
		type: Number,
		default: 0
	}
});
</script>

<style lang="scss" scoped>
.type {
	color: #02a6b5;
	margin: auto;
	font-size: 15px;
}
.img-icon {
	width: 80px;
	height: 80px;
}
@media (max-width: 480px) {
	.index-num {
		margin-top: -30px;
	}

	.unit {
		font-size: 12px;
	}
}

@font-face {
	font-family: electronicFont;
	src: url('@/assets/font/DS-DIGIT.TTF');
}

.unit {
	font-size: 1rem;
}

.index-num {
	background-color: #0f2159;
	border-radius: 4px;

	.index-row-value {
		position: relative;
		padding: 8px 0;
		border: 1px solid rgb(255 255 255 / 20%);

		.index-col {
			flex: 1;
			font-family: electronicFont;
			font-size: 1.8rem;
			color: #ffeb7b;
			text-align: center;
		}

		.index-divider {
			height: unset;
			margin-block: 20px;
			background: rgb(255 255 255 / 20%);
		}

		@mixin border-conrner {
			position: absolute;
			width: 30px;
			height: 15px;
			content: '';
		}

		&::before {
			@include border-conrner;

			top: 0;
			left: 0;
			border-top: 2px solid #02a6b5;
			border-left: 2px solid #02a6b5;
		}

		&::after {
			@include border-conrner;

			right: 0;
			bottom: 0;
			border-right: 2px solid #02a6b5;
			border-bottom: 2px solid #02a6b5;
		}
	}

	.index-row-label {
		color: rgb(255 255 255 / 70%);

		.index-col {
			flex: 1;
			margin: 8px 0;
			font-size: 16px;
			text-align: center;
		}
	}
}
</style>
