import { GET, POST, PUT, DELETE } from '@/service/api';

// 获取所有电池信息
export const getAllBatterySimulations = () => {
  return GET('/batteries');
};

// 获取所有工步信息
export const getAllSteps = () => {
  return GET('/steps');
};

// 根据ID获取工步设计
export const getStepDesignById = (id) => {
  return GET(`/steps/${id}`);
};

// 创建新工步设计
export const createStepDesign = (stepData) => {
  return POST('/steps', stepData);
};

// 更新工步设计信息
export const updateStepDesign = (id, stepData) => {
  return PUT(`/steps/${id}`, stepData);
};

// 删除工步设计
export const deleteStepDesign = (id) => {
  return DELETE(`/steps/${id}`);
};
