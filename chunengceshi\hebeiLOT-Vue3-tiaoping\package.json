{"name": "pro_data_web", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": " vite build", "preview": "vite preview", "prepare": "husky install", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:lint-staged": "lint-staged", "commit": "git-cz"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@types/lodash-es": "^4.17.7", "@types/node": "^18.16.2", "@types/qs": "^6.9.8", "ant-design-vue": "^3.2.20", "axios": "^1.4.0", "date-fns": "^3.6.0", "dayjs": "^1.11.7", "echarts": "^5.4.2", "gsap": "^3.12.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "pinia": "^2.0.11", "postcss-pxtorem": "^6.0.0", "qs": "^6.11.2", "vant": "^4.9.1", "vue": "3.2.x", "vue-router": "^4.1.6"}, "devDependencies": {"@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^4.1.0", "@vue/runtime-dom": "^3.3.4", "commitizen": "^4.3.0", "commitlint": "^17.6.1", "cz-git": "^1.6.1", "esbuild": "^0.18.20", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.11.0", "husky": "^8.0.1", "lint-staged": "^13.0.1", "postcss": "^8.4.14", "postcss-html": "^1.4.1", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.52.3", "sass-loader": "^13.0.0", "stylelint": "^14.9.1", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-recommended-scss": "^6.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^26.0.0", "stylelint-config-standard-scss": "^4.0.0", "typescript": "^5.0.2", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.24.1", "vite": "^4.3.2", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^1.4.2"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}