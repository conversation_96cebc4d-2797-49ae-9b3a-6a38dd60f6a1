<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="400px" viewBox="0 0 800 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Electric Car</title>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Car Body -->
        <path d="M600,200 C600,144.771525 555.228475,100 500,100 L300,100 C244.771525,100 200,144.771525 200,200 L150,250 L100,250 C77.90861,250 60,267.90861 60,290 L60,310 C60,332.09139 77.90861,350 100,350 L700,350 C722.09139,350 740,332.09139 740,310 L740,290 C740,267.90861 722.09139,250 700,250 L650,250 L600,200 Z" fill="#333333"></path>
        
        <!-- Windows -->
        <path d="M550,200 L500,130 L300,130 L250,200 L220,250 L580,250 L550,200 Z" fill="#99CCFF"></path>
        
        <!-- Headlight -->
        <circle cx="180" cy="220" r="20" fill="#FFCC00"></circle>
        
        <!-- Taillight -->
        <rect x="620" y="220" width="40" height="20" rx="10" fill="#FF0000"></rect>
        
        <!-- Wheel 1 -->
        <circle cx="200" cy="330" r="50" fill="#111111"></circle>
        <circle cx="200" cy="330" r="35" fill="#444444"></circle>
        <circle cx="200" cy="330" r="20" fill="#666666"></circle>
        <circle cx="200" cy="330" r="5" fill="#000000"></circle>
        
        <!-- Wheel 2 -->
        <circle cx="600" cy="330" r="50" fill="#111111"></circle>
        <circle cx="600" cy="330" r="35" fill="#444444"></circle>
        <circle cx="600" cy="330" r="20" fill="#666666"></circle>
        <circle cx="600" cy="330" r="5" fill="#000000"></circle>
        
        <!-- Battery Indicator -->
        <rect x="350" y="260" width="100" height="30" rx="15" fill="#333333" stroke="#00FF99" stroke-width="2"></rect>
        <rect x="355" y="265" width="90" height="20" rx="10" fill="#00FF99"></rect>
        
        <!-- Charging Lightning Bolt -->
        <polygon points="700,150 680,200 700,200 660,250 680,200 660,200" fill="#FFCC00" stroke="#FFFFFF" stroke-width="2"></polygon>
    </g>
</svg> 