<!-- PcsLog.vue -->
<template>
	<div class="right box">
		<div class="title">{{ tableTitle }}</div>
		<div class="edit">
			<div class="edit-left">
				<a-row>
					<a-col :span="7">
						<span>模拟记录：</span>
						<a-select
							v-model:value="selectedLog"
							style="width: 200px; margin-left: 8px"
							@change="handleLogTypeChange"
							:options="logTypeOptions"
							option-label-prop="name"
							option-value-prop="id"
							:placeholder="'请选择模拟记录'"
						>
							<a-select-option
								v-for="log in logTypeOptions"
								:key="log.id"
								:value="log"
							>
							{{ log.name }}
							</a-select-option>
						</a-select>
					</a-col>

					<a-col :offset="1" :span="8">
						<span>设备选择:</span>
						<a-select
							v-model:value="deviceType"
							style="width: 200px; margin-left: 8px"
							@change="handleDeviceChange"
						>
							<a-select-option value="PCS">PCS</a-select-option>
							<a-select-option value="BMS">BMS</a-select-option>
						</a-select>
					</a-col>

					<a-col :offset="1" :span="5">
						<span>顺序:</span>
						<a-select
							v-model:value="sortOrder"
							style="width: 120px; margin-left: 8px"
							@change="handleSortChange"
						>
							<a-select-option value="desc">时间倒序</a-select-option>
							<a-select-option value="asc">时间正序</a-select-option>
						</a-select>
					</a-col>
				</a-row>
			</div>
			<div class="edit-right">
				<div class="edit-right-item">
					<DownloadOutlined
						style="font-size: 32px; color: #fff"
						@click="handleDownload"
					/>
				</div>
			</div>
		</div>
		<div class="table">
			<a-table
				:dataSource="dataSource"
				:columns="columns"
				:rowKey="record => record.id"
				:scroll="{ x: 3500, y: 550 }"
				:pagination="false"
			>
			</a-table>
		</div>
		<a-pagination
			:current="pagination.current"
			:total="pagination.total"
			:page-size="pagination.pageSize"
			@change="handlePageChange"
			@show-size-change="handlePageSizeChange"
			show-size-changer
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { getPCSLogs, getBMSLogs, downloadPCSLogs, downloadBMSLogs, getNonRunningRecords } from './service';
import message from 'ant-design-vue/es/message';

const tableTitle = ref('模拟记录');
const dataSource = ref([]);
const logTypeOptions = ref([]);
const selectedLog = ref(null);
const deviceType = ref('PCS');
const sortOrder = ref('desc');
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0
});
const formatDateTime = (datetime: string) => {
	console.log(datetime)
	return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};
const bmsFieldComments = {
    voltage: '电压 (V)',
    current: '电流 (A)',
    chargeCurrentLimit: '充电电流限制 (A)',
    dischargeCurrentLimit: '放电电流限制 (A)',
    chargeVoltageLimit: '充电电压限制 (V)',
    dischargeVoltageLimit: '放电电压限制 (V)',
    chargeableCapacity: '可充电容量 (Ah)',
    dischargeableCapacity: '可放电容量 (Ah)',
    bmsStatus: 'BMS状态',
    bmsHeartbeat: 'BMS心跳信号',
    sop: 'SOP',
    maxVoltage: '最高电压 (V)',
    minVoltage: '最低电压 (V)',
    maxTemperature: '最高温度 (°C)',
    minTemperature: '最低温度 (°C)',
	soc:'SOC'
};

const switchTable = async () => {
	if (!selectedLog.value) {
		return; // 如果没有选中的模拟记录，直接返回
	}

	const params = {
		page: pagination.current,
		pageSize: pagination.pageSize,
		startTime: selectedLog.value.startTime,
		endTime: selectedLog.value.endTime,
		sortOrder: sortOrder.value
	};

	let res;
	if (deviceType.value === 'PCS') {
		res = await getPCSLogs(params);
	} else {
		res = await getBMSLogs(params);
	}
	console.log(res.data)
	res= res.data
	if (res && res.data) {
		dataSource.value = res.data.logs.map(log => ({
			...log,
			created_date: dayjs(log.created_at).format('YYYY-MM-DD'),
			created_time: dayjs(log.created_at).format('HH:mm:ss.SSS')
		}));
		pagination.total = res.data.totalLogs;
		pagination.pageSize = res.data.pageSize;
	}
};

const getNotRuningInit = async () => {
	const res = await getNonRunningRecords();

	if (res && res.data && res.data.data.length > 0) {
		logTypeOptions.value = res.data.data.map(log => ({
			...log,
			label: formatDateTime(log.name),
			value: log.id
		}));
		// 默认选择第一个
		selectedLog.value = logTypeOptions.value[0];
		switchTable(); // 初始化表格数据
	} else {
		// 如果没有获取到数据，设置 selectedLog 为 null，停止后续的数据请求
		selectedLog.value = null;
		dataSource.value = []; // 清空表格数据
		pagination.total = 0;
	}
};

onMounted(() => {
	getNotRuningInit();
});

const handleLogTypeChange = (log: any) => {
	selectedLog.value = log;
	switchTable();
};

const handleDeviceChange = (value: string) => {
	deviceType.value = value;
	switchTable();
};

const handleSortChange = (value: string) => {
	sortOrder.value = value;
	switchTable();
};

const handlePageSizeChange = (current: number, size: number) => {
	pagination.pageSize = size;
	pagination.current = current;
	switchTable();
};

const handlePageChange = (page: number) => {
	pagination.current = page;
	switchTable();
};

const handleDownload = async () => {
	if (!selectedLog.value) {
		message.error('请选择有效的模拟记录');
		return;
	}

	try {
		const queryParams: any = {
			sortOrder: sortOrder.value,
			startTime: selectedLog.value.startTime,
			endTime: selectedLog.value.endTime
		};

		if (deviceType.value === 'PCS') {
			await downloadPCSLogs(queryParams);
		} else {
			await downloadBMSLogs(queryParams);
		}
	} catch (error) {
		message.error('下载日志失败');
	}
};

watch(selectedLog, () => {
	switchTable();
});

const pcsColumns = [
	{ title: '记录日期', dataIndex: 'created_date', key: 'created_date', width: 150 },
	{ title: '记录时间', dataIndex: 'created_time', key: 'created_time', width: 150 },
	{ title: '数据头', dataIndex: 'perx', key: 'perx' },
	{ title: '交流电网电压A (0.1V)', dataIndex: 'grid_ua', key: 'grid_ua' },
	{ title: '交流电网电压B (0.1V)', dataIndex: 'grid_ub', key: 'grid_ub' },
	{ title: '交流电网电压C (0.1V)', dataIndex: 'grid_uc', key: 'grid_uc' },
	{ title: '交流电流Ia (0.01A)', dataIndex: 'grid_ia', key: 'grid_ia' },
	{ title: '交流电流Ib (0.01A)', dataIndex: 'grid_ib', key: 'grid_ib' },
	{ title: '交流电流Ic (0.01A)', dataIndex: 'grid_ic', key: 'grid_ic' },
	{ title: '电网频率 (0.01Hz)', dataIndex: 'grid_fre', key: 'grid_fre' },
	{ title: '电网功率因数', dataIndex: 'grid_pf', key: 'grid_pf' },
	{ title: '交流功率总和', dataIndex: 'p_ac_sum', key: 'p_ac_sum' },
	{ title: '直流母线电压 (0.1V)', dataIndex: 'vp_bus', key: 'vp_bus' },
	{ title: '母线电压 (0.1V)', dataIndex: 'vm_bus', key: 'vm_bus' },
	{ title: '直流电流 (0.1A)', dataIndex: 'i_dc', key: 'i_dc' },
	{ title: '直流功率总和', dataIndex: 'p_dc_sum', key: 'p_dc_sum' },
	{ title: '逆变器温度 (°C)', dataIndex: 'temp_inverter', key: 'temp_inverter' },
	{ title: '散热器1温度 (°C)', dataIndex: 'temp_fin1', key: 'temp_fin1' },
	{ title: '散热器2温度 (°C)', dataIndex: 'temp_fin2', key: 'temp_fin2' },
	{ title: '交流泄漏电流 (mA)', dataIndex: 'iac_leak', key: 'iac_leak' },
	{ title: 'PI电流设定值', dataIndex: 'pi_i_set', key: 'pi_i_set' },
	{ title: 'D轴电压 (V)', dataIndex: 'd_v', key: 'd_v' },
	{ title: 'Q轴电压 (V)', dataIndex: 'q_v', key: 'q_v' },
	{ title: 'D轴电流 (A)', dataIndex: 'd_i', key: 'd_i' },
	{ title: 'Q轴电流 (A)', dataIndex: 'q_i', key: 'q_i' },
	{ title: '相位', dataIndex: 'phase', key: 'phase' },
	{ title: '启动状态', dataIndex: 'start_status', key: 'start_status' },
	{ title: '维护状态', dataIndex: 'maintain_status', key: 'maintain_status' }
];

const bmsColumns = [
{ title: '记录日期', dataIndex: 'created_date', key: 'created_date', width: 150 },
    { title: '记录时间', dataIndex: 'created_time', key: 'created_time', width: 150 },
    { title: bmsFieldComments.voltage, dataIndex: 'voltage', key: 'voltage' },
    { title: bmsFieldComments.current, dataIndex: 'current', key: 'current' },
    { title: bmsFieldComments.chargeCurrentLimit, dataIndex: 'charge_current_limit', key: 'charge_current_limit' },
    { title: bmsFieldComments.dischargeCurrentLimit, dataIndex: 'discharge_current_limit', key: 'discharge_current_limit' },
    { title: bmsFieldComments.chargeVoltageLimit, dataIndex: 'charge_voltage_limit', key: 'charge_voltage_limit' },
    { title: bmsFieldComments.dischargeVoltageLimit, dataIndex: 'discharge_voltage_limit', key: 'discharge_voltage_limit' },
    { title: bmsFieldComments.chargeableCapacity, dataIndex: 'chargeable_capacity', key: 'chargeable_capacity' },
    { title: bmsFieldComments.dischargeableCapacity, dataIndex: 'dischargeable_capacity', key: 'dischargeable_capacity' },
    { title: bmsFieldComments.bmsStatus, dataIndex: 'bms_status', key: 'bms_status' },
    { title: bmsFieldComments.bmsHeartbeat, dataIndex: 'bms_heartbeat', key: 'bms_heartbeat' },
    { title: bmsFieldComments.sop, dataIndex: 'sop', key: 'sop' },
	{ title: bmsFieldComments.soc, dataIndex: 'soc', key: 'soc' },
    { title: bmsFieldComments.maxVoltage, dataIndex: 'max_voltage', key: 'max_voltage' },
    { title: bmsFieldComments.minVoltage, dataIndex: 'min_voltage', key: 'min_voltage' },
    { title: bmsFieldComments.maxTemperature, dataIndex: 'max_temperature', key: 'max_temperature' },
    { title: bmsFieldComments.minTemperature, dataIndex: 'min_temperature', key: 'min_temperature' }
];

const columns = computed(() => {
	return deviceType.value === 'PCS' ? pcsColumns : bmsColumns;
});

switchTable();
</script>



<style scoped>
.right.box {
	padding: 16px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.title {
	color: #fff;
	border-left: #2ca6ff 3px solid;
	padding-left: 10px;
	margin-top: 10px;
	margin-left: 10px;
	font-size: 18px;
	margin-bottom: 20px;
}

.edit {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16px;
}

.edit-left {
	display: flex;
	align-items: center;
	color: #fff;
}

.edit-right {
	display: flex;
	align-items: center;
}

.edit-right-item {
	cursor: pointer;
}

.table {
	margin-top: 16px;
}

a-table {
	background: #fff;
}
</style>
<style lang="scss">
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
	color: #fff !important;
}

.ant-pagination {
	display: flex;
	margin-top: 1.25rem;
	justify-content: flex-end;
	color: #fff;
}

.ant-tabs-tab-btn {
	color: #fff;
}

.ant-tooltip-inner {
	min-height: unset;
}

.ant-form-item-label {
	text-align: left;
}

.ant-table-thead > tr > th {
	color: #fff;
	background-color: rgba($color: #2ca6ff, $alpha: 0.5);
}

.ant-table-tbody {
	color: #fff;
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table-tbody > tr.ant-table-row:hover > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.7);
}

.ant-table-tbody > tr.ant-table-row > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table {
	background: none !important;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
