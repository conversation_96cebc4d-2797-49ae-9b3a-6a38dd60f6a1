<template>
	<div class="home">
		<transition-loading :isShow="loadShow" />
		<div class="chart-list">
			<home-header />
			<div style="padding: 0 8px" class="chart-content">
				<div class="full-box">
					<!-- 主要内容区域 -->
					<div class="main-content">
						<!-- 智能算法演示模块 -->
						<div class="algorithm-demo-section">
							<div class="section-title">智能算法<span class="collaboration-tag">河北工业大学智能学院合作项目</span></div>
							<div class="algorithm-container">
								<div class="algorithm-selector">
									<div class="selector-title">选择算法模型：</div>
									<div class="selector-options">
										<div class="algorithm-option" :class="{ active: selectedAlgorithm === 'lifePredict' }" @click="selectAlgorithm('lifePredict')">
											<div class="option-icon">📊</div>
											<div class="option-name">寿命预测</div>
										</div>
										<div class="algorithm-option" :class="{ active: selectedAlgorithm === 'thermalManage' }" @click="selectAlgorithm('thermalManage')">
											<div class="option-icon">🌡️</div>
											<div class="option-name">热管理</div>
										</div>
										<div class="algorithm-option" :class="{ active: selectedAlgorithm === 'balanceOptimize' }" @click="selectAlgorithm('balanceOptimize')">
											<div class="option-icon">⚖️</div>
											<div class="option-name">均衡优化</div>
										</div>
										<div class="algorithm-option" :class="{ active: selectedAlgorithm === 'faultDiagnosis' }" @click="selectAlgorithm('faultDiagnosis')">
											<div class="option-icon">🔍</div>
											<div class="option-name">故障诊断</div>
										</div>
									</div>
								</div>
								
								<div class="algorithm-content">
									<!-- 寿命预测算法 -->
									<div v-if="selectedAlgorithm === 'lifePredict'" class="algorithm-detail">
										<div class="detail-header">
											<div class="detail-title">深度学习电池寿命预测</div>
											<div class="detail-description">基于历史循环数据预测电池剩余使用寿命</div>
										</div>
										
										<!-- 添加寿命预测算法介绍 -->
										<div class="algorithm-intro">
											<div class="algorithm-title">算法介绍</div>
											<div class="algorithm-description">
												<p>寿命预测算法采用层次化LSTM神经网络结构，通过对电池历史循环数据的深度分析，准确预测剩余使用寿命。系统考虑充放电曲线特征、温度变化、内阻增长等多维指标，建立全面的健康状态评估模型。</p>
												<p>算法优势：</p>
												<p>- 多特征融合：集成电化学特性与使用环境因素，提高预测准确性</p>
												<p>- 早期预警：可在电池容量下降到80%前提前预测衰减趋势</p>
												<p>- 个性化适应：根据具体使用场景自动调整预测模型参数</p>
											</div>
										</div>
										
										<div class="detail-visualization">
											<div class="chart-container">
												<div ref="lifePredictChartRef" class="echarts-container"></div>
											</div>
											<div class="chart-info">
												<div class="info-item">
													<div class="info-label">当前循环:</div>
													<div class="info-value">{{ batteryGroupData.cycleCount }}次</div>
												</div>
												<div class="info-item">
													<div class="info-label">预计剩余寿命:</div>
													<div class="info-value">{{ predictedLifeMonths }}个月</div>
												</div>
												<div class="info-item">
													<div class="info-label">健康评分:</div>
													<div class="info-value">{{ algorithmData.healthScore }}/100</div>
												</div>
											</div>
										</div>
										<div class="detail-controls">
											<!-- <button class="control-button primary" @click="runPredictionAlgorithm">运行预测算法</button> -->
											<!-- <button class="control-button" @click="showAlgorithmDetails = !showAlgorithmDetails">
												{{ showAlgorithmDetails ? '隐藏' : '显示' }}算法详情
											</button> -->
										</div>
										<div v-if="showAlgorithmDetails" class="algorithm-details">
											<div class="detail-method">
												<div class="method-title">算法方法</div>
												<div class="method-content">
													<p>利用LSTM神经网络对电池健康状态进行预测，基于以下参数：</p>
													<ul>
														<li>电压衰减曲线</li>
														<li>内阻变化趋势</li>
														<li>充放电效率</li>
														<li>温度响应特性</li>
													</ul>
												</div>
											</div>
											<div class="detail-parameters">
												<div class="parameter-title">关键参数</div>
												<div class="parameter-table">
													<div class="parameter-row">
														<div class="parameter-name">初始容量</div>
														<div class="parameter-value">{{ batteryGroupData.fullCapacity }}Ah</div>
													</div>
													<div class="parameter-row">
														<div class="parameter-name">当前容量</div>
														<div class="parameter-value">{{ batteryGroupData.remainingCapacity }}Ah</div>
													</div>
													<div class="parameter-row">
														<div class="parameter-name">容量衰减率</div>
														<div class="parameter-value">{{ algorithmData.capacityFadeRate }}%/100次循环</div>
													</div>
													<div class="parameter-row">
														<div class="parameter-name">平均放电深度</div>
														<div class="parameter-value">{{ algorithmData.avgDischargeDepth }}%</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									
									<!-- 热管理算法 -->
									<div v-if="selectedAlgorithm === 'thermalManage'" class="algorithm-detail">
										<div class="detail-header">
											<div class="detail-title">智能热管理系统</div>
											<div class="detail-description">基于神经网络的电池温度预测与热管理策略优化</div>
										</div>
										
										<!-- 添加热管理算法介绍 -->
										<div class="thermal-management-intro">
											<div class="algorithm-title">算法介绍</div>
											<div class="algorithm-description">
												<p>热管理算法利用卷积神经网络(CNN)和长短期记忆网络(LSTM)构建热分布预测模型，实现电池组温度场的实时监测与预测。该系统可根据预测结果，自动调整冷却策略，防止电池过热和热失控。</p>
												<p>技术特点：</p>
												<p>- 温度场建模：采用三维热扩散模型，精确模拟电池组内部热传导</p>
												<p>- 热点预警：提前5-10分钟预测可能形成的热点位置，实现主动散热</p>
												<p>- 智能冷却：根据负载和环境条件，动态优化冷却功率分配，降低能耗</p>
											</div>
										</div>
										
										<div class="detail-visualization">
											<div class="chart-container">
												<div ref="thermalChartRef" class="echarts-container"></div>
											</div>
											<div class="chart-info">
												<div class="info-item">
													<div class="info-label">平均温度:</div>
													<div class="info-value">{{ thermalData.avgTemp }}°C</div>
												</div>
												<div class="info-item">
													<div class="info-label">最高温度:</div>
													<div class="info-value" :class="{'warning': thermalData.maxTemp > 40}">{{ thermalData.maxTemp }}°C</div>
												</div>
												<div class="info-item">
													<div class="info-label">温差:</div>
													<div class="info-value" :class="{'warning': thermalData.tempDiff > 5}">{{ thermalData.tempDiff }}°C</div>
												</div>
												<div class="info-item">
													<div class="info-label">冷却状态:</div>
													<div class="info-value">{{ thermalData.coolingStatus }}</div>
												</div>
											</div>
										</div>
										<div class="detail-controls">
											<!-- <button class="control-button primary" @click="optimizeCooling">优化冷却策略</button> -->
											<button class="control-button" :class="{'active': thermalData.coolingActive}" @click="toggleCooling">
												{{ thermalData.coolingActive ? '关闭冷却' : '启动冷却' }}
											</button>
											<button class="control-button" @click="predictHotspots">预测热点</button>
										</div>
										<div v-if="thermalData.showPrediction" class="prediction-result">
											<div class="prediction-title">热点预测结果</div>
											<div class="prediction-content">
												<p>系统预测在当前使用模式下，电池组可能在以下位置形成热点:</p>
												<div class="hotspot-markers">
													<div class="hotspot-marker" v-for="(spot, index) in thermalData.hotspots" :key="index">
														电池{{ spot.cell }}号 - {{ spot.probability }}% 概率
													</div>
												</div>
												<div class="prediction-recommendation">
													<strong>建议措施:</strong> {{ thermalData.recommendation }}
												</div>
											</div>
										</div>
									</div>
									
									<!-- 均衡优化算法 -->
									<div v-if="selectedAlgorithm === 'balanceOptimize'" class="algorithm-detail">
										<div class="detail-header">
											<div class="detail-title">智能均衡优化系统</div>
											<div class="detail-description">基于强化学习的电池均衡策略与充放电优化</div>
										</div>
										
										<!-- 添加均衡优化算法介绍 -->
										<div class="balancing-optimization-intro">
											<div class="algorithm-title">算法介绍</div>
											<div class="algorithm-description">
												<p>均衡优化算法采用深度强化学习(DRL)框架，通过构建马尔可夫决策过程模型，实现电池组充放电过程中的最优化控制。系统能够根据电池单体状态差异，动态调整均衡策略，降低能量损耗。</p>
												<p>核心优势：</p>
												<p>- 时序优化：基于Q-learning的时序决策，确定每个电池单体最佳均衡时间点</p>
												<p>- 能量回收：采用双向均衡技术，将高电量电池能量转移到低电量电池，减少能量损耗</p>
												<p>- 寿命延长：通过减少电池组内的电压/容量差异，显著延长整体使用寿命</p>
											</div>
										</div>
										
										<div class="detail-visualization">
											<div class="chart-container">
												<div ref="balanceChartRef" class="echarts-container"></div>
											</div>
											<div class="chart-info">
												<div class="info-item">
													<div class="info-label">均衡中电池:</div>
													<div class="info-value">{{ balanceData.activeBalancing }} 个</div>
												</div>
												<div class="info-item">
													<div class="info-label">均衡效率:</div>
													<div class="info-value">{{ balanceData.efficiency }}%</div>
												</div>
												<div class="info-item">
													<div class="info-label">预计完成时间:</div>
													<div class="info-value">{{ balanceData.estimatedTime }}</div>
												</div>
												<div class="info-item">
													<div class="info-label">能量节省:</div>
													<div class="info-value">{{ balanceData.energySaving }}%</div>
												</div>
											</div>
										</div>
										<div class="detail-controls">
											<!-- <button class="control-button primary" @click="startBalancing">均衡优化</button> -->
											<button class="control-button" @click="showBalanceStrategy = !showBalanceStrategy">
												{{ showBalanceStrategy ? '隐藏' : '查看' }}最优策略
											</button>
										</div>
										<div v-if="showBalanceStrategy" class="strategy-details">
											<div class="strategy-title">智能均衡策略</div>
											<div class="strategy-content">
												<div class="strategy-description">
													系统基于强化学习算法，通过分析电池单体状态，计算出最优均衡路径与时序。
												</div>
												<div class="strategy-steps">
													<div class="step" v-for="(step, index) in balanceData.strategy" :key="index">
														<div class="step-number">{{ index + 1 }}</div>
														<div class="step-content">{{ step }}</div>
													</div>
												</div>
											</div>
											<div class="benefits">
												<div class="benefit-title">预期收益</div>
												<div class="benefit-items">
													<div class="benefit-item">
														<div class="benefit-icon">⚡</div>
														<div class="benefit-text">提升电池容量利用率 {{ balanceData.capacityImprovement }}%</div>
													</div>
													<div class="benefit-item">
														<div class="benefit-icon">🕒</div>
														<div class="benefit-text">延长电池使用寿命 {{ balanceData.lifetimeExtension }}%</div>
													</div>
													<div class="benefit-item">
														<div class="benefit-icon">📈</div>
														<div class="benefit-text">减少充电时间 {{ balanceData.chargingTimeReduction }}%</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									
									<!-- 故障诊断算法 -->
									<div v-if="selectedAlgorithm === 'faultDiagnosis'" class="algorithm-detail">
										<div class="detail-header">
											<div class="detail-title">智能故障诊断系统</div>
											<div class="detail-description">基于机器学习的电池故障模式识别与预警</div>
										</div>
										
										<!-- 添加故障诊断算法介绍 -->
										<div class="fault-diagnosis-intro">
											<div class="algorithm-title">算法介绍</div>
											<div class="algorithm-description">
												<p>本故障诊断算法集成了多种机器学习方法，通过分析电池实时运行数据，可提前识别潜在故障风险。系统利用支持向量机(SVM)和随机森林算法建立异常检测模型，准确识别电池组中的问题单体。</p>
												<p>主要特点：</p>
												<p>- 自适应阈值调整，针对不同应用场景优化故障检测灵敏度</p>
												<p>- 基于历史数据的多维度特征提取，提高故障识别精确度</p>
												<p>- 支持绝缘问题、容量衰减、内部短路等多种故障类型监测</p>
											</div>
										</div>
										
										<div class="detail-visualization">
											<div class="chart-container">
												<div ref="faultChartRef" class="echarts-container"></div>
											</div>
											<div class="chart-info">
												<div class="info-item">
													<div class="info-label">检测到的异常:</div>
													<div class="info-value">{{ diagnosticData.anomalyCount }}个</div>
												</div>
												<div class="info-item">
													<div class="info-label">最高风险:</div>
													<div class="info-value" :class="{'warning': diagnosticData.highestRisk > 50}">
														{{ diagnosticData.highestRisk }}%
													</div>
												</div>
												<div class="info-item">
													<div class="info-label">建议措施:</div>
													<div class="info-value">{{ diagnosticData.recommendedAction }}</div>
												</div>
												<div class="info-item">
													<div class="info-label">模型精度:</div>
													<div class="info-value">{{ diagnosticData.modelAccuracy }}%</div>
												</div>
											</div>
										</div>
										<div class="detail-controls">
											<!-- <button class="control-button primary" @click="runDiagnostic">运行诊断</button> -->
											<button class="control-button" @click="showDiagnosticReport = !showDiagnosticReport">
												{{ showDiagnosticReport ? '隐藏' : '查看' }}诊断报告
											</button>
										</div>
										<div v-if="showDiagnosticReport" class="diagnostic-report">
											<div class="report-title">故障诊断报告</div>
											<div class="report-timestamp">生成时间: {{ diagnosticData.reportTime }}</div>
											<div class="report-content">
												<div class="report-section">
													<div class="section-title">检测到的问题</div>
													<div class="fault-list">
														<div v-for="(issue, index) in diagnosticData.detectedIssues" :key="index" class="fault-item">
															<div class="fault-severity" :class="issue.severity"></div>
															<div class="fault-details">
																<div class="fault-title">{{ issue.title }}</div>
																<div class="fault-description">{{ issue.description }}</div>
															</div>
														</div>
													</div>
												</div>
												<div class="report-section">
													<div class="section-title">建议行动</div>
													<div class="action-list">
														<div v-for="(action, index) in diagnosticData.suggestedActions" :key="index" class="action-item">
															<div class="action-number">{{ index + 1 }}</div>
															<div class="action-text">{{ action }}</div>
														</div>
													</div>
												</div>
												<div class="report-section">
													<div class="section-title">预测维护时间</div>
													<div class="maintenance-prediction">
														<div class="maintenance-value">{{ diagnosticData.maintenanceTime }}</div>
														<div class="maintenance-note">* 基于当前异常情况和使用模式</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									
									<!-- 占位符 -->
									<div v-else-if="selectedAlgorithm !== 'lifePredict' && selectedAlgorithm !== 'thermalManage' && selectedAlgorithm !== 'balanceOptimize' && selectedAlgorithm !== 'faultDiagnosis'" class="algorithm-placeholder">
										<div class="placeholder-icon">🔬</div>
										<div class="placeholder-text">请选择一个算法进行演示</div>
									</div>
								</div>
							</div>
						</div>

						<!-- 大型电池组件 -->
						<div class="battery-group-container">
							<div class="group-title">电池管理系统 (BMS)</div>
							<div class="group-content">
								<div class="large-battery">
									<div class="battery-visual">
										<div class="battery-body">
											<div class="battery-level" :class="{
												'charging': batteryGroupData.isCharging,
												'discharging': batteryGroupData.isDischarging
											}" :style="{height: `${batteryGroupData.healthStatus}%`}">
												<div v-if="batteryGroupData.isCharging" class="bubbles">
													<div class="bubble"></div>
													<div class="bubble"></div>
												<div class="bubble"></div>
												<div class="bubble"></div>
												<div class="bubble"></div>
											</div>
										</div>
										<div class="battery-cap"></div>
										</div>
									</div>
									<div class="battery-info">
										<div class="info-value">{{ batteryGroupData.totalPower }}V</div>
										<div class="info-label">总电量</div>
									</div>
									<div class="battery-status-indicators">
										<div class="status-indicator" :class="batteryGroupData.isCharging ? 'active charging' : ''">
											<div class="status-icon">⚡</div>
											<div class="status-text">充电</div>
										</div>
										<div class="status-indicator" :class="batteryGroupData.isDischarging ? 'active discharging' : ''">
											<div class="status-icon">🔋</div>
											<div class="status-text">放电</div>
										</div>
										<div class="status-indicator" :class="batteryGroupData.isWarning ? 'active warning' : ''">
											<div class="status-icon">⚠️</div>
											<div class="status-text">告警</div>
										</div>
									</div>
									
									<div class="battery-controls">
										<button class="control-btn charge" :class="{ active: batteryGroupData.isCharging }" @click="toggleCharge">
											<div class="btn-icon">⚡</div>
											<div class="btn-text">{{ batteryGroupData.isCharging ? '停止充电' : '开始充电' }}</div>
										</button>
										<button class="control-btn discharge" :class="{ active: batteryGroupData.isDischarging }" @click="toggleDischarge">
											<div class="btn-icon">🔋</div>
											<div class="btn-text">{{ batteryGroupData.isDischarging ? '停止放电' : '开始放电' }}</div>
										</button>
								</div>
								</div>
								
								<div class="bms-details">
									<div class="bms-data-row core-data">
										<div class="data-box">
											<div class="data-label">平均电压</div>
											<div class="data-value">{{ batteryGroupData.averageVoltage }}V</div>
										</div>
										<div class="data-box">
											<div class="data-label">总电流</div>
											<div class="data-value" :class="{'positive': (batteryGroupData.currentFlow || 2.5) > 0, 'negative': (batteryGroupData.currentFlow || 2.5) < 0}">
												{{ batteryGroupData.currentFlow || '+2.5' }}A
											</div>
										</div>
										<div class="data-box">
											<div class="data-label">总功率</div>
											<div class="data-value">{{ batteryGroupData.totalPower * batteryGroupData.currentFlow || '121.25' }}W</div>
										</div>
										<div class="data-box">
											<div class="data-label">健康状态</div>
											<div class="data-value" :class="batteryGroupData.healthStatus < 80 ? 'warning' : 'good'">
												{{ batteryGroupData.healthStatus }}%
											</div>
										</div>
									</div>
									
									<div class="bms-panel">
										<div class="panel-title">电池组数据</div>
										<div class="bms-data-row">
											<div class="data-box">
												<div class="data-label">电池数量</div>
												<div class="data-value">{{ batteries.length || '16' }}个</div>
											</div>
											<div class="data-box">
												<div class="data-label">电池组温度</div>
												<div class="data-value" :class="{'warning': parseFloat(batteryGroupData.temperature.toString()) > 40}">
													{{ batteryGroupData.temperature }}°C
												</div>
											</div>
											<div class="data-box">
												<div class="data-label">最高单体电压</div>
												<div class="data-value">{{ batteryGroupData.maxCellVoltage || '3.48' }}V</div>
											</div>
											<div class="data-box">
												<div class="data-label">最低单体电压</div>
												<div class="data-value">{{ batteryGroupData.minCellVoltage || '3.21' }}V</div>
											</div>
										</div>
									</div>
									
									<div class="bms-panel">
										<div class="panel-title">充放电信息</div>
										<div class="bms-data-row">
											<div class="data-box">
												<div class="data-label">充电状态</div>
												<div class="data-value">{{ batteryGroupData.isCharging ? '充电中' : '未充电' }}</div>
											</div>
											<div class="data-box">
												<div class="data-label">循环次数</div>
												<div class="data-value">{{ batteryGroupData.cycleCount || '126' }}次</div>
											</div>
											<div class="data-box">
												<div class="data-label">满充容量</div>
												<div class="data-value">{{ batteryGroupData.fullCapacity || '24.5' }}Ah</div>
											</div>
											<div class="data-box">
												<div class="data-label">剩余容量</div>
												<div class="data-value">{{ batteryGroupData.remainingCapacity || '18.7' }}Ah</div>
											</div>
										</div>
									</div>
									
									<div class="bms-panel">
										<div class="panel-title">系统状态</div>
										<div class="bms-data-row">
											<div class="data-box">
												<div class="data-label">估计续航</div>
												<div class="data-value">{{ batteryGroupData.remainingTime || '6.5' }}小时</div>
											</div>
											<div class="data-box">
												<div class="data-label">电压差异</div>
												<div class="data-value" :class="{'warning': batteryGroupData.voltageDifference > 0.3}">
													{{ batteryGroupData.voltageDifference }}V
												</div>
											</div>
											<div class="data-box">
												<div class="data-label">均衡状态</div>
												<div class="data-value">{{ batteryGroupData.balancingActive || '进行中' }}</div>
											</div>
											<div class="data-box">
												<div class="data-label">BMS温度</div>
												<div class="data-value">{{ batteryGroupData.bmsTemperature || '32.6' }}°C</div>
											</div>
										</div>
									</div>
									
									<div class="bms-alerts">
										<div class="alert-item" :class="{'active': batteryGroupData.isWarning}">
											<span class="alert-icon">⚠️</span>单体过压告警
										</div>
										<div class="alert-item">
											<span class="alert-icon">⚠️</span>单体欠压告警
										</div>
										<div class="alert-item">
											<span class="alert-icon">⚠️</span>温度过高告警
										</div>
										<div class="alert-item">
											<span class="alert-icon">⚠️</span>系统通信异常
										</div>
										<div class="alert-item">
											<span class="alert-icon">⚠️</span>电流过载告警
										</div>
										<div class="alert-item">
											<span class="alert-icon">⚠️</span>绝缘阻抗异常
										</div>
									</div>
								</div>
							</div>
							
							<!-- 充放电曲线图表 单独一个区块 -->
							<div class="charging-status-chart">
								<div class="chart-header">
									<div class="chart-title">充放电状态曲线</div>
									<div class="estimated-time" :class="{'discharging': batteryGroupData.isDischarging}">
										<span v-if="batteryGroupData.isCharging">预计充满时间: {{ batteryGroupData.estimatedFullTime || '2小时23分钟' }}</span>
										<span v-else-if="batteryGroupData.isDischarging">预计放空时间: {{ batteryGroupData.estimatedEmptyTime || '4小时56分钟' }}</span>
										<span v-else>电池静置中</span>
									</div>
									<div class="navigation-buttons">
										<!-- <div class="nav-button" @click="goToChargingPage">
											<span class="icon">🚗</span>
											<span class="text">酷炫充电模式</span>
										</div> -->
										<div class="nav-button" @click="goToTestingPage">
											<span class="icon">🔬</span>
											<span class="text">电池测试</span>
										</div>
									</div>
								</div>
								<div class="chart-container">
									<div ref="chargingCurveChartRef" class="echarts-container"></div>
								</div>
							</div>
						</div>

						<!-- 单个电池状态 -->
						<div class="battery-cells-section">
							<div class="section-title">单个电池状态</div>
							<div class="cardList">
								<div class="card" v-for="battery in batteries" :key="battery.cellNum">
									<div class="card2">
										<div class="battery-small">
											<div class="battery-body">
												<div class="battery-level" :class="{
													'charging': batteryGroupData.isCharging,
													'discharging': batteryGroupData.isDischarging
												}" :style="{height: `${Math.min(parseFloat(battery.voltage) / 4 * 100, 100)}%`}"></div>
											</div>
											<div class="battery-cap"></div>
										</div>
										<div class="message">
											<div class="title">电池{{ battery.cellNum }}号</div>
											<div class="content">
												<div class="item">
													<div class="item-left">电压：</div>
													<div class="item-right">{{ battery.voltage }}V</div>
												</div>
												<div class="item">
													<div class="item-left">温度：</div>
													<div class="item-right">{{ battery.temperature || '35.2' }}°C</div>
												</div>
												<div class="item">
													<div class="item-left">健康：</div>
													<div class="item-right" :class="{'warning': (battery.health || 95) < 85}">
														{{ battery.health || '95' }}%
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<earth-bg />
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, onBeforeUnmount, computed, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import HomeHeader from './components/home-header/index.vue';
import EarthBg from './components/earth-bg/index.vue';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { getTableName, getTableData, addRecord, updateRecord, deleteRecord as deleteRecordAPI } from './service';
import message from 'ant-design-vue/es/message';
import * as echarts from 'echarts';

const loadShow = ref(false);
const leftList = ref<any[]>([]);
const selectLeftId = ref<string | number>('');
const openAdd = ref(false);
const tableTitle = ref('电池状态');
const batteries = ref<any[]>([]);
const batteryGroupData = ref({
	totalPower: 48.5,
	averageVoltage: '3.25',
	temperature: 36.2,
	healthStatus: 92,
	isCharging: true,
	isDischarging: false,
	isWarning: false,
	currentFlow: 2.5,
	maxCellVoltage: '3.48',
	minCellVoltage: '3.21',
	cycleCount: '126',
	fullCapacity: '24.5',
	remainingCapacity: '18.7',
	remainingTime: '6.5',
	voltageDifference: 0.27,
	balancingActive: '进行中',
	bmsTemperature: '32.6',
	estimatedFullTime: '2小时23分钟',
	estimatedEmptyTime: '4小时56分钟'
});

// 定时器变量
let curveUpdateTimer: number | null = null;

const route = useRoute();

const fetchTableName = async () => {
	leftList.value = [{ id: 1, table_name: '电池状态', api_url: '/api/loguser' }];
	selectLeftEvent(1);
};

const selectLeftEvent = (id: any) => {
	selectLeftId.value = id;
	tableTitle.value = leftList.value.find((item: any) => item.id === id)?.table_name;
	fetchBatteryData();
};

const fetchBatteryData = () => {
	// Fetch initial battery data if needed
	// batteries.value = [
	//  {
	//    cellNum: '1',
	//    voltage:'3.300',
	//  },
	// ];
};

let ws: WebSocket | null = null;

const connectWebSocket = () => {
	ws = new WebSocket('ws://localhost:3000/ws/battery-info');

	ws.onopen = () => {
		console.log('WebSocket connected');
	};

	ws.onmessage = event => {
		const data = JSON.parse(event.data);
		// console.log(data.cellVoltages);
		updateDate(data);
	};

	ws.onerror = error => {
		console.error('WebSocket error:', error);
	};

	ws.onclose = () => {
		console.log('WebSocket disconnected');
		setTimeout(connectWebSocket, 5000); // Attempt to reconnect after 5 seconds
	};
};

const updateDate = (data: any) => {
  const updatedCellNums = new Set();

  data.cellVoltages.forEach((newCell: any) => {
    const existingCell = batteries.value.find(cell => cell.cellNum === newCell.cellNum);
    if (existingCell) {
      existingCell.voltage = newCell.voltage;
    } else {
      batteries.value.push(newCell);
    }
    updatedCellNums.add(newCell.cellNum);
  });

  // 更新电池组整体数据
  if (data.batteryGroup) {
    batteryGroupData.value = {
      ...batteryGroupData.value,
      ...data.batteryGroup
    };
  }

  // Sort the batteries array after updating
  batteries.value.sort((a, b) => parseInt(a.cellNum) - parseInt(b.cellNum));
  
  // 只在首次更新时渲染图表，后续不再更新
  if (!chargingCurveData.value.isInitialized) {
    updateChargingCurveData();
    chargingCurveData.value.isInitialized = true;
  }
};

// 页面跳转方法
const router = useRouter();

// 统一调整窗口大小处理函数
const handleResize = () => {
	if (window.innerWidth < 480) {
		router.push({ name: 'warningMobile', query: route.query });
	}
  
  // 调整所有图表大小
  if (lifePredictChart) lifePredictChart.resize();
  if (thermalChart) thermalChart.resize();
  if (balanceChart) balanceChart.resize();
  if (faultChart) faultChart.resize();
  if (chargingCurveChart) chargingCurveChart.resize();
};

// 挂载后初始化图表
onMounted(() => {
	fetchTableName();
	handleResize();
  window.addEventListener('resize', handleResize);
	connectWebSocket();
  
  // 初始化默认选中的算法图表
  setTimeout(() => {
    initLifePredictChart();
  }, 500);
  
  // 完全重写充放电曲线图表初始化方法
  setTimeout(() => {
  console.log('开始初始化充放电曲线图表...');
  
  // 已有固定示例数据，不需要清空和生成
  // chargingCurveData.value.times = [];
  // chargingCurveData.value.voltage = [];
  // chargingCurveData.value.current = [];
  // chargingCurveData.value.power = [];
  
  console.log('使用固定示例数据:', chargingCurveData.value);
  
  // 确保DOM已渲染后初始化图表
  nextTick(() => {
    if (chargingCurveChartRef.value) {
      console.log('充放电曲线图表DOM已就绪，开始初始化图表');
      
      // 清理旧实例
      if (chargingCurveChart) {
        chargingCurveChart.dispose();
        chargingCurveChart = null;
      }
      
      // 创建新图表实例
      chargingCurveChart = echarts.init(chargingCurveChartRef.value);
      drawChargingCurveChart();
      console.log('充放电曲线图表已初始化完成');
    } else {
      console.error('充放电曲线图表DOM未找到');
    }
    
    // 注释掉定时更新相关代码
    /*
    if (curveUpdateTimer) {
      clearInterval(curveUpdateTimer);
    }
    
    curveUpdateTimer = window.setInterval(() => {
      updateChargingCurveData();
    }, 1800000);
    */
  });
}, 2000); // 也可以缩短延迟时间
});

onBeforeUnmount(() => {
	window.removeEventListener('resize', handleResize); // 移除这个监听器
	window.removeEventListener('resize', resizeCharts);
	
	if (lifePredictChart) lifePredictChart.dispose();
	if (thermalChart) thermalChart.dispose();
	if (balanceChart) balanceChart.dispose();
	if (faultChart) faultChart.dispose();
	
	// 新增: 清除充放电曲线相关资源
	if (chargingCurveChart) chargingCurveChart.dispose();
	if (curveUpdateTimer) clearInterval(curveUpdateTimer);
	
	// 清除窗口大小变化监听
	window.removeEventListener('resize', () => {
		if (chargingCurveChart) {
			chargingCurveChart.resize();
		}
	});
});

const toggleCharge = () => {
	// 防止同时充电和放电
	if (!batteryGroupData.value.isCharging && batteryGroupData.value.isDischarging) {
		batteryGroupData.value.isDischarging = false;
	}
	
	batteryGroupData.value.isCharging = !batteryGroupData.value.isCharging;
	
	// 发送控制命令到WebSocket服务器
	if (ws && ws.readyState === WebSocket.OPEN) {
		ws.send(JSON.stringify({
			command: batteryGroupData.value.isCharging ? 'startCharging' : 'stopCharging'
		}));
	}
};

const toggleDischarge = () => {
	// 防止同时充电和放电
	if (!batteryGroupData.value.isDischarging && batteryGroupData.value.isCharging) {
		batteryGroupData.value.isCharging = false;
	}
	
	batteryGroupData.value.isDischarging = !batteryGroupData.value.isDischarging;
	
	// 发送控制命令到WebSocket服务器
	if (ws && ws.readyState === WebSocket.OPEN) {
		ws.send(JSON.stringify({
			command: batteryGroupData.value.isDischarging ? 'startDischarging' : 'stopDischarging'
		}));
	}
};

// 跳转到充电页面
const goToChargingPage = () => {
	router.push('/battery/charging');
};

// 跳转到测试页面
const goToTestingPage = () => {
	router.push('/battery/testing');
};

// 算法演示相关状态
const selectedAlgorithm = ref('lifePredict');
const showAlgorithmDetails = ref(false);
const showBalanceStrategy = ref(false);
const showDiagnosticReport = ref(false);

// 寿命预测算法数据
const algorithmData = ref({
	capacityFadeRate: 1.2,
	avgDischargeDepth: 65,
	healthScore: 87,
	predictionRunning: false
});

// 热管理算法数据
const thermalData = ref({
	cellTemps: [32.1, 34.5, 35.2, 33.8, 36.1, 37.2, 35.8, 34.2, 36.7, 38.1, 37.4, 35.6],
	avgTemp: 35.6,
	maxTemp: 38.1,
	tempDiff: 6.0,
	coolingStatus: '自动模式',
	coolingActive: false,
	showPrediction: false,
	hotspots: [
		{ cell: 10, probability: 78 },
		{ cell: 6, probability: 62 },
		{ cell: 11, probability: 45 }
	],
	recommendation: '建议降低10号和6号电池周围的环境温度，并减少电池放电功率15%以防止过热'
});

// 均衡优化算法数据
const balanceData = ref({
	cells: [
		{ voltage: 3.42, level: 85, balancing: false },
		{ voltage: 3.38, level: 82, balancing: true },
		{ voltage: 3.45, level: 88, balancing: false },
		{ voltage: 3.36, level: 80, balancing: true },
		{ voltage: 3.41, level: 84, balancing: false },
		{ voltage: 3.44, level: 87, balancing: false },
		{ voltage: 3.39, level: 83, balancing: false },
		{ voltage: 3.37, level: 81, balancing: true }
	],
	voltageDiff: 0.09,
	balanceStatus: '进行中',
	activeBalancing: 3,
	efficiency: 92,
	estimatedTime: '约45分钟',
	energySaving: 5.8,
	strategy: [
		'优先均衡3.36V的4号电池单体',
		'同步均衡3.37V的8号电池和3.38V的2号电池',
		'待电压达到3.40V后，再均衡其余电池',
		'采用脉冲均衡模式减少能量损耗'
	],
	capacityImprovement: 8.2,
	lifetimeExtension: 12.5,
	chargingTimeReduction: 15.3
});

// 故障诊断算法数据
const diagnosticData = ref({
	systemHealth: 86,
	anomalyCount: 3,
	highestRisk: 72,
	recommendedAction: '检查6号电池',
	modelAccuracy: 94.5,
	reportTime: new Date().toLocaleString(),
	faultTypes: [
		{ name: '电池内短路', risk: 12 },
		{ name: '过充损伤', risk: 25 },
		{ name: '容量衰减', risk: 58 },
		{ name: '热失控', risk: 8 },
		{ name: '绝缘问题', risk: 72 }
	],
	detectedIssues: [
		{ 
			severity: 'high', 
			title: '绝缘电阻异常', 
			description: '6号电池绝缘电阻下降到标准值的65%以下，存在潜在安全风险' 
		},
		{ 
			severity: 'medium', 
			title: '容量不均衡', 
			description: '电池组内部容量差异超过15%，影响整体性能和寿命' 
		},
		{ 
			severity: 'low', 
			title: '充电效率下降', 
			description: '系统充电效率较标准值下降8%，可能是连接器或控制板问题' 
		}
	],
	suggestedActions: [
		'进行完整的绝缘测试，重点检查6号电池连接器',
		'启动强制均衡程序，平衡电池组容量',
		'检查充电控制器和连接器，确保良好接触',
		'下一次维护时更换老化的导热材料'
	],
	maintenanceTime: '建议7天内进行检修'
});

// 计算属性
const predictedCycles = computed(() => {
	return parseInt(batteryGroupData.value.cycleCount) + Math.floor(Math.random() * 300) + 500;
});

const predictedLifeMonths = computed(() => {
	// 假设每天一个循环
	return Math.floor(predictedCycles.value / 30);
});

// 算法相关方法
const selectAlgorithm = (algorithm: string) => {
	selectedAlgorithm.value = algorithm;
};

// 寿命预测相关方法
const runPredictionAlgorithm = () => {
	algorithmData.value.predictionRunning = true;
	
	// 模拟算法计算
	setTimeout(() => {
		algorithmData.value.healthScore = Math.floor(Math.random() * 10) + 80;
		algorithmData.value.predictionRunning = false;
	}, 1500);
};

// 热管理相关方法
const getTempColor = (temp: number) => {
	if (temp < 30) return '#00b8ff';
	if (temp < 35) return '#66ff00';
	if (temp < 40) return '#ffcc00';
	if (temp < 45) return '#ff6600';
	return '#ff0000';
};

const toggleCooling = () => {
	thermalData.value.coolingActive = !thermalData.value.coolingActive;
	thermalData.value.coolingStatus = thermalData.value.coolingActive ? '强制冷却中' : '自动模式';
};

const optimizeCooling = () => {
	// 模拟优化过程
	setTimeout(() => {
		thermalData.value.coolingStatus = '优化完成';
		// 模拟温度降低
		thermalData.value.cellTemps = thermalData.value.cellTemps.map(temp => 
			Math.max(temp - Math.random() * 2 - 1, 28)
		);
		thermalData.value.avgTemp = Number((thermalData.value.cellTemps.reduce((a, b) => a + b, 0) / thermalData.value.cellTemps.length).toFixed(1));
		thermalData.value.maxTemp = Math.max(...thermalData.value.cellTemps);
		thermalData.value.tempDiff = Number((thermalData.value.maxTemp - Math.min(...thermalData.value.cellTemps)).toFixed(1));
	}, 20000);
};

const predictHotspots = () => {
	thermalData.value.showPrediction = true;
};

// 均衡优化相关方法
const startBalancing = () => {
	// 模拟均衡过程
	const cells = balanceData.value.cells.map(cell => ({
		...cell, 
		balancing: cell.voltage > 3.40
	}));
	
	balanceData.value.cells = cells;
	balanceData.value.activeBalancing = cells.filter(c => c.balancing).length;
	balanceData.value.balanceStatus = '智能均衡中';
};

// 故障诊断相关方法
const getHealthClass = (health: number) => {
	if (health > 90) return 'excellent';
	if (health > 75) return 'good';
	if (health > 50) return 'fair';
	return 'poor';
};

const runDiagnostic = () => {
	// 模拟诊断过程
	setTimeout(() => {
		diagnosticData.value.reportTime = new Date().toLocaleString();
		// 随机调整一些诊断结果
		diagnosticData.value.systemHealth = Math.floor(Math.random() * 15) + 75;
		diagnosticData.value.faultTypes = diagnosticData.value.faultTypes.map(fault => ({
			...fault,
			risk: Math.min(100, Math.max(5, fault.risk + (Math.random() * 20 - 10)))
		}));
		diagnosticData.value.highestRisk = Math.max(...diagnosticData.value.faultTypes.map(f => f.risk));
	}, 2500);
};

// 图表引用
const lifePredictChartRef = ref<HTMLElement | null>(null);
const thermalChartRef = ref<HTMLElement | null>(null);
const balanceChartRef = ref<HTMLElement | null>(null);
const faultChartRef = ref<HTMLElement | null>(null);

// 图表实例
let lifePredictChart: echarts.ECharts | null = null;
let thermalChart: echarts.ECharts | null = null;
let balanceChart: echarts.ECharts | null = null;
let faultChart: echarts.ECharts | null = null;

// 充放电曲线图表引用
const chargingCurveChartRef = ref<HTMLElement | null>(null);
let chargingCurveChart: echarts.ECharts | null = null;

// 充放电曲线数据
// 充放电曲线数据
// 充放电曲线数据
const chargingCurveData = ref({
  times: [
    '08:00:00', '08:30:00', '09:00:00', '09:30:00', '10:00:00', 
    '10:30:00', '11:00:00', '11:30:00', '12:00:00', '12:30:00',
    '13:00:00', '13:30:00', '14:00:00', '14:30:00', '15:00:00'
  ],
  voltage: [
    '3.25', '3.28', '3.30', '3.33', '3.35', 
    '3.38', '3.40', '3.42', '3.45', '3.47',
    '3.49', '3.50', '3.52', '3.54', '3.55'
  ],
  current: [
    '2.10', '2.15', '2.20', '2.25', '2.30', 
    '2.35', '2.40', '2.45', '2.50', '2.55',
    '2.60', '2.65', '2.70', '2.75', '2.80'
  ],
  power: [
    '6.83', '7.05', '7.26', '7.49', '7.71', 
    '7.94', '8.16', '8.38', '8.63', '8.85',
    '9.07', '9.28', '9.50', '9.74', '9.94'
  ],
  lastUpdated: new Date().getTime(),
  isInitialized: true
});
// 更新充放电曲线数据
const updateChargingCurveData = () => {
	const now = new Date();
	const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
	
	// 保持最近30个数据点
	if (chargingCurveData.value.times.length > 30) {
		chargingCurveData.value.times.shift();
		chargingCurveData.value.voltage.shift();
		chargingCurveData.value.current.shift();
		chargingCurveData.value.power.shift();
	}
	
	// 添加新数据
	chargingCurveData.value.times.push(timeStr);
	
	// 添加一些波动以产生更自然的曲线
	const voltageValue = parseFloat(batteryGroupData.value.averageVoltage) + (Math.random() * 0.2 - 0.1);
	const currentValue = batteryGroupData.value.isCharging ? 
		(parseFloat(String(batteryGroupData.value.currentFlow)) + (Math.random() * 0.3 - 0.1)) : 
		(batteryGroupData.value.isDischarging ? 
			(parseFloat(String(batteryGroupData.value.currentFlow)) - (Math.random() * 0.3 - 0.1)) : 0);
	const powerValue = voltageValue * Math.abs(currentValue);
	
	chargingCurveData.value.voltage.push(voltageValue.toFixed(2));
	chargingCurveData.value.current.push(currentValue.toFixed(2));
	chargingCurveData.value.power.push(powerValue.toFixed(2));
	
	// 更新图表
	drawChargingCurveChart();
};

// 分离图表绘制逻辑为独立函数
const drawChargingCurveChart = () => {
	if (!chargingCurveChartRef.value) return;
	
	// 确保图表实例存在
	if (!chargingCurveChart) {
		chargingCurveChart = echarts.init(chargingCurveChartRef.value);
	}
	
	const option = {
		backgroundColor: 'rgba(0,0,0,0.1)',
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985'
				}
			}
		},
		legend: {
			data: ['电压(V)', '电流(A)', '功率(W)'],
			textStyle: {
				color: '#fff'
			},
			right: 10,
			top: 10
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: chargingCurveData.value.times,
			axisLine: {
				lineStyle: {
					color: '#8ecbff'
				}
			},
			axisLabel: {
				color: '#8ecbff',
				fontSize: 10,
				rotate: 45
			}
		},
		yAxis: [
			{
				type: 'value',
				name: '电压(V)',
				position: 'left',
				nameTextStyle: {
					color: '#01c2ff'
				},
				axisLine: {
					lineStyle: {
						color: '#01c2ff'
					}
				},
				axisLabel: {
					color: '#01c2ff',
					fontSize: 10
				},
				splitLine: {
					lineStyle: {
						color: 'rgba(1, 194, 255, 0.1)'
					}
				}
			},
			{
				type: 'value',
				name: '电流(A)/功率(W)',
				position: 'right',
				nameTextStyle: {
					color: '#00ff75'
				},
				axisLine: {
					lineStyle: {
						color: '#00ff75'
					}
				},
				axisLabel: {
					color: '#00ff75',
					fontSize: 10
				},
				splitLine: {
					show: false
				}
			}
		],
		series: [
			{
				name: '电压(V)',
				type: 'line',
				data: chargingCurveData.value.voltage,
				yAxisIndex: 0,
				showSymbol: false,
				lineStyle: {
					width: 2,
					color: '#01c2ff'
				},
				itemStyle: {
					color: '#01c2ff'
				},
				areaStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{
								offset: 0,
								color: 'rgba(1, 194, 255, 0.5)'
							},
							{
								offset: 1,
								color: 'rgba(1, 194, 255, 0.1)'
							}
						]
					}
				}
			},
			{
				name: '电流(A)',
				type: 'line',
				data: chargingCurveData.value.current,
				yAxisIndex: 1,
				showSymbol: false,
				lineStyle: {
					width: 2,
					color: '#00ff75'
				},
				itemStyle: {
					color: '#00ff75'
				}
			},
			{
				name: '功率(W)',
				type: 'line',
				data: chargingCurveData.value.power,
				yAxisIndex: 1,
				showSymbol: false,
				lineStyle: {
					width: 2,
					color: '#ffcc00',
					type: 'dashed'
				},
				itemStyle: {
					color: '#ffcc00'
				}
			}
		]
	};
	
	chargingCurveChart.setOption(option);
};

// 清理之前的initChargingCurveChart引用
const initChargingCurveChart = drawChargingCurveChart;

// 初始化寿命预测图表
const initLifePredictChart = () => {
	if (!lifePredictChartRef.value) return;
	
	lifePredictChart = echarts.init(lifePredictChartRef.value);
	
	const option = {
		backgroundColor: 'rgba(0,0,0,0.1)',
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985'
				}
			}
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: ['过去', '当前', '未来(预测)'],
			axisLine: {
				lineStyle: {
					color: '#8ecbff'
				}
			},
			axisLabel: {
				color: '#8ecbff'
			}
		},
		yAxis: {
			type: 'value',
			name: '电池容量(%)',
			nameTextStyle: {
				color: '#8ecbff'
			},
			min: 60,
			max: 100,
			axisLine: {
				lineStyle: {
					color: '#8ecbff'
				}
			},
			axisLabel: {
				color: '#8ecbff'
			},
			splitLine: {
				lineStyle: {
					color: 'rgba(140, 203, 255, 0.1)'
				}
			}
		},
		series: [
			{
				name: '实际容量',
				type: 'line',
				lineStyle: {
					width: 3,
					color: '#01c2ff'
				},
				itemStyle: {
					color: '#01c2ff'
				},
				data: [100, batteryGroupData.value.healthStatus, null],
				symbolSize: 8
			},
			{
				name: '预测容量',
				type: 'line',
				lineStyle: {
					width: 3,
					color: '#00ff75',
					type: 'dashed'
				},
				itemStyle: {
					color: '#00ff75'
				},
				data: [null, batteryGroupData.value.healthStatus, batteryGroupData.value.healthStatus - 15],
				symbolSize: 8
			},
			{
				name: '警戒线',
				type: 'line',
				markLine: {
					silent: true,
					lineStyle: {
						color: '#ff6e76'
					},
					data: [{
						yAxis: 70,
						label: {
							formatter: '警戒线',
							position: 'start',
							color: '#ff6e76'
						}
					}]
				}
			}
		],
		legend: {
			data: ['实际容量', '预测容量'],
			textStyle: {
				color: '#fff'
			},
			right: 10,
			top: 10
		}
	};
	
	lifePredictChart.setOption(option);
};

// 初始化热管理图表
const initThermalChart = () => {
	if (!thermalChartRef.value) return;
	
	thermalChart = echarts.init(thermalChartRef.value);
	
	const option = {
		backgroundColor: 'rgba(0,0,0,0.1)',
		tooltip: {
			position: 'top',
			formatter: function (params: any) {
				return `电池${params.data[0] + 1}号: ${params.data[2]}°C`;
			}
		},
		grid: {
			left: 30,
			bottom: 10,
			right: 60,
			top: 10,
			containLabel: true
		},
		xAxis: {
			type: 'category',
			data: ['1', '2', '3', '4'],
			splitArea: {
				show: true
			},
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			axisLabel: {
				color: '#8ecbff',
				fontSize: 12
			}
		},
		yAxis: {
			type: 'category',
			data: ['A', 'B', 'C'],
			splitArea: {
				show: true
			},
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			axisLabel: {
				color: '#8ecbff',
				fontSize: 12
			}
		},
		visualMap: {
			min: 30,
			max: 40,
			calculable: true,
			orient: 'horizontal',
			left: 'center',
			bottom: '0%',
			text: ['高温', '低温'],
			textStyle: {
				color: '#fff'
			},
			inRange: {
				color: ['#00b8ff', '#66ff00', '#ffcc00', '#ff6600', '#ff0000']
			}
		},
		series: [{
			name: '电池温度',
			type: 'heatmap',
			data: thermalData.value.cellTemps.map((temp, index) => {
				const x = index % 4;
				const y = Math.floor(index / 4);
				return [x, y, temp];
			}),
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowColor: 'rgba(0, 0, 0, 0.5)'
				}
			},
			label: {
				show: true,
				formatter: function(params: any) {
					return params.data[2].toFixed(1) + '°C';
				},
				color: '#fff',
				fontSize: 12
			},
			itemStyle: {
				borderColor: 'rgba(255, 255, 255, 0.1)',
				borderWidth: 1
			}
		}]
	};
	
	thermalChart.setOption(option);
};

// 初始化均衡优化图表
const initBalanceChart = () => {
	if (!balanceChartRef.value) return;
	
	balanceChart = echarts.init(balanceChartRef.value);
	
	const option = {
		backgroundColor: 'rgba(0,0,0,0.1)',
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			},
			formatter: function(params: any) {
				const dataIndex = params[0].dataIndex;
				const voltage = balanceData.value.cells[dataIndex].voltage;
				const balancing = balanceData.value.cells[dataIndex].balancing;
				return `电池${dataIndex + 1}号<br/>电压: ${voltage}V<br/>状态: ${balancing ? '均衡中' : '正常'}`;
			}
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			top: '50px',
			containLabel: true
		},
		xAxis: {
			type: 'category',
			data: balanceData.value.cells.map((_, i) => `电池${i + 1}`),
			axisLabel: {
				color: '#8ecbff',
				fontSize: 12
			},
			axisLine: {
				lineStyle: {
					color: '#8ecbff'
				}
			}
		},
		yAxis: {
			type: 'value',
			name: '电压(V)',
			min: 3.30,
			max: 3.50,
			nameTextStyle: {
				color: '#8ecbff'
			},
			axisLabel: {
				color: '#8ecbff',
				fontSize: 12
			},
			axisLine: {
				lineStyle: {
					color: '#8ecbff'
				}
			},
			splitLine: {
				lineStyle: {
					color: 'rgba(140, 203, 255, 0.1)'
				}
			}
		},
		series: [
			{
				name: '电池电压',
				type: 'bar',
				data: balanceData.value.cells.map((cell) => ({
					value: cell.voltage,
					itemStyle: {
						color: cell.balancing ? '#ffcc00' : '#01c2ff'
					}
				})),
				label: {
					show: true,
					position: 'top',
					formatter: '{c} V',
					color: '#fff',
					fontSize: 12
				},
				barWidth: '50%'
			},
			{
				name: '目标均衡区间',
				type: 'line',
				markArea: {
					silent: true,
					data: [
						[{
							name: '理想均衡区间',
							yAxis: 3.40
						}, {
							yAxis: 3.42
						}]
					],
					itemStyle: {
						color: 'rgba(0, 255, 117, 0.2)',
						borderColor: 'rgba(0, 255, 117, 0.5)',
						borderWidth: 1
					}
				}
			}
		]
	};
	
	balanceChart.setOption(option);
};

// 初始化故障诊断图表
const initFaultChart = () => {
	if (!faultChartRef.value) return;
	
	faultChart = echarts.init(faultChartRef.value);
	
	const option = {
		backgroundColor: 'rgba(0,0,0,0.1)',
		tooltip: {
			trigger: 'item'
		},
		radar: {
			shape: 'circle',
			indicator: diagnosticData.value.faultTypes.map(item => ({
				name: item.name,
				max: 100
			})),
			splitArea: {
				areaStyle: {
					color: ['rgba(0, 0, 0, 0.1)'],
				}
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(140, 203, 255, 0.2)'
				}
			},
			splitLine: {
				lineStyle: {
					color: 'rgba(140, 203, 255, 0.2)'
				}
			},
			name: {
				textStyle: {
					color: '#8ecbff',
					fontSize: 12
				}
			}
		},
		series: [{
			name: '故障风险',
			type: 'radar',
			data: [
				{
					value: diagnosticData.value.faultTypes.map(item => item.risk),
					name: '风险评分',
					areaStyle: {
						color: 'rgba(1, 194, 255, 0.6)'
					},
					lineStyle: {
						color: '#01c2ff',
						width: 2
					},
					itemStyle: {
						color: '#01c2ff'
					}
				}
			]
		}],
		legend: {
			data: ['风险评分'],
			textStyle: {
				color: '#fff'
			},
			right: 10,
			top: 10
		}
	};
	
	faultChart.setOption(option);
};

// 窗口大小改变时重绘图表
const resizeCharts = () => {
	if (lifePredictChart) lifePredictChart.resize();
	if (thermalChart) thermalChart.resize();
	if (balanceChart) balanceChart.resize();
	if (faultChart) faultChart.resize();
	if (chargingCurveChart) chargingCurveChart.resize();
};

// 监听窗口大小变化
window.addEventListener('resize', resizeCharts);

// 组件卸载前清理图表和事件监听
onBeforeUnmount(() => {
	window.removeEventListener('resize', handleResize);
	window.removeEventListener('resize', resizeCharts);
	
	if (lifePredictChart) lifePredictChart.dispose();
	if (thermalChart) thermalChart.dispose();
	if (balanceChart) balanceChart.dispose();
	if (faultChart) faultChart.dispose();
	
	// 新增: 清除充放电曲线相关资源
	if (chargingCurveChart) chargingCurveChart.dispose();
	if (curveUpdateTimer) clearInterval(curveUpdateTimer);
	
	// 清除窗口大小变化监听
	window.removeEventListener('resize', () => {
		if (chargingCurveChart) {
			chargingCurveChart.resize();
		}
	});
});

// 监听算法选择变化
watch(selectedAlgorithm, (newVal) => {
  // 清除旧图表
  if (lifePredictChart) lifePredictChart.dispose();
  if (thermalChart) thermalChart.dispose();
  if (balanceChart) balanceChart.dispose();
  if (faultChart) faultChart.dispose();
  
  // 初始化新选择的算法图表
  setTimeout(() => {
    if (newVal === 'lifePredict') {
      initLifePredictChart();
    } else if (newVal === 'thermalManage') {
      initThermalChart();
    } else if (newVal === 'balanceOptimize') {
      initBalanceChart();
    } else if (newVal === 'faultDiagnosis') {
      initFaultChart();
    }
  }, 100);
});

// ... existing code ...
</script>

<style lang="scss" scoped>
.full-box {
	width: 100%;
	height: 95%;
	background-color: rgba($color: #04285a, $alpha: 0.5);
	padding: 20px;
	overflow: auto;
}

.main-content {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.battery-group-container {
	background: rgba(1, 194, 255, 0.1);
	border: 1px solid rgba(1, 194, 255, 0.3);
	border-radius: 15px;
	padding: 20px;
	
	.group-title {
		font-size: 24px;
		color: #01c2ff;
		margin-bottom: 25px;
		padding-left: 10px;
		border-left: 3px solid #2ca6ff;
	}
	
	.group-content {
		display: flex;
		gap: 30px;
		
		.large-battery {
			flex: 0 0 auto;
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.battery-visual {
				position: relative;
				width: 120px;
				height: 200px;
				
				.battery-body {
					position: absolute;
					top: 10px;
					width: 100%;
					height: 180px;
					background: rgba(0, 0, 0, 0.3);
					border: 4px solid #01c2ff;
					border-radius: 10px;
					overflow: hidden;
					
					.battery-level {
						position: absolute;
						bottom: 0;
						width: 100%;
						background: linear-gradient(to top, #00ff75, #01c2ff);
						transition: height 1.5s ease-in-out;
					
						.bubbles {
						position: absolute;
						width: 100%;
						height: 100%;
						overflow: hidden;
						
						.bubble {
							position: absolute;
								bottom: -20px;
								width: 8px;
								height: 8px;
								background: rgba(255, 255, 255, 0.6);
							border-radius: 50%;
								opacity: 0.6;
								animation: bubbleRise 2s infinite ease-in;
							
							&:nth-child(1) {
									left: 10%;
									width: 6px;
									height: 6px;
									animation-delay: 0.2s;
									animation-duration: 2.5s;
							}
							
							&:nth-child(2) {
									left: 30%;
									width: 8px;
									height: 8px;
									animation-delay: 0.5s;
									animation-duration: 2.8s;
							}
							
							&:nth-child(3) {
									left: 50%;
									width: 7px;
									height: 7px;
									animation-delay: 0.8s;
									animation-duration: 3s;
								}
								
								&:nth-child(4) {
								left: 70%;
									width: 5px;
									height: 5px;
									animation-delay: 1.2s;
									animation-duration: 2.2s;
								}
								
								&:nth-child(5) {
									left: 85%;
									width: 4px;
									height: 4px;
									animation-delay: 1.5s;
									animation-duration: 2.4s;
								}
							}
						}
					}
					
					.battery-cap {
						position: absolute;
						top: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 40px;
						height: 10px;
						background: #01c2ff;
						border-radius: 5px 5px 0 0;
					}
				}
				
				.battery-cap {
					position: absolute;
					top: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40px;
					height: 10px;
					background: #01c2ff;
					border-radius: 5px 5px 0 0;
				}
			}
			
			.battery-info {
				text-align: center;
				margin-top: 20px;
				
				.info-value {
					font-size: 36px;
					font-weight: bold;
					color: #00ff75;
					text-shadow: 0 0 10px rgba(0, 255, 117, 0.5);
				}
				
				.info-label {
					color: #8ecbff;
					font-size: 18px;
					margin-top: 5px;
				}
			}
			
			.battery-status-indicators {
				display: flex;
				gap: 15px;
				margin-top: 20px;
				
				.status-indicator {
					display: flex;
					flex-direction: column;
					align-items: center;
					opacity: 0.4;
					transition: all 0.3s ease;
					
					&.active {
						opacity: 1;
						
						&.charging .status-icon {
							color: #00ff75;
							text-shadow: 0 0 10px rgba(0, 255, 117, 0.7);
							animation: pulse 1.5s infinite;
						}
						
						&.discharging .status-icon {
							color: #ffcc00;
							text-shadow: 0 0 10px rgba(255, 204, 0, 0.7);
						}
						
						&.warning .status-icon {
							color: #ff6e76;
							text-shadow: 0 0 10px rgba(255, 110, 118, 0.7);
							animation: pulse 0.8s infinite;
						}
					}
					
					.status-icon {
						font-size: 24px;
						margin-bottom: 5px;
					}
					
					.status-text {
						font-size: 12px;
						color: #8ecbff;
					}
				}
			}
			
			.battery-controls {
				display: flex;
				gap: 10px;
				margin-top: 20px;
				
				.control-btn {
					display: flex;
					flex-direction: column;
					align-items: center;
					background: rgba(1, 194, 255, 0.1);
					border: 1px solid rgba(1, 194, 255, 0.3);
					border-radius: 10px;
					padding: 10px 15px;
					cursor: pointer;
					transition: all 0.3s ease;
					
					&:hover {
						background: rgba(1, 194, 255, 0.2);
					}
					
					&.active {
						background: rgba(1, 194, 255, 0.3);
						box-shadow: 0 0 15px rgba(1, 194, 255, 0.5);
						
						&.charge .btn-icon {
							color: #00ff75;
							text-shadow: 0 0 10px rgba(0, 255, 117, 0.7);
						}
						
						&.discharge .btn-icon {
							color: #ffcc00;
							text-shadow: 0 0 10px rgba(255, 204, 0, 0.7);
						}
					}
					
					.btn-icon {
						font-size: 24px;
						margin-bottom: 5px;
					}
					
					.btn-text {
						font-size: 14px;
						color: #ffffff;
						font-weight: bold;
					}
				}
			}
		}
		
		.bms-details {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 15px;
			
			.core-data {
				background: rgba(1, 194, 255, 0.15);
				border-radius: 12px;
				padding: 15px;
				
				.data-box {
					.data-value {
						font-size: 28px;
					}
				}
			}
			
			.bms-panel {
				background: rgba(1, 194, 255, 0.05);
				border-radius: 10px;
				padding: 15px;
				
				.panel-title {
					color: #01c2ff;
					font-size: 18px;
					margin-bottom: 15px;
					border-left: 2px solid #01c2ff;
					padding-left: 8px;
				}
			}
			
			.bms-data-row {
				display: flex;
				flex-wrap: wrap;
				gap: 15px;
				
				.data-box {
					flex: 1;
					min-width: 120px;
					max-width: 180px;
					
					.data-label {
						color: #8ecbff;
						font-size: 14px;
						margin-bottom: 5px;
					}
					
					.data-value {
						color: #ffffff;
						font-size: 20px;
						font-weight: bold;
						
						&.warning {
							color: #ff6e76;
						}
						
						&.good {
							color: #00ff75;
						}
						
						&.positive {
							color: #00ff75;
						}
						
						&.negative {
							color: #ff6e76;
						}
					}
				}
			}
			
			.bms-alerts {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				margin-top: 10px;
				
				.alert-item {
					background: rgba(30, 30, 30, 0.5);
					border-radius: 6px;
					padding: 8px 12px;
					display: flex;
					align-items: center;
					gap: 8px;
					opacity: 0.4;
					font-size: 14px;
					color: #ffffff;
					transition: all 0.3s ease;
					
					&.active {
						background: rgba(255, 110, 118, 0.2);
						border: 1px solid rgba(255, 110, 118, 0.5);
						opacity: 1;
						animation: alert-flash 2s infinite;
					}
					
					.alert-icon {
						font-size: 14px;
					}
				}
			}
		}
	}
}

.battery-cells-section {
	.section-title {
		font-size: 22px;
		color: #01c2ff;
		margin-bottom: 20px;
		padding-left: 10px;
		border-left: 3px solid #2ca6ff;
	}
}

.cardList {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
	overflow: auto;
	max-height: 60vh;
	
	.card {
		width: 250px;
		height: 180px;
		background-image: linear-gradient(163deg, #00ff75 0%, #3700ff 100%);
		border-radius: 20px;
		transition: all 0.3s;
	}

	.card2 {
		width: 250px;
		height: 180px;
		background-color: #1a1a1a;
		border-radius: 20px;
		transition: all 0.2s;
		display: flex;
		padding: 15px;
		align-items: center;

		.battery-small {
			position: relative;
			width: 50px;
			height: 90px;
			margin-right: 15px;
			
			.battery-body {
				position: absolute;
				top: 5px;
				width: 100%;
				height: 80px;
				background: rgba(0, 0, 0, 0.3);
				border: 2px solid #01c2ff;
				border-radius: 5px;
				overflow: hidden;
				
				.battery-level {
					position: absolute;
					bottom: 0;
					width: 100%;
					background: linear-gradient(to top, #00ff75, #01c2ff);
					transition: height 1s ease-in-out;
				}
			}
			
			.battery-cap {
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 20px;
				height: 5px;
				background: #01c2ff;
				border-radius: 3px 3px 0 0;
			}
		}

		.message {
			width: 70%;
			color: #fff;

			.title {
				font-size: 18px;
				font-weight: bold;
				color: #01c2ff;
				margin-bottom: 15px;
			}

			.content {
				width: 100%;

				.item {
					display: flex;
					margin-bottom: 8px;
					
					.item-left {
						color: #8ecbff;
						width: 50px;
					}
					
					.item-right {
						font-weight: bold;
						color: #00ff75;
						
						&.warning {
							color: #ff6e76;
						}
					}
				}
			}
		}
	}

	.card2:hover {
		transform: scale(0.98);
	}

	.card:hover {
		box-shadow: 0px 0px 30px 1px rgba(0, 255, 117, 0.3);
	}
}

.home {
	position: relative;
	width: 100%;
	height: 100%;
	background: url('@/assets/images/index-bg.png') no-repeat;
	background-size: 100% 100%;

	.chart-list {
		height: 100%;

		.chart-content {
			height: calc(100% - 77px);
			margin-top: 12px;

			.chart-content-row,
			.chart-content-col {
				height: 100%;
			}

			.chart-container {
				width: 100%;
				height: 100%;
			}
		}
	}
}

// 小屏幕下的样式
@media (max-width: 576px) {
	.home {
		height: unset;
		background: #060c20;
	}
	
	.battery-group-container {
		.group-content {
			flex-direction: column;
		}
	}
}

@keyframes bubbleRise {
	0% {
		transform: translateY(0) scale(0.5);
		opacity: 0;
	}
	50% {
		opacity: 0.8;
		transform: translateY(-50px) scale(1);
	}
	100% {
		transform: translateY(-100px) scale(0.5);
		opacity: 0;
	}
}

@keyframes bubble {
	0% {
		background: linear-gradient(to top, #00ff75, #01c2ff);
	}
	50% {
		background: linear-gradient(to top, #01c2ff, #00ff75);
	}
	100% {
		background: linear-gradient(to top, #00ff75, #01c2ff);
	}
}

@keyframes discharge {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.7;
	}
	100% {
		opacity: 1;
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}

@keyframes alert-flash {
	0% {
		background: rgba(255, 110, 118, 0.2);
	}
	50% {
		background: rgba(255, 110, 118, 0.4);
	}
	100% {
		background: rgba(255, 110, 118, 0.2);
	}
}

// 智能算法演示模块样式
.algorithm-demo-section {
	background: rgba(1, 194, 255, 0.1);
	border: 1px solid rgba(1, 194, 255, 0.3);
	border-radius: 15px;
	padding: 20px;
	margin-top: 20px;
	
	.section-title {
		font-size: 24px;
		color: #01c2ff;
		margin-bottom: 25px;
		padding-left: 10px;
		border-left: 3px solid #2ca6ff;
		display: flex;
		align-items: center;
		
		.collaboration-tag {
			font-size: 14px;
			background: rgba(0, 255, 117, 0.2);
			color: #00ff75;
			padding: 4px 10px;
			border-radius: 15px;
			margin-left: 15px;
		}
	}
	
	.algorithm-container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		
		.algorithm-selector {
			background: rgba(1, 194, 255, 0.05);
			border-radius: 10px;
			padding: 15px;
			
			.selector-title {
				color: #8ecbff;
				font-size: 16px;
				margin-bottom: 15px;
			}
			
			.selector-options {
				display: flex;
				gap: 15px;
				flex-wrap: wrap;
				
				.algorithm-option {
					background: rgba(1, 194, 255, 0.1);
					border: 1px solid rgba(1, 194, 255, 0.2);
					border-radius: 10px;
					padding: 15px;
					display: flex;
					flex-direction: column;
					align-items: center;
					cursor: pointer;
					transition: all 0.3s ease;
					width: 100px;
					
					&:hover {
						background: rgba(1, 194, 255, 0.2);
					}
					
					&.active {
						background: rgba(1, 194, 255, 0.3);
						border: 1px solid rgba(1, 194, 255, 0.6);
						box-shadow: 0 0 15px rgba(1, 194, 255, 0.3);
					}
					
					.option-icon {
						font-size: 24px;
						margin-bottom: 10px;
					}
					
					.option-name {
						color: #ffffff;
						font-size: 14px;
					}
				}
			}
		}
		
		.algorithm-content {
			background: rgba(1, 194, 255, 0.05);
			border-radius: 10px;
			padding: 20px;
			min-height: 300px;
			
			.algorithm-detail {
				.detail-header {
					margin-bottom: 20px;
					
					.detail-title {
						color: #00ff75;
						font-size: 20px;
						font-weight: bold;
						margin-bottom: 5px;
					}
					
					.detail-description {
						color: #8ecbff;
						font-size: 14px;
					}
				}
				
				.detail-visualization {
					display: flex;
					gap: 20px;
					margin-bottom: 20px;
					
					.chart-container {
						flex: 1;
						min-height: 300px;
						background: rgba(0, 0, 0, 0.2);
						border-radius: 8px;
						padding: 15px;
						position: relative;
			
						.echarts-container {
							width: 100%;
							height: 270px;
							border-radius: 6px;
						}
					}
					
					.chart-info {
						width: 200px;
						
						.info-item {
							margin-bottom: 15px;
							
							.info-label {
								color: #8ecbff;
								font-size: 14px;
								margin-bottom: 5px;
							}
							
							.info-value {
								color: #ffffff;
								font-size: 18px;
								font-weight: bold;
							}
						}
					}
				}
				
				.detail-controls {
					display: flex;
					gap: 10px;
					margin-bottom: 20px;
					
					.control-button {
						background: rgba(1, 194, 255, 0.2);
						border: 1px solid rgba(1, 194, 255, 0.3);
						border-radius: 5px;
						padding: 8px 15px;
						color: #ffffff;
						cursor: pointer;
						transition: all 0.3s ease;
						
						&:hover {
							background: rgba(1, 194, 255, 0.3);
						}
						
						&.primary {
							background: rgba(0, 255, 117, 0.3);
							border: 1px solid rgba(0, 255, 117, 0.5);
							
							&:hover {
								background: rgba(0, 255, 117, 0.4);
							}
						}
					}
				}
				
				.algorithm-details {
					background: rgba(0, 0, 0, 0.2);
					border-radius: 8px;
					padding: 15px;
					
					.detail-method, .detail-parameters {
						margin-bottom: 15px;
						
						.method-title, .parameter-title {
							color: #01c2ff;
							font-size: 16px;
							margin-bottom: 10px;
						}
						
						.method-content {
							color: #8ecbff;
							font-size: 14px;
							
							p {
								margin-bottom: 10px;
							}
							
							ul {
								padding-left: 20px;
								
								li {
									margin-bottom: 5px;
								}
							}
						}
						
						.parameter-table {
							.parameter-row {
								display: flex;
								border-bottom: 1px dashed rgba(1, 194, 255, 0.2);
								padding: 8px 0;
								
								.parameter-name {
									width: 120px;
									color: #8ecbff;
								}
								
								.parameter-value {
									color: #ffffff;
								}
							}
						}
					}
				}
			}
			
			.algorithm-placeholder {
				height: 100%;
				min-height: 200px;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				
				.placeholder-icon {
					font-size: 40px;
					margin-bottom: 15px;
					opacity: 0.5;
				}
				
				.placeholder-text {
					color: #8ecbff;
					font-size: 16px;
				}
			}
		}
	}
}

// 媒体查询
@media (max-width: 768px) {
	.algorithm-container {
		.algorithm-content {
			.algorithm-detail {
				.detail-visualization {
					flex-direction: column;
				}
			}
		}
	}
}

.echarts-container {
	width: 100%;
	height: 300px;
	border-radius: 8px;
}

// 智能算法演示模块样式
.algorithm-demo-section {
	// ... existing code ...
	
	.algorithm-content {
		// ... existing code ...
		
		// 故障诊断算法样式
		.diagnostic-report {
			margin-top: 20px;
			background: rgba(0, 0, 0, 0.2);
			border-radius: 8px;
			padding: 15px;
			
			.report-title {
				color: #01c2ff;
				font-size: 16px;
				font-weight: bold;
			}
			
			.report-timestamp {
				color: #8ecbff;
				font-size: 13px;
				margin-bottom: 15px;
			}
			
			.report-section {
				margin-bottom: 20px;
				
				.section-title {
					color: #01c2ff;
					font-size: 15px;
					margin-bottom: 10px;
					border-bottom: 1px dashed rgba(1, 194, 255, 0.3);
					padding-bottom: 5px;
				}
				
				.fault-list {
					.fault-item {
						display: flex;
						margin-bottom: 10px;
						background: rgba(0, 0, 0, 0.2);
						border-radius: 5px;
						padding: 10px;
						
						.fault-severity {
							width: 4px;
							border-radius: 2px;
							margin-right: 10px;
							
							&.high {
								background: #ff6e76;
							}
							
							&.medium {
								background: #ffcc00;
							}
							
							&.low {
								background: #66ff00;
							}
						}
						
						.fault-details {
							flex: 1;
							
							.fault-title {
								color: #ffffff;
								font-size: 14px;
								margin-bottom: 5px;
							}
							
							.fault-description {
								color: #8ecbff;
								font-size: 13px;
							}
						}
					}
				}
				
				.action-list {
					.action-item {
						display: flex;
						margin-bottom: 8px;
						
						.action-number {
							flex: 0 0 25px;
							height: 25px;
							background: rgba(0, 255, 117, 0.2);
							border-radius: 4px;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-right: 10px;
							font-size: 12px;
							color: #00ff75;
						}
						
						.action-text {
							flex: 1;
							color: #ffffff;
							font-size: 14px;
							padding-top: 3px;
						}
					}
				}
				
				.maintenance-prediction {
					margin-top: 10px;
					background: rgba(1, 194, 255, 0.1);
					border-radius: 6px;
					padding: 15px;
					text-align: center;
					
					.maintenance-value {
						color: #ffffff;
						font-size: 18px;
						margin-bottom: 5px;
					}
					
					.maintenance-note {
						color: #8ecbff;
						font-size: 12px;
					}
				}
			}
		}
		
		// 均衡优化算法样式
		.strategy-details {
			margin-top: 20px;
			background: rgba(0, 0, 0, 0.2);
			border-radius: 8px;
			padding: 15px;
			
			.strategy-title {
				color: #01c2ff;
				font-size: 16px;
				margin-bottom: 10px;
			}
			
			.strategy-content {
				margin-bottom: 15px;
				
				.strategy-description {
					color: #8ecbff;
					font-size: 14px;
					margin-bottom: 15px;
				}
				
				.strategy-steps {
					.step {
						display: flex;
						margin-bottom: 10px;
						
						.step-number {
							flex: 0 0 25px;
							height: 25px;
							background: rgba(1, 194, 255, 0.3);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-right: 10px;
							font-size: 12px;
							color: #ffffff;
						}
						
						.step-content {
							flex: 1;
							color: #ffffff;
							font-size: 14px;
							padding-top: 3px;
						}
					}
				}
			}
			
			.benefits {
				.benefit-title {
					color: #01c2ff;
					font-size: 15px;
					margin-bottom: 10px;
				}
				
				.benefit-items {
					display: flex;
					flex-wrap: wrap;
					gap: 15px;
					
					.benefit-item {
						display: flex;
						align-items: center;
						background: rgba(0, 255, 117, 0.1);
						border-radius: 6px;
						padding: 8px 12px;
						
						.benefit-icon {
							margin-right: 8px;
							font-size: 16px;
						}
						
						.benefit-text {
							color: #ffffff;
							font-size: 13px;
						}
					}
				}
			}
		}
	}
}
// ... existing code ...

// 全局颜色修复
.algorithm-demo-section {
	// 确保文本颜色正确
	.diagnostic-report {
		color: #fff;
		
		.fault-item {
			.fault-details {
				.fault-title, .fault-description {
					color: #fff !important;
				}
			}
		}
	}
	
	.strategy-details {
		color: #fff;
		
		.step-content {
			color: #fff !important;
		}
		
		.benefit-text {
			color: #fff !important;
		}
	}
	
	// 热管理部分颜色修复
	.prediction-result {
		color: #fff;
		
		.prediction-title {
			color: #01c2ff;
		}
		
		.prediction-content {
			color: #fff !important;
			
			p {
				color: #fff !important;
			}
			
			.hotspot-markers {
				.hotspot-marker {
					color: #fff !important;
				}
			}
			
			.prediction-recommendation {
				color: #fff !important;
				
				strong {
					color: #00ff75;
				}
			}
		}
	}
	
	// 算法介绍部分颜色修复
	.algorithm-intro {
		color: #fff;
		
		.algorithm-title {
			color: #01c2ff;
			font-weight: bold;
			margin-bottom: 10px;
		}
		
		.algorithm-description {
			color: #fff !important;
			
			p {
				color: #fff !important;
				margin-bottom: 5px;
			}
			
			.key-features {
				color: #fff !important;
				
				li {
					color: #fff !important;
				}
				
				.feature-highlight {
					color: #00ff75;
				}
			}
		}
	}
	
	// 故障诊断算法介绍
	.fault-diagnosis-intro {
		color: #fff;
		margin-bottom: 15px;
		
		.algorithm-title {
			color: #01c2ff;
			font-weight: bold;
			margin-bottom: 10px;
		}
		
		.algorithm-description {
			color: #fff !important;
			
			p {
				color: #fff !important;
				margin-bottom: 5px;
			}
		}
	}
	
	// 均衡优化算法介绍
	.balancing-optimization-intro {
		color: #fff;
		margin-bottom: 15px;
		
		.algorithm-title {
			color: #01c2ff;
			font-weight: bold;
			margin-bottom: 10px;
		}
		
		.algorithm-description {
			color: #fff !important;
			
			p {
				color: #fff !important;
				margin-bottom: 5px;
			}
		}
	}
	
	// 热管理算法介绍
	.thermal-management-intro {
		color: #fff;
		margin-bottom: 15px;
		
		.algorithm-title {
			color: #01c2ff;
			font-weight: bold;
			margin-bottom: 10px;
		}
		
		.algorithm-description {
			color: #fff !important;
			
			p {
				color: #fff !important;
				margin-bottom: 5px;
			}
		}
	}
}

// 其它样式
// ... existing code ...

// 增强电池动画效果
@keyframes charging-effect {
  0% {
    background: linear-gradient(to top, #00ff75, #01c2ff);
    box-shadow: 0 0 5px rgba(1, 194, 255, 0.5);
  }
  50% {
    background: linear-gradient(to top, #01c2ff, #00ff75);
    box-shadow: 0 0 15px rgba(0, 255, 117, 0.7);
  }
  100% {
    background: linear-gradient(to top, #00ff75, #01c2ff);
    box-shadow: 0 0 5px rgba(1, 194, 255, 0.5);
  }
}

@keyframes discharging-effect {
  0% {
    background: linear-gradient(to top, #01c2ff, #00ff75);
    opacity: 1;
  }
  50% {
    background: linear-gradient(to top, #01c2ff 30%, #00ff75 100%);
    opacity: 0.7;
  }
  100% {
    background: linear-gradient(to top, #01c2ff, #00ff75);
    opacity: 1;
  }
}

.battery-group-container {
  // ... existing code ...
  
  .large-battery {
    // ... existing code ...
    
    .battery-visual {
      // ... existing code ...
      
      .battery-body {
        // ... existing code ...
        
        .battery-level {
          // ... existing code ...
          
          &.charging {
            animation: charging-effect 3s infinite;
            
            .bubbles {
              opacity: 1;
            }
          }
          
          &.discharging {
            animation: discharging-effect 4s infinite;
          }
        }
      }
    }
  }
}

.battery-cells-section {
  // ... existing code ...
  
  .card {
    // ... existing code ...
    
    .card2 {
      // ... existing code ...
      
      .battery-small {
        // ... existing code ...
        
        .battery-level {
          // ... existing code ...
          
          &.charging {
            animation: charging-effect 3s infinite;
          }
          
          &.discharging {
            animation: discharging-effect 4s infinite;
          }
        }
      }
    }
  }
}
// ... existing code ...

// 添加充放电曲线样式
.charging-status-chart {
	background: rgba(1, 194, 255, 0.1);
	border: 1px solid rgba(1, 194, 255, 0.3);
	border-radius: 10px;
	padding: 15px;
	margin-top: 20px;
	height: 500px;
	.chart-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		
		.chart-title {
			font-size: 20px;
			font-weight: 600;
			color: #01c2ff;
		}
		
		.estimated-time {
			color: rgba(0, 255, 117, 0.8);
			font-size: 16px;
			
			&.discharging {
				color: rgba(255, 99, 71, 0.8);
			}
		}
		
		.navigation-buttons {
			display: flex;
			gap: 10px;
			
			.nav-button {
				display: flex;
				align-items: center;
				background: rgba(0, 255, 117, 0.2);
				border-radius: 15px;
				padding: 5px 10px;
				cursor: pointer;
				transition: all 0.3s ease;
				
				&:hover {
					background: rgba(0, 255, 117, 0.3);
				}
				
				&:first-child {
					background: rgba(0, 255, 117, 0.2);
					
					&:hover {
						background: rgba(0, 255, 117, 0.3);
					}
				}
				
				&:last-child {
					background: rgba(1, 194, 255, 0.2);
					
					&:hover {
						background: rgba(1, 194, 255, 0.3);
					}
				}
				
				.icon {
					font-size: 24px;
					margin-right: 5px;
				}
				
				.text {
					font-size: 14px;
					color: #ffffff;
				}
			}
		}
	}
	
	.chart-container {
		height: 300px !important; // 增加高度
		// background: rgba(0, 0, 0, 0.2);
		border-radius: 8px;
		padding: 10px;
		
		.echarts-container {
			width: 100%;
			height: 400px;
		}
	}
}

// ... existing code ...
</style>

<style lang="scss">
.ant-tooltip-inner {
	min-height: unset;
}

.ant-form-item-label {
	text-align: left;
}

.ant-table-thead > tr > th {
	color: #fff;
	background-color: rgba($color: #2ca6ff, $alpha: 0.5);
}

.ant-table-tbody {
	color: #fff;
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table-tbody > tr.ant-table-row:hover > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.7);
}

.ant-table-tbody > tr.ant-table-row > td {
	background-color: rgba($color: #1e5c88, $alpha: 0.4);
}

.ant-table {
	background: none !important;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
