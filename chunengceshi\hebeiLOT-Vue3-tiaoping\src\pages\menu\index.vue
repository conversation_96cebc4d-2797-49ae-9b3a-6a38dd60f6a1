<template>
	<div class="home">
		<transition-loading :isShow="loadShow" />

		<div class="content">
			<home-header />
			<div class="menu-list">
				<div
					class="menu-list-li"
					v-for="menuItem in MenuList"
					:key="menuItem.id"
					@click="toPage(menuItem.router, menuItem.id)"
				>
					<div class="menu-list-li-content">
						<div class="menu-list-li-img-container">
							<img :src="getMenuImage(menuItem.id)" alt="" class="menu-list-li-img" />
						</div>
						<p class="menu-list-li-text">{{ menuItem.name }}</p>
					</div>
				</div>
			</div>
			<div class="quit" @click="quitEvnt">
				<LoginOutlined style="font-size: 35px; color: #fff" />
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { LoginOutlined } from '@ant-design/icons-vue';
import HomeHeader from './components/home-header/index.vue';
import { message } from 'ant-design-vue';
import { logout, getMenuList } from './service';

const loadShow = ref(false);
const MenuList = ref<Menu[]>([]);

// 根据菜单ID获取对应的图片
const getMenuImage = (id: number) => {
	// 根据不同的菜单ID返回对应的图片路径
	switch (id) {
		case 1: return '/assets/index/index.jpg'; // 首页
		case 2: return '/assets/index/05.jpg'; // 警报系统
		case 4: return '/assets/index/battery.jpg'; // 电池管理
		case 5: return '/assets/index/06.jpg'; // 日志系统
		case 6: return '/assets/index/08.jpg'; // 工况模拟
		case 7: return '/assets/index/07.jpg'; // PCS监控
		case 8: return '/assets/index/04.jpg'; // 台区站点监测
		case 9: return '/assets/index/02.jpg'; // 台区调度
		case 10: return '/assets/index/03.jpg'; // 一次调频控制系统
		default: return '/assets/index/index.jpg';
	}
};

const getMenu = async () => {
	// const res = await getMenuList()
	// MenuList.value = res.data.data
	// console.log(MenuList.value)
	MenuList.value = [
    {
        id: 1,
        name: '首页',
        router: '/home',
        sort_order: 1,
        icon: '' // 移除图标引用
    },
    {
        id: 2,
        name: '警报系统',
        router: '/warning',
        sort_order: 3,
        icon: ''
    },
    {
        id: 4,
        name: '电池管理',
        router: '/battery',
        sort_order: 2,
        icon: ''
    },
    {
        id: 5,
        name: '日志系统',
        router: '/setting',
        sort_order: 5,
        icon: ''
    },
    {
        id: 6,
        name: '工况模拟',
        router: '/workingtest',
        sort_order: 5,
        icon: ''
    },
    {
        id: 7,
        name: 'PCS监控',
        router: '/pcs',
        sort_order: 6,
        icon: ''
    },
    {
        id: 8,
        name: '台区站点监测',
        router: '/battery-monitor',
        sort_order: 7,
        icon: ''
    },
    {
        id: 9,
        name: '台区调度',
        router: '/dispatch-forecast',
        sort_order: 8,
        icon: ''
    },
    {
        id: 10,
        name: '一次调频控制系统',
        router: '/frequency-control',
        sort_order: 9,
        icon: ''
    }
];
};

import { useRouter } from 'vue-router';
import Menu from './model';

const router = useRouter();
function toPage(url: string, id?: number | string) {
	// 检查id是否定义且不为null
	if (id !== undefined && id !== null) {
		// 将id作为查询参数传递
		router.push({ path: url, query: { id } });
	} else {
		router.push(url);
	}
}
const replacePage = (pageUrl: string) => {
	router.replace({ path: pageUrl });
};

const quitEvnt = async () => {
	try {
		// 通知服务器销毁会话
		await logout();

		// 清除 Cookies 中的会话数据
		document.cookie.split(';').forEach(c => {
			document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
		});

		// 显示成功消息
		message.success('退出登录成功');

		// 重定向到登录页面
		replacePage('/login');
	} catch (error) {
		console.error('退出登录失败', error);
		message.error('退出登录失败，请重试');
	}
};
onMounted(() => {
	getMenu();
});
</script>
<style lang="scss" scoped>
@media (min-width: 480px) {
	.home {
		position: relative;
		width: 100%;
		height: 100%;
		background: url('@/assets/images/index-bg.png') no-repeat;
		background-size: 100% 100%;

		.content {
			height: 100%;

			.menu-list {
				padding-top: 40px;
				width: 85%;
				margin: auto;
				display: flex;
				gap: 30px;
				flex-wrap: wrap;
				&-li {
					position: relative;
					z-index: 3;
					width: 350px;
					height: 180px;
					background-color: rgba(6, 12, 32, 0.7);
					border-radius: 12px;
					display: flex;
					justify-content: center;
					align-items: center;
					cursor: pointer;
					transition: all 0.5s;
					overflow: hidden;
					
					&-content {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 100%;
					}
					
					&-img-container {
						width: 100%;
						height: 75%;
						overflow: hidden;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					
					&-img {
						width: 100%;
						height: 100%;
						object-fit: cover;
						transition: all 0.3s;
					}
					
					&:hover {
						background-color: var(--module-bg-hover);
						
						.menu-list-li-img {
							transform: scale(1.05);
						}

						.menu-list-li-text {
							color: #ffffff !important;
						}
					}

					&-text {
						color: #fff;
						font-size: 22px;
						margin: 10px 0;
						text-align: center;
					}
				}
			}

			.quit {
				position: absolute;
				top: 1%;
				right: 1%;
				cursor: pointer;
			}
		}
	}
}

@media (max-width: 480px) {
	.home {
		position: relative;
		width: 100%;
		height: 100%;
		background: url('@/assets/images/index-bg.png') no-repeat;
		background-size: 100% 100%;

		.content {
			height: 100%;

			.menu-list {
				padding-top: 40px;
				width: 85%;
				margin: auto;
				display: flex;
				gap: 30px;
				flex-wrap: wrap;
				justify-content: center;
				&-li {
					position: relative;
					z-index: 3;
					width: 80vw;
					height: 150px;
					background-color: rgba(6, 12, 32, 0.7);
					border-radius: 12px;
					display: flex;
					justify-content: center;
					align-items: center;
					cursor: pointer;
					transition: all 0.5s;
					overflow: hidden;
					
					&-content {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 100%;
					}
					
					&-img-container {
						width: 100%;
						height: 70%;
						overflow: hidden;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					
					&-img {
						width: 100%;
						height: 100%;
						object-fit: cover;
						transition: all 0.3s;
					}
					
					&:hover {
						background-color: var(--module-bg-hover);
						
						.menu-list-li-img {
							transform: scale(1.05);
						}

						.menu-list-li-text {
							color: #ffffff !important;
						}
					}

					&-text {
						color: #fff;
						font-size: 20px;
						margin: 8px 0;
						text-align: center;
					}
				}
			}

			.quit {
				position: absolute;
				top: 1%;
				right: 1%;
				cursor: pointer;
			}
		}
	}
}

// 小屏幕下的样式
// @media (max-width: 576px) {
// 	.home {
// 		height: unset;
// 		background: #060c20;

// 		.chart-content {
// 			.chart-content-col:first-child {
// 				height: 1000px !important;
// 			}

// 			&-left,
// 			&-center {
// 				&-item {
// 					flex: 1 !important;
// 				}
// 			}

// 			.chart-content-col:nth-child(2) {
// 				height: 1500px !important;
// 			}

// 			.chart-content-col:nth-child(3) {
// 				height: 1500px !important;
// 			}
// 		}
// 	}
// }
</style>

<style lang="scss">
.ant-tooltip-inner {
	min-height: unset;
}

.tooltip-review {
	// width: 80%;
	overflow: hidden;

	.tooltip-title {
		width: 180px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tooltip-btn {
		width: max-content;
		padding: 2px 5px;
		margin: 5px 5px 0 0;
		color: #ffffff;
		cursor: pointer;
		background-color: #ff6e76;
		border-radius: 4px;
	}

	.tooltip-item {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tooltip-label-icon {
		display: flex;
		align-items: center;
		margin-right: 5px;
		overflow: hidden;

		.tooltip-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.tooltip-icon {
			width: 6px;
			height: 6px;
			margin-right: 5px;
			border-radius: 50%;
		}
	}

	.tooltip-value {
		flex: 1;
		flex-shrink: 0;
		font-size: 15px;
		font-weight: bold;
		color: #666666;
	}
}
</style>
