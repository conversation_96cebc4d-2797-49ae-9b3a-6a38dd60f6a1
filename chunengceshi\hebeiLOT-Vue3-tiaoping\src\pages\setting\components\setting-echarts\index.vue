<template>
	<div ref="chart" :style="{ width: '100%', height: '85%' }"></div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
	name: 'EChartsComponent',
	props: {
		data: {
			type: Array,
			required: true
		}
	},
	setup(props) {
		const chart = ref(null);

		const initChart = () => {
			const chartInstance = echarts.init(chart.value);
			const option = {
				tooltip: {
					trigger: 'axis',
					textStyle: {
						color: '#000000' // 设置 tooltip 中字体为黑色
					},
					backgroundColor: '#ffffff', // 设置 tooltip 背景色为白色
					borderColor: '#cccccc', // 设置 tooltip 边框颜色
					borderWidth: 1
				},
				legend: {
					data: ['电池电压', '电池电流', '电池温度', '有功功率', '无功功率'],
					textStyle: {
						color: '#ffffff'
					}
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: props.data.map(item => item.time),
					axisLabel: {
						color: '#ffffff'
					}
				},
				yAxis: {
					type: 'value',
					axisLabel: {
						color: '#ffffff'
					}
				},
				series: [
					{
						name: '电池电压',
						type: 'line',
						data: props.data.map(item => item.battery_voltage),
						lineStyle: {
							width: 3 // 线条粗细
						}
					},
					{
						name: '电池电流',
						type: 'line',
						data: props.data.map(item => item.battery_current),
						lineStyle: {
							width: 3 // 线条粗细
						}
					},
					{
						name: '电池温度',
						type: 'line',
						data: props.data.map(item => item.battery_temperature),
						lineStyle: {
							width: 3 // 线条粗细
						}
					},
					{
						name: '有功功率',
						type: 'line',
						data: props.data.map(item => item.active_power),
						lineStyle: {
							width: 3 // 线条粗细
						}
					},
					{
						name: '无功功率',
						type: 'line',
						data: props.data.map(item => item.reactive_power),
						lineStyle: {
							width: 3 // 线条粗细
						}
					}
				]
			};

			chartInstance.setOption(option);
		};

		onMounted(() => {
			initChart();
		});

		watch(props.data, () => {
			initChart();
		});

		return {
			chart
		};
	}
});
</script>
