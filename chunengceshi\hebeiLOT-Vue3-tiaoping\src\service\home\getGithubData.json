{"msg": "操作成功", "data": {"list": [{"project_id": 1, "influence": "68.59", "response": "10.94", "activity": "52.48", "trend": "150.00", "github": "78.26", "name": "AdguardTeam/AdguardFilters"}, {"project_id": 2, "influence": "50.00", "response": "57.65", "activity": "82.70", "trend": "66.17", "github": "62.92", "name": "airbytehq/airbyte"}, {"project_id": 3, "influence": "24.30", "response": "27.27", "activity": "51.25", "trend": "36.42", "github": "33.92", "name": "alibaba/nacos"}, {"project_id": 4, "influence": "41.81", "response": "35.40", "activity": "68.98", "trend": "33.50", "github": "43.47", "name": "angular/angular"}, {"project_id": 5, "influence": "18.46", "response": "20.98", "activity": "65.53", "trend": "30.89", "github": "32.11", "name": "angular/components"}, {"project_id": 6, "influence": "22.38", "response": "20.69", "activity": "49.20", "trend": "30.06", "github": "29.71", "name": "ankidroid/Anki-Android"}, {"project_id": 7, "influence": "27.83", "response": "26.23", "activity": "35.76", "trend": "35.12", "github": "31.29", "name": "ansible/ansible"}, {"project_id": 8, "influence": "46.71", "response": "46.98", "activity": "77.00", "trend": "50.11", "github": "53.84", "name": "ant-design/ant-design"}, {"project_id": 9, "influence": "45.43", "response": "40.93", "activity": "52.43", "trend": "49.36", "github": "47.11", "name": "apache/airflow"}, {"project_id": 10, "influence": "21.44", "response": "16.55", "activity": "44.19", "trend": "33.69", "github": "28.69", "name": "apache/apisix"}, {"project_id": 11, "influence": "36.41", "response": "36.44", "activity": "42.08", "trend": "25.40", "github": "34.25", "name": "apache/arrow"}, {"project_id": 12, "influence": "38.67", "response": "34.71", "activity": "48.98", "trend": "47.31", "github": "42.53", "name": "apache/beam"}, {"project_id": 13, "influence": "33.51", "response": "42.27", "activity": "62.27", "trend": "42.35", "github": "43.66", "name": "apache/dolphinscheduler"}, {"project_id": 14, "influence": "31.76", "response": "55.29", "activity": "78.11", "trend": "42.43", "github": "48.94", "name": "apache/doris"}, {"project_id": 17, "influence": "19.08", "response": "28.53", "activity": "79.30", "trend": "27.66", "github": "35.59", "name": "apache/iceberg"}, {"project_id": 18, "influence": "35.52", "response": "54.87", "activity": "67.09", "trend": "40.99", "github": "47.34", "name": "apache/pulsar"}, {"project_id": 19, "influence": "31.03", "response": "33.89", "activity": "38.13", "trend": "51.42", "github": "39.14", "name": "apache/shardingsphere"}, {"project_id": 21, "influence": "36.02", "response": "53.38", "activity": "78.93", "trend": "41.13", "github": "49.61", "name": "apache/superset"}, {"project_id": 22, "influence": "21.54", "response": "32.05", "activity": "71.14", "trend": "30.63", "github": "36.29", "name": "apache/tvm"}, {"project_id": 23, "influence": "45.31", "response": "39.99", "activity": "51.93", "trend": "37.32", "github": "43.17", "name": "apple/swift"}, {"project_id": 24, "influence": "57.12", "response": "55.48", "activity": "79.55", "trend": "65.95", "github": "63.93", "name": "appsmithorg/appsmith"}, {"project_id": 26, "influence": "24.00", "response": "38.96", "activity": "78.53", "trend": "28.66", "github": "39.30", "name": "ArduPilot/ardupilot"}, {"project_id": 27, "influence": "24.33", "response": "32.38", "activity": "84.33", "trend": "36.88", "github": "41.70", "name": "argoproj/argo-cd"}, {"project_id": 29, "influence": "31.47", "response": "35.53", "activity": "87.40", "trend": "40.86", "github": "46.29", "name": "Automattic/jetpack"}, {"project_id": 30, "influence": "58.42", "response": "31.40", "activity": "79.34", "trend": "68.78", "github": "60.31", "name": "Automattic/wp-calypso"}], "total": 223}, "code": 200}