<template>
  <div class="pcs-debug">
    <div class="section-header">PCS调试</div>
    
    <div class="debug-container">
      <div class="debug-panel">
        <div class="panel-header">PCS控制</div>
        <div class="panel-content">
          <div class="control-group">
            <div class="control-label">工作状态控制:</div>
            <div class="control-actions">
              <button class="action-btn start-btn" @click="startPcs" :disabled="isRunning">启动</button>
              <button class="action-btn stop-btn" @click="stopPcs" :disabled="!isRunning">停止</button>
              <button class="action-btn reset-btn" @click="resetPcs">复位</button>
            </div>
          </div>
          
          <div class="control-group">
            <div class="control-label">功率控制:</div>
            <div class="control-actions slider-control">
              <div class="slider-label">有功功率设定(kW):</div>
              <div class="slider-container">
                <input type="range" min="-100" max="100" v-model="activePower" class="slider" />
                <div class="slider-value">{{ activePower }}kW</div>
              </div>
              <button class="action-btn apply-btn" @click="applyPowerSetting" :disabled="!isRunning">应用</button>
            </div>
          </div>
          
          <div class="control-group">
            <div class="control-label">功率因数控制:</div>
            <div class="control-actions slider-control">
              <div class="slider-label">功率因数设定:</div>
              <div class="slider-container">
                <input type="range" min="0.8" max="1" step="0.01" v-model="powerFactor" class="slider" />
                <div class="slider-value">{{ powerFactor }}</div>
              </div>
              <button class="action-btn apply-btn" @click="applyPowerFactorSetting" :disabled="!isRunning">应用</button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="debug-panel">
        <div class="panel-header">PCS状态监测</div>
        <div class="panel-content">
          <div class="status-display">
            <div class="status-row">
              <div class="status-label">运行状态:</div>
              <div class="status-value" :class="{ 'status-active': isRunning }">
                {{ isRunning ? '运行中' : '已停止' }}
              </div>
            </div>
            
            <div class="status-row">
              <div class="status-label">电网连接:</div>
              <div class="status-value" :class="{ 'status-active': isGridConnected }">
                {{ isGridConnected ? '已连接' : '未连接' }}
              </div>
            </div>
            
            <div class="status-row">
              <div class="status-label">故障状态:</div>
              <div class="status-value" :class="{ 'status-fault': hasFault }">
                {{ hasFault ? '有故障' : '正常' }}
              </div>
            </div>
            
            <div class="status-row">
              <div class="status-label">电池连接:</div>
              <div class="status-value" :class="{ 'status-active': isBatteryConnected }">
                {{ isBatteryConnected ? '已连接' : '未连接' }}
              </div>
            </div>
          </div>
          
          <div class="realtime-data">
            <div class="data-header">实时数据</div>
            <div class="data-metrics">
              <div class="metric-item">
                <div class="metric-label">输出功率:</div>
                <div class="metric-value">{{ outputPower }} kW</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">输出电压:</div>
                <div class="metric-value">{{ outputVoltage }} V</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">输出电流:</div>
                <div class="metric-value">{{ outputCurrent }} A</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">电网频率:</div>
                <div class="metric-value">{{ gridFrequency }} Hz</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">设备温度:</div>
                <div class="metric-value">{{ deviceTemp }} °C</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="debug-log">
      <div class="log-header">
        <div class="log-title">调试日志</div>
        <button class="clear-log-btn" @click="clearLogs">清空日志</button>
      </div>
      <div class="log-content">
        <div v-for="(log, index) in debugLogs" :key="index" class="log-item" :class="log.type">
          <div class="log-time">{{ log.time }}</div>
          <div class="log-message">{{ log.message }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 控制变量
const activePower = ref(0);
const powerFactor = ref(1);

// 状态变量
const isRunning = ref(false);
const isGridConnected = ref(true);
const hasFault = ref(false);
const isBatteryConnected = ref(true);

// 实时数据
const outputPower = ref(0);
const outputVoltage = ref(220);
const outputCurrent = ref(0);
const gridFrequency = ref(50);
const deviceTemp = ref(35);

// 调试日志
const debugLogs = ref([
  { time: '10:25:30', message: '系统初始化完成', type: 'info' },
  { time: '10:26:15', message: '检测到电网连接', type: 'info' },
  { time: '10:28:45', message: '电池连接正常', type: 'info' },
  { time: '10:30:10', message: '功率设定值已更新为0kW', type: 'success' },
]);

// 获取当前时间字符串
const getCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
};

// 添加日志
const addLog = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  debugLogs.value.unshift({ 
    time: getCurrentTime(),
    message,
    type
  });
};

// 启动PCS
const startPcs = () => {
  if (!isRunning.value) {
    isRunning.value = true;
    addLog('PCS系统已启动', 'success');
    
    // 模拟启动后电流和功率变化
    setTimeout(() => {
      outputCurrent.value = 10;
      outputPower.value = 20;
      addLog('输出电流: 10A, 输出功率: 20kW', 'info');
    }, 1000);
  }
};

// 停止PCS
const stopPcs = () => {
  if (isRunning.value) {
    isRunning.value = false;
    addLog('PCS系统已停止', 'warning');
    
    // 模拟停止后电流和功率归零
    setTimeout(() => {
      outputCurrent.value = 0;
      outputPower.value = 0;
      addLog('输出电流: 0A, 输出功率: 0kW', 'info');
    }, 800);
  }
};

// 复位PCS
const resetPcs = () => {
  isRunning.value = false;
  hasFault.value = false;
  activePower.value = 0;
  powerFactor.value = 1;
  outputCurrent.value = 0;
  outputPower.value = 0;
  
  addLog('PCS系统已复位', 'warning');
  
  // 模拟重启过程
  setTimeout(() => {
    addLog('系统自检中...', 'info');
    
    setTimeout(() => {
      addLog('通信模块检查通过', 'info');
      
      setTimeout(() => {
        addLog('系统参数恢复默认值', 'info');
        
        setTimeout(() => {
          addLog('系统复位完成', 'success');
        }, 600);
      }, 500);
    }, 800);
  }, 500);
};

// 应用功率设置
const applyPowerSetting = () => {
  addLog(`设定有功功率为 ${activePower.value}kW`, 'info');
  
  // 模拟功率变化过程
  const interval = setInterval(() => {
    if (Math.abs(outputPower.value - activePower.value) < 5) {
      outputPower.value = activePower.value;
      clearInterval(interval);
      addLog(`输出功率已调整至 ${outputPower.value}kW`, 'success');
    } else {
      outputPower.value += outputPower.value < activePower.value ? 5 : -5;
    }
  }, 500);
};

// 应用功率因数设置
const applyPowerFactorSetting = () => {
  addLog(`设定功率因数为 ${powerFactor.value}`, 'info');
  setTimeout(() => {
    addLog(`功率因数已调整至 ${powerFactor.value}`, 'success');
  }, 800);
};

// 清空日志
const clearLogs = () => {
  debugLogs.value = [];
  addLog('调试日志已清空', 'info');
};
</script>

<style scoped lang="scss">
.pcs-debug {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  color: #e0e0e0;
  
  .section-header {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #00a8ff;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  }
  
  .debug-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .debug-panel {
    background-color: rgba(0, 40, 80, 0.3);
    border: 1px solid rgba(0, 168, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
    
    .panel-header {
      background-color: rgba(0, 60, 120, 0.5);
      padding: 12px 15px;
      font-weight: bold;
      color: #00a8ff;
      border-bottom: 1px solid rgba(0, 168, 255, 0.3);
    }
    
    .panel-content {
      padding: 15px;
    }
  }
  
  .control-group {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .control-label {
      font-weight: bold;
      margin-bottom: 10px;
      color: #00a8ff;
    }
    
    .control-actions {
      display: flex;
      gap: 10px;
      
      &.slider-control {
        display: block;
        
        .slider-label {
          margin-bottom: 8px;
        }
        
        .slider-container {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          
          .slider {
            flex: 1;
            height: 10px;
            background: rgba(0, 68, 102, 0.5);
            border-radius: 5px;
            outline: none;
            -webkit-appearance: none;
            
            &::-webkit-slider-thumb {
              -webkit-appearance: none;
              appearance: none;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              background: #00a8ff;
              cursor: pointer;
            }
          }
          
          .slider-value {
            width: 70px;
            text-align: right;
            margin-left: 10px;
          }
        }
      }
    }
  }
  
  .action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #004477;
    color: white;
    
    &:hover {
      background-color: #005599;
      box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      &:hover {
        box-shadow: none;
        background-color: #004477; // 重置回默认颜色
        
        &.start-btn {
          background-color: #007733;
        }
        
        &.stop-btn {
          background-color: #aa3300;
        }
        
        &.apply-btn {
          background-color: #0066aa;
        }
      }
    }
  }
  
  .status-display {
    margin-bottom: 20px;
    
    .status-row {
      display: flex;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .status-label {
        width: 120px;
      }
      
      .status-value {
        padding: 2px 10px;
        border-radius: 10px;
        font-size: 14px;
        background-color: rgba(100, 100, 100, 0.3);
        
        &.status-active {
          background-color: rgba(0, 150, 0, 0.3);
          color: #00ff00;
        }
        
        &.status-fault {
          background-color: rgba(150, 0, 0, 0.3);
          color: #ff5555;
        }
      }
    }
  }
  
  .realtime-data {
    .data-header {
      font-weight: bold;
      margin-bottom: 15px;
      color: #00a8ff;
    }
    
    .data-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      
      .metric-item {
        display: flex;
        
        .metric-label {
          width: 100px;
        }
        
        .metric-value {
          font-weight: bold;
          color: #00ddff;
        }
      }
    }
  }
  
  .debug-log {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: rgba(0, 40, 80, 0.3);
    border: 1px solid rgba(0, 168, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
    
    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: rgba(0, 60, 120, 0.5);
      padding: 10px 15px;
      border-bottom: 1px solid rgba(0, 168, 255, 0.3);
      
      .log-title {
        font-weight: bold;
        color: #00a8ff;
      }
      
      .clear-log-btn {
        padding: 5px 10px;
        background-color: #333;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        
        &:hover {
          background-color: #444;
        }
      }
    }
    
    .log-content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      
      .log-item {
        display: flex;
        margin-bottom: 6px;
        padding: 4px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        
        &:last-child {
          margin-bottom: 0;
          border-bottom: none;
        }
        
        &.info {
          color: #aaaaff;
        }
        
        &.success {
          color: #aaffaa;
        }
        
        &.warning {
          color: #ffdd66;
        }
        
        &.error {
          color: #ff8866;
        }
        
        .log-time {
          width: 100px;
          opacity: 0.8;
        }
        
        .log-message {
          flex: 1;
        }
      }
    }
  }
}
</style> 