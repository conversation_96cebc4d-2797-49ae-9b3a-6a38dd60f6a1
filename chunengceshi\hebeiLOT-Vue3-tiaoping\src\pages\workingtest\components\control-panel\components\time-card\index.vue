<template>
	<div class="card">
		<p class="time-text">
			<span>{{ hours }}:{{ minutes }}:{{ seconds }}:{{ milliseconds }}</span>
			<span class="time-sub-text"></span>
		</p>
	</div>
</template>




<script setup>
import { defineProps } from 'vue';

const props = defineProps({
	hours: {
		type: String,
		required: true
	},
	minutes: {
		type: String,
		required: true
	},
	seconds: {
		type: String,
		required: true
	},
	milliseconds: {
		type: String,
		required: true
	}
});
</script>

<style scoped>
.card {
	width: 250px;
	height: 80px;
	background-color: rgba(35, 132, 221, 0.6);
	display: flex;
	color: white;
	justify-content: center;
	position: relative;
	margin-top: 40px;
}

.time-text {
	font-size: 40px;
	margin-top: 8px;
	/* margin-left: 15px; */
	font-weight: 600;
	font-family: 'Gill Sans', 'Gill Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
}

.time-sub-text {
	font-size: 15px;
	margin-left: 5px;
}

.day-text {
	font-size: 18px;
	margin-top: 0px;
	margin-left: 15px;
	font-weight: 500;
	font-family: 'Gill Sans', '<PERSON>s MT', Calibri, 'Trebuchet MS', sans-serif;
}

.moon {
	font-size: 20px;
	position: absolute;
	right: 15px;
	top: 15px;
	transition: all 0.3s ease-in-out;
}

.card:hover > .moon {
	font-size: 23px;
}
</style>
