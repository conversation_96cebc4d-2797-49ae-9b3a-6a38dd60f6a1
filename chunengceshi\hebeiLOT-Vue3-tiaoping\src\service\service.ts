import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import qs from 'qs';
import router from '@/router';
import { message } from 'ant-design-vue';

const env = import.meta.env.DEV;
const defaultContentType = 'application/x-www-form-urlencoded; charset=UTF-8';
const service = axios.create({
	baseURL: '/api', // api的base_url
	// baseURL: 'http://**************:20708/api',
	timeout: 50000, // 请求超时时间
	withCredentials: true, // 跨域携带cookie
	validateStatus: (status: number) => {
		return status >= 200 && status <= 500;
	}
});

/**
 * @description 处理开发环境打包环境接口url
 * @returns url
 */
const formatUrl = (config: InternalAxiosRequestConfig<any>): string | undefined => {
	if (!config.url) return;
	// 如果是开发环境
	if (env) {
		const envPath = '/api';
		return `${envPath}${config.url}`;
	} else {
		const envPath = '/api';
		return `${envPath}${config.url}`;
		// return `${config.url}`;
	}
};

/**
 * request拦截器 axios
 * config是axios.post或者get第三个参数的config配置。
 * post和get第一，二个参数实际上是第三个的子集。
 * config可以包含所有axios可用参数，和本系统扩展参数例如：baseUrl
 */
service.interceptors.request.use(
	config => {
		config.url = formatUrl(config);
		// 默认post都是 application/x-www-form-urlencoded; charset=UTF-8，需要转换成string
		if (config.headers['content-type'] === defaultContentType) {
			config.data = qs.stringify(config.data);
		}
		return config;
	},
	(error: AxiosError) => Promise.reject(error)
);

/**
 * response拦截器 axios
 * 处理401，主要是后端的登录重定向，后端其实不一定是登录的重定向
 * 302重定向。同上，后端其实不一定是登录的重定向。
 */
service.interceptors.response.use(
	response => {
		// 处理返回的code不为1的情况
		if (response.data.code !== 1) {
			// message.error(response.data.data || 'Error');
		}
		if (response.data.code == 401) {
			message.error(response.data.data || '登录失效');
			router.push('/login');
		}
		return response;
	},
	(error: AxiosError) => {
		if (error.response?.status === 401) {
			// 处理401登录过期的情况
			message.error('登录过期，请重新登录');
			router.push('/login');
		} else {
			message.error(error.response?.data.message || 'Error');
		}
		return Promise.reject(error);
	}
);

export default service;
