/*
 * @Author: wwoop <EMAIL>
 * @Date: 2024-08-07 14:22:02
 * @LastEditors: wwoop <EMAIL>
 * @LastEditTime: 2024-08-08 13:07:50
 * @FilePath: \DM_screen_web\src\pages\workingtest\components\template-status\service.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { GET, POST, PUT, DELETE, DOWNLOAD } from '@/service/api';

/**
 * 创建新的工作模板及配置
 * @param data 包含模板和配置的数据
 * @returns 返回创建结果
 */
export const createTemplateWithConfigurations = (data) => {
  return POST('/work', data);
};

/**
 * 更新指定ID的工作模板及配置
 * @param id 模板ID
 * @param data 更新后的模板和配置数据
 * @returns 返回更新结果
 */
export const updateTemplateWithConfigurations = (id, data) => {
  return PUT(`/work/${id}`, data);
};

/**
 * 根据ID获取工作模板及配置
 * @param id 模板ID
 * @returns 返回模板及配置数据
 */
export const getTemplateById = (id) => {
  return GET(`/work/${id}`);
};

/**
 * 删除指定ID的工作模板及配置
 * @param id 模板ID
 * @returns 返回删除结果
 */
export const deleteTemplateWithConfigurations = (id) => {
  return DELETE(`/work/${id}`);
};

/**
 * 获取所有工作模板及配置
 * @returns 返回所有模板及配置数据
 */
export const getAllTemplates = () => {
  return GET('/work');
};

/**
 * 导入数据
 * @param file 文件数据
 * @returns 返回导入结果
 */
export const importData = (file) => {
  const formData = new FormData();
  formData.append('file', file);
  return POST('/work/import', formData);
};

/**
 * 导出数据
 * @returns 返回导出结果
 */
export const exportData = (templateID) => {
  return DOWNLOAD(`/work/export/${templateID}`);
};