import { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'

const batteryMonitorRoutes: Array<RouteRecordRaw> = [
  {
    path: '/battery-monitor',
    name: 'BatteryMonitor',
    component: Layout,
    redirect: '/battery-monitor/index',
    meta: {
      title: '储能监控',
      icon: 'dashboard',
    },
    children: [
      {
        path: 'index',
        name: 'BatteryMonitorIndex',
        component: () => import('@/pages/battery-monitor/index.vue'),
        meta: {
          title: '储能监控',
          icon: 'dashboard',
        },
      },
    ],
  },
]

export default batteryMonitorRoutes 