<template>
  <div class="pcs-fault">
    <div class="section-header">PCS故障</div>
    
    <div class="fault-controls">
      <div class="filter-group">
        <div class="filter-label">故障类型:</div>
        <select v-model="faultTypeFilter" class="filter-select">
          <option value="all">全部</option>
          <option value="hardware">硬件故障</option>
          <option value="software">软件故障</option>
          <option value="communication">通信故障</option>
          <option value="grid">电网故障</option>
          <option value="battery">电池故障</option>
        </select>
      </div>
      
      <div class="filter-group">
        <div class="filter-label">故障等级:</div>
        <select v-model="faultLevelFilter" class="filter-select">
          <option value="all">全部</option>
          <option value="critical">严重</option>
          <option value="major">重要</option>
          <option value="minor">次要</option>
        </select>
      </div>
      
      <div class="filter-group">
        <div class="filter-label">时间范围:</div>
        <select v-model="timeRangeFilter" class="filter-select">
          <option value="today">今天</option>
          <option value="week">最近一周</option>
          <option value="month">最近一个月</option>
          <option value="all">全部</option>
        </select>
      </div>
      
      <div class="action-group">
        <button class="refresh-btn" @click="refresh" :class="{ refreshing: isRefreshing }">
          <span class="refresh-icon">↻</span>
          刷新
        </button>
        <button class="export-btn" @click="exportData">导出</button>
      </div>
    </div>
    
    <div class="fault-summary">
      <div class="summary-card critical">
        <div class="summary-value">{{ criticalCount }}</div>
        <div class="summary-label">严重故障</div>
      </div>
      <div class="summary-card major">
        <div class="summary-value">{{ majorCount }}</div>
        <div class="summary-label">重要故障</div>
      </div>
      <div class="summary-card minor">
        <div class="summary-value">{{ minorCount }}</div>
        <div class="summary-label">次要故障</div>
      </div>
      <div class="summary-card total">
        <div class="summary-value">{{ totalFaults }}</div>
        <div class="summary-label">故障总数</div>
      </div>
    </div>
    
    <div class="fault-table">
      <div class="table-header">
        <div class="header-cell time">发生时间</div>
        <div class="header-cell code">故障代码</div>
        <div class="header-cell type">故障类型</div>
        <div class="header-cell level">故障等级</div>
        <div class="header-cell message">故障描述</div>
        <div class="header-cell status">状态</div>
        <div class="header-cell action">操作</div>
      </div>
      
      <div class="table-body">
        <div v-for="(fault, index) in filteredFaults" :key="index" class="table-row" :class="fault.level">
          <div class="table-cell time">{{ fault.time }}</div>
          <div class="table-cell code">{{ fault.code }}</div>
          <div class="table-cell type">{{ fault.type }}</div>
          <div class="table-cell level">
            <span class="level-tag" :class="fault.level">{{ fault.levelText }}</span>
          </div>
          <div class="table-cell message">{{ fault.message }}</div>
          <div class="table-cell status">{{ fault.status }}</div>
          <div class="table-cell action">
            <button class="action-btn details-btn" @click="viewFaultDetails(fault)">详情</button>
            <button class="action-btn clear-btn" v-if="fault.status === '未清除'" @click="clearFault(index)">清除</button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="pagination">
      <button class="page-btn prev" @click="goToPrevPage" :disabled="currentPage === 1">上一页</button>
      <div class="page-info">第 {{ currentPage }} 页 / 共 {{ totalPages }} 页</div>
      <button class="page-btn next" @click="goToNextPage" :disabled="currentPage === totalPages">下一页</button>
    </div>
    
    <!-- 故障详情模态框 -->
    <div class="modal-overlay" v-if="showDetailModal" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header" :class="currentFaultDetail?.level">
          <div class="modal-title">故障详情 - {{ currentFaultDetail?.code }}</div>
          <button class="modal-close" @click="closeDetailModal">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-section">
            <div class="detail-item">
              <div class="detail-label">故障代码:</div>
              <div class="detail-value">{{ currentFaultDetail?.code }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">发生时间:</div>
              <div class="detail-value">{{ currentFaultDetail?.time }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">故障类型:</div>
              <div class="detail-value">{{ currentFaultDetail?.type }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">故障等级:</div>
              <div class="detail-value">
                <span class="level-tag" :class="currentFaultDetail?.level">{{ currentFaultDetail?.levelText }}</span>
              </div>
            </div>
            <div class="detail-item">
              <div class="detail-label">故障描述:</div>
              <div class="detail-value">{{ currentFaultDetail?.message }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">状态:</div>
              <div class="detail-value">{{ currentFaultDetail?.status }}</div>
            </div>
          </div>
          
          <div class="detail-section">
            <div class="section-title">故障详情</div>
            <div v-for="(value, key) in currentFaultDetail?.details" :key="key" class="detail-item">
              <div class="detail-label">{{ formatLabel(key) }}:</div>
              <div class="detail-value">{{ value }}</div>
            </div>
          </div>
          
          <div class="detail-section solution" v-if="currentFaultDetail?.details?.solution">
            <div class="section-title">解决方案</div>
            <div class="solution-text">{{ currentFaultDetail?.details?.solution }}</div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="modal-btn" @click="closeDetailModal">关闭</button>
          <button class="modal-btn primary" v-if="currentFaultDetail?.status === '未清除'">清除故障</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 定义类型接口
interface FaultDetail {
  time: string;
  code: string;
  type: string;
  level: string;
  levelText: string;
  message: string;
  status: string;
  details: {
    location?: string;
    temperature?: string;
    threshold?: string;
    duration?: string;
    solution?: string;
    voltage?: string;
    frequency?: string;
    retries?: string;
    errorCode?: string;
    errorType?: string;
    cellNo?: string;
    [key: string]: string | undefined;
  };
}

// 过滤器
const faultTypeFilter = ref('all');
const faultLevelFilter = ref('all');
const timeRangeFilter = ref('week');

// 分页信息
const currentPage = ref(1);
const totalPages = ref(3);

// 模态框状态
const showDetailModal = ref(false);
const currentFaultDetail = ref<FaultDetail | null>(null);

// 故障数据
const faults = ref([
  {
    time: '2023-11-15 08:23:45',
    code: 'E001',
    type: '硬件故障',
    level: 'critical',
    levelText: '严重',
    message: 'IGBT过温保护',
    status: '已清除',
    details: {
      location: '功率模块1',
      temperature: '87℃',
      threshold: '75℃',
      duration: '3分钟',
      solution: '检查散热系统，确认风扇工作正常。如持续报错，可能需要更换IGBT模块。'
    }
  },
  {
    time: '2023-11-14 15:36:12',
    code: 'E015',
    type: '电网故障',
    level: 'major',
    levelText: '重要',
    message: '电网过压保护',
    status: '已清除',
    details: {
      location: '电网接入点',
      voltage: '264V',
      threshold: '255V',
      duration: '5分钟',
      solution: '检查电网电压稳定性，确认电网电压在正常范围内。如持续发生，可能需要调整过压保护阈值或安装稳压装置。'
    }
  },
  {
    time: '2023-11-14 10:18:33',
    code: 'E022',
    type: '通信故障',
    level: 'minor',
    levelText: '次要',
    message: 'CAN通信超时',
    status: '未清除',
    details: {
      location: 'CAN总线',
      duration: '30秒',
      retries: '3次',
      solution: '检查CAN通信线路连接是否稳固，排除干扰源。尝试重置通信模块，如问题持续存在，可能需要更换通信板。'
    }
  },
  {
    time: '2023-11-13 23:54:21',
    code: 'E007',
    type: '软件故障',
    level: 'minor',
    levelText: '次要',
    message: '控制软件异常',
    status: '已清除',
    details: {
      location: '控制系统',
      errorCode: 'SW-2056',
      solution: '记录错误代码，重启PCS系统。如频繁出现此故障，请联系厂商升级控制软件。'
    }
  },
  {
    time: '2023-11-12 14:45:30',
    code: 'E038',
    type: '电池故障',
    level: 'major',
    levelText: '重要',
    message: '电池过温保护',
    status: '未清除',
    details: {
      location: '电池组',
      temperature: '52℃',
      threshold: '50℃',
      cellNo: '电池单元6、8',
      solution: '检查电池温度传感器，确认散热系统正常工作。降低充放电倍率，必要时停止使用并联系电池厂商。'
    }
  },
  {
    time: '2023-11-10 09:12:05',
    code: 'E002',
    type: '硬件故障',
    level: 'critical',
    levelText: '严重',
    message: 'DC/DC电路故障',
    status: '已清除',
    details: {
      location: 'DC/DC转换器',
      errorType: '电压不稳定',
      solution: '检查DC/DC电路连接和元器件状态。如问题持续存在，更换DC/DC模块。'
    }
  },
  {
    time: '2023-11-09 16:28:57',
    code: 'E042',
    type: '电网故障',
    level: 'major',
    levelText: '重要',
    message: '电网频率超限',
    status: '已清除',
    details: {
      location: '电网接入点',
      frequency: '51.8Hz',
      threshold: '51.5Hz',
      duration: '2分钟',
      solution: '监测电网频率变化，如频繁发生，可能需要调整频率保护参数或联系电网运营商。'
    }
  },
]);

// 过滤后的故障列表
const filteredFaults = computed(() => {
  return faults.value.filter(fault => {
    // 故障类型过滤
    if (faultTypeFilter.value !== 'all') {
      const typeMap: Record<string, string> = {
        'hardware': '硬件故障',
        'software': '软件故障',
        'communication': '通信故障',
        'grid': '电网故障',
        'battery': '电池故障'
      };
      if (fault.type !== typeMap[faultTypeFilter.value]) {
        return false;
      }
    }
    
    // 故障等级过滤
    if (faultLevelFilter.value !== 'all' && fault.level !== faultLevelFilter.value) {
      return false;
    }
    
    // 时间范围过滤 (简化实现，实际应检查日期)
    return true;
  });
});

// 故障统计
const criticalCount = computed(() => faults.value.filter(f => f.level === 'critical').length);
const majorCount = computed(() => faults.value.filter(f => f.level === 'major').length);
const minorCount = computed(() => faults.value.filter(f => f.level === 'minor').length);
const totalFaults = computed(() => faults.value.length);

// 查看故障详情
const viewFaultDetails = (fault: any) => {
  currentFaultDetail.value = fault;
  showDetailModal.value = true;
};

// 关闭详情模态框
const closeDetailModal = () => {
  showDetailModal.value = false;
};

// 清除故障
const clearFault = (index: number) => {
  // 在实际应用中，这里应该有API调用来清除故障
  const fault = filteredFaults.value[index];
  
  // 在原始数据中查找并更新状态
  const originalIndex = faults.value.findIndex(f => 
    f.code === fault.code && f.time === fault.time);
  
  if (originalIndex !== -1) {
    faults.value[originalIndex].status = '已清除';
  }
};

// 刷新数据
const refreshData = () => {
  // 模拟刷新操作，实际应调用API
  const refreshIndicator = ref(true);
  setTimeout(() => {
    refreshIndicator.value = false;
  }, 1000);
  return refreshIndicator;
};

// 模拟刷新指示器
const isRefreshing = ref(false);

// 刷新故障数据
const refresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    isRefreshing.value = false;
  }, 1000);
};

// 导出故障数据
const exportData = () => {
  alert('故障数据已导出');
};

// 翻页
const goToPrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};
</script>

<script lang="ts">
// 格式化标签名称
function formatLabel(key: string | number): string {
  const labelMap: Record<string, string> = {
    location: '位置',
    temperature: '温度',
    threshold: '阈值',
    duration: '持续时间',
    solution: '解决方案',
    voltage: '电压',
    frequency: '频率',
    retries: '重试次数',
    errorCode: '错误代码',
    errorType: '错误类型',
    cellNo: '电池单元'
  };
  
  return labelMap[key.toString()] || key.toString();
}
</script>

<style scoped lang="scss">
.pcs-fault {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  color: #e0e0e0;
  
  .section-header {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #00a8ff;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  }
  
  .fault-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 168, 255, 0.2);
    
    .filter-group {
      display: flex;
      align-items: center;
      
      .filter-label {
        margin-right: 10px;
        white-space: nowrap;
      }
      
      .filter-select {
        padding: 6px 10px;
        background-color: rgba(0, 30, 60, 0.6);
        border: 1px solid rgba(0, 168, 255, 0.3);
        border-radius: 4px;
        color: white;
        min-width: 120px;
        
        &:focus {
          outline: none;
          border-color: #00a8ff;
          box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
        }
      }
    }
    
    .action-group {
      margin-left: auto;
      display: flex;
      gap: 10px;
      
      button {
        padding: 6px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        
        &.refresh-btn {
          background-color: #0066aa;
          color: white;
          
          &:hover {
            background-color: #0077cc;
            box-shadow: 0 0 5px rgba(0, 168, 255, 0.5);
          }
        }
        
        &.export-btn {
          background-color: #006633;
          color: white;
          
          &:hover {
            background-color: #007744;
            box-shadow: 0 0 5px rgba(0, 255, 168, 0.5);
          }
        }
      }
    }
  }
  
  .fault-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    
    .summary-card {
      flex: 1;
      background-color: rgba(0, 40, 80, 0.3);
      border: 1px solid rgba(0, 168, 255, 0.3);
      border-radius: 4px;
      padding: 15px;
      text-align: center;
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-3px);
      }
      
      .summary-value {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .summary-label {
        font-size: 14px;
        opacity: 0.8;
      }
      
      &.critical {
        border-color: rgba(255, 80, 80, 0.5);
        .summary-value {
          color: #ff5050;
        }
      }
      
      &.major {
        border-color: rgba(255, 180, 50, 0.5);
        .summary-value {
          color: #ffb432;
        }
      }
      
      &.minor {
        border-color: rgba(255, 255, 80, 0.5);
        .summary-value {
          color: #ffff50;
        }
      }
      
      &.total {
        border-color: rgba(0, 168, 255, 0.5);
        .summary-value {
          color: #00a8ff;
        }
      }
    }
  }
  
  .fault-table {
    flex: 1;
    overflow: auto;
    background-color: rgba(0, 40, 80, 0.3);
    border: 1px solid rgba(0, 168, 255, 0.3);
    border-radius: 4px;
    margin-bottom: 15px;
    
    .table-header {
      display: flex;
      background-color: rgba(0, 60, 120, 0.5);
      padding: 12px 15px;
      font-weight: bold;
      position: sticky;
      top: 0;
      z-index: 1;
      
      .header-cell {
        padding: 0 10px;
        
        &.time {
          width: 180px;
        }
        &.code {
          width: 100px;
        }
        &.type {
          width: 120px;
        }
        &.level {
          width: 100px;
        }
        &.message {
          flex: 1;
        }
        &.status {
          width: 80px;
        }
        &.action {
          width: 140px;
          text-align: center;
        }
      }
    }
    
    .table-body {
      .table-row {
        display: flex;
        padding: 12px 15px;
        border-bottom: 1px solid rgba(0, 100, 200, 0.1);
        transition: background-color 0.3s;
        
        &:hover {
          background-color: rgba(0, 60, 120, 0.2);
        }
        
        &.critical {
          background-color: rgba(255, 0, 0, 0.05);
          
          &:hover {
            background-color: rgba(255, 0, 0, 0.1);
          }
        }
        
        &.major {
          background-color: rgba(255, 180, 0, 0.05);
          
          &:hover {
            background-color: rgba(255, 180, 0, 0.1);
          }
        }
        
        &.minor {
          background-color: rgba(255, 255, 0, 0.03);
          
          &:hover {
            background-color: rgba(255, 255, 0, 0.08);
          }
        }
        
        .table-cell {
          padding: 0 10px;
          display: flex;
          align-items: center;
          
          &.time {
            width: 180px;
          }
          &.code {
            width: 100px;
            font-family: monospace;
          }
          &.type {
            width: 120px;
          }
          &.level {
            width: 100px;
            
            .level-tag {
              padding: 2px 8px;
              border-radius: 10px;
              font-size: 12px;
              
              &.critical {
                background-color: rgba(255, 0, 0, 0.2);
                color: #ff5050;
              }
              
              &.major {
                background-color: rgba(255, 180, 0, 0.2);
                color: #ffb432;
              }
              
              &.minor {
                background-color: rgba(255, 255, 0, 0.2);
                color: #ffff50;
              }
            }
          }
          &.message {
            flex: 1;
          }
          &.status {
            width: 80px;
          }
          &.action {
            width: 140px;
            display: flex;
            gap: 5px;
            justify-content: center;
            
            .action-btn {
              padding: 3px 8px;
              border: none;
              border-radius: 3px;
              font-size: 12px;
              cursor: pointer;
              
              &.details-btn {
                background-color: #0066aa;
                color: white;
                
                &:hover {
                  background-color: #0077cc;
                }
              }
              
              &.clear-btn {
                background-color: #aa3300;
                color: white;
                
                &:hover {
                  background-color: #cc4400;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    
    .page-btn {
      padding: 5px 15px;
      background-color: #004477;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background-color: #005599;
      }
      
      &.prev {
        &::before {
          content: "《 ";
        }
      }
      
      &.next {
        &::after {
          content: " 》";
        }
      }
    }
    
    .page-info {
      font-size: 14px;
    }
  }
}

.refresh-btn {
  .refresh-icon {
    display: inline-block;
    margin-right: 5px;
    transition: transform 0.5s ease;
  }
  
  &.refreshing .refresh-icon {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 添加模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 700px;
  max-width: 90%;
  background-color: #00122e;
  border-radius: 6px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 168, 255, 0.3);
  
  &.critical {
    background-color: rgba(255, 50, 50, 0.2);
  }
  
  &.major {
    background-color: rgba(255, 160, 0, 0.2);
  }
  
  &.minor {
    background-color: rgba(255, 255, 50, 0.1);
  }
  
  .modal-title {
    font-size: 18px;
    font-weight: bold;
    color: #00a8ff;
  }
  
  .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #aaa;
    cursor: pointer;
    
    &:hover {
      color: white;
    }
  }
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: 70vh;
  
  .detail-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 168, 255, 0.2);
    
    &:last-child {
      border-bottom: none;
    }
    
    .section-title {
      font-weight: bold;
      font-size: 16px;
      color: #00a8ff;
      margin-bottom: 10px;
    }
    
    .detail-item {
      display: flex;
      margin-bottom: 8px;
      
      .detail-label {
        width: 120px;
        color: #aaa;
      }
      
      .detail-value {
        flex: 1;
      }
    }
    
    &.solution {
      background-color: rgba(0, 60, 120, 0.2);
      padding: 15px;
      border-radius: 4px;
      
      .solution-text {
        line-height: 1.6;
      }
    }
  }
}

.modal-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  border-top: 1px solid rgba(0, 168, 255, 0.3);
  
  .modal-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: #333;
    color: white;
    cursor: pointer;
    
    &:hover {
      background-color: #444;
    }
    
    &.primary {
      background-color: #0066aa;
      
      &:hover {
        background-color: #0088cc;
      }
    }
  }
}

// 页面按钮禁用状态
.page-btn {
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: #004477;
    }
  }
}
</style> 