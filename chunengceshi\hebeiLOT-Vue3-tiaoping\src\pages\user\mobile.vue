<template>
	<div class="container">
		<div class="title">{{ tableTitle }}</div>
		<div class="back" @click="backMenu()">返回主页</div>
		<div class="edit">
			<div class="edit-search">
				<van-search v-model="searchValue" shape="round" placeholder="请输入搜索关键词" />
			</div>
			<div class="edit-btn">
				<van-button type="primary" size="small" @click="openAddEvent">新增</van-button>
				<DownloadOutlined style="font-size: 12px; color: #fff" />
			</div>
		</div>
		<div class="table">
			<van-cell-group inset>
				<van-cell v-for="item in dataSource" :key="item.user_id" :title="item.username" :label="item.full_name" is-link>
					<template #default>
						<div @click="editRecord(item)">{{ item.username }} ({{ item.full_name }})</div>
					</template>
				</van-cell>
			</van-cell-group>
		</div>
		<van-popup v-model:show="openAdd" :style="{ padding: '20px' }" position="bottom">
			<van-form ref="formRef" :model="form" :rules="rules" layout="vertical">
				<van-field v-model="form.username" name="username" label="用户名" placeholder="请输入用户名" />
				<van-field v-model="form.full_name" name="full_name" label="全名" placeholder="请输入全名" />
				<van-field
					v-if="!form.user_id"
					v-model="form.password"
					name="password"
					label="密码"
					type="password"
					placeholder="请输入密码"
				/>
				<van-field v-if="!form.user_id" label="角色">
					<template #input>
						<van-radio-group v-model="form.role" direction="horizontal">
							<van-radio name="0">普通用户</van-radio>
							<van-radio name="1">管理员</van-radio>
						</van-radio-group>
					</template>
				</van-field>
				<div style="margin-top: 16px">
					<van-button type="primary" block @click="submitForm">提交</van-button>
					<van-button v-if="form.user_id && form.role !== 1" type="danger" block @click="confirmDelete(form.user_id)">
						删除
					</van-button>
					<van-button block @click="onClose">取消</van-button>
				</div>
			</van-form>
		</van-popup>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { getTableName, getTableData, addRecord, updateRecord, deleteRecord as deleteRecordAPI } from './service';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { Toast, showConfirmDialog } from 'vant';
import { useRouter } from 'vue-router';

const router = useRouter();

const loadShow = ref(false);
const leftList = ref([]);
const selectLeftId = ref<string | number>('');
const dataSource = ref([]);
const openAdd = ref(false);
const tableTitle = ref('权限控制');
const searchValue = ref('');
const formRef = ref(null);

const route = useRoute();
const id = route.query.id;

const fetchTableName = async () => {
	const res = await getTableName(id as string);
	leftList.value = res.data.data;
	if (leftList.value.length > 0) {
		selectLeftEvent(leftList.value[0].id);
		switchTable(leftList.value[0].api_url);
	}
};

const backMenu = () => {
	router.push('/menu');
};

const selectLeftEvent = (id: string | number) => {
	selectLeftId.value = id;
	tableTitle.value = leftList.value.find((item: any) => item.id === id)?.table_name;
	switchTable(leftList.value.find((item: any) => item.id === id)?.api_url);
};

const switchTable = async (url: string, search?: string) => {
	const res = await getTableData(url, search);
	dataSource.value = res.data.data;
};

const form = reactive({
	user_id: null as number | null,
	username: '',
	full_name: '',
	password: '',
	role: null as number | null
});

const rules = ref({
	username: [{ required: true, message: '请输入用户名' }],
	full_name: [{ required: true, message: '请输入全名' }],
	role: [{ required: true, message: '请选择角色' }]
});

const openAddEvent = () => {
	form.user_id = null;
	form.username = '';
	form.full_name = '';
	form.password = '';
	form.role = null;
	openAdd.value = true;
};

const editRecord = (record: any) => {
	form.user_id = record.user_id;
	form.username = record.username;
	form.full_name = record.full_name;
	form.password = ''; // 编辑时不显示密码
	form.role = record.role;
	openAdd.value = true;
};

const onClose = () => {
	openAdd.value = false;
};

const submitForm = async () => {
	try {
		await formRef.value.validate();
		// 确保 role 字段是整数
		if (form.role !== null) {
			form.role = parseInt(form.role);
		}
		if (form.user_id) {
			await updateRecord(form);
		} else {
			const { user_id, ...addForm } = form; // 删除 user_id 字段
			await addRecord(addForm);
		}
		openAdd.value = false;
		switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value).api_url);
	} catch (error) {
		console.error('表单校验失败:', error);
	}
};

const deleteRecord = async (user_id: number) => {
	try {
		await deleteRecordAPI(user_id);
		switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value).api_url);
	} catch (error) {
		console.error('删除记录失败:', error);
	}
};

const confirmDelete = (user_id: number) => {
	showConfirmDialog({
		title: '确认删除',
		message: '确定要删除该用户吗？'
	})
		.then(() => {
			deleteRecord(user_id);
			openAdd.value = false;
		})
		.catch(() => {
			// 取消操作
		});
};

watch(searchValue, newVal => {
	switchTable(leftList.value.find((item: any) => item.id === selectLeftId.value)?.api_url, newVal);
});

onMounted(() => {
	fetchTableName();
});

const roleText = computed(() => {
	if (form.role === 1) {
		return '管理员';
	} else if (form.role === 0) {
		return '普通用户';
	} else {
		return '请选择角色';
	}
});
</script>

<style lang="scss" scoped>
.container {
	width: 100vw;
	height: 100vh;
	background-color: #0d1430;
	position: absolute;
	overflow: auto;
	padding: 20px;

	.title {
		color: #fff;
		border-left: #2ca6ff 10px solid;
		padding-left: 10px;
		margin-bottom: 20px;
		font-size: 24px;
	}
	.back {
		position: absolute;
		right: 0%;
		top: 25px;
		color: #ffffff;
		// background-color: rgba($color: #2384dd, $alpha: .6);
		background-image: url('/src/assets/images/bg1.png');
		background-repeat: no-repeat;
		width: 150px;
		height: 40px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		font-size: 18px;
	}
	.edit {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: auto;
		margin-top: 30px;
		margin-bottom: 30px;
		width: 95%;

		.edit-search {
			width: 50%;
			margin-right: 10px;
		}

		.edit-btn {
			display: flex;
			align-items: center;
			gap: 10px;
		}
	}

	.table {
		width: 100%;
	}
}
</style>
