<template>
	<ConfigProvider :locale="zhCN">
		<router-view />
	</ConfigProvider>
</template>
<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { notification } from 'ant-design-vue';
dayjs.locale('zh-cn');
const openNotification = () => {
	notification.error({
		message: '设备异常',
		description: '连接设备失败，请检查设备是否正常工作。',
		duration: 0,
		onClose: () => {}
	});
};
// setTimeout(()=>{openNotification()},1000)
</script>

<style>
@font-face {
	font-family: electronicFont;
	src: url('@/assets/font/DS-DIGIT.TTF');
}
</style>
