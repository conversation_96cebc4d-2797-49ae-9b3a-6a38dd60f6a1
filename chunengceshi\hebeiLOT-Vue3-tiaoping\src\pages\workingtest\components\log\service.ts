/*
 * @Author: wwoop <EMAIL>
 * @Date: 2024-08-06 10:39:14
 * @LastEditors: wwoop <EMAIL>
 * @LastEditTime: 2024-08-09 14:08:19
 * @FilePath: \DM_screen_web\src\pages\workingtest\components\log\service.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { GET, POST, PUT, DELETE, DOWNLOAD } from '@/service/api';

// 获取PCS日志数据，增加搜索参数和分页
export const getPCSLogs = (params: any) => {
	let url = '/task-schedules/pcs-logs';
	const queryParams = [];

	if (params.page) queryParams.push(`page=${params.page}`);
	if (params.pageSize) queryParams.push(`pageSize=${params.pageSize}`);
	if (params.startTime) queryParams.push(`startTime=${params.startTime}`);
	if (params.endTime) queryParams.push(`endTime=${params.endTime}`);
	if (params.sortOrder) queryParams.push(`sortOrder=${params.sortOrder}`);

	if (queryParams.length > 0) {
		url += `?${queryParams.join('&')}`;
	}

	return GET(url);
};

export const getNonRunningRecords = () => {
	let url = '/records/non-running';
	return GET(url);
};

// 获取BMS日志数据，增加搜索参数和分页
export const getBMSLogs = (params: any) => {
	let url = '/task-schedules/bms-logs';
	const queryParams = [];

	if (params.page) queryParams.push(`page=${params.page}`);
	if (params.pageSize) queryParams.push(`pageSize=${params.pageSize}`);
	if (params.startTime) queryParams.push(`startTime=${params.startTime}`);
	if (params.endTime) queryParams.push(`endTime=${params.endTime}`);
	if (params.sortOrder) queryParams.push(`sortOrder=${params.sortOrder}`);

	if (queryParams.length > 0) {
		url += `?${queryParams.join('&')}`;
	}

	return GET(url);
};

// 下载PCS日志
export const downloadPCSLogs = (params: any) => {
	let url = '/task-schedules/pcs-logs/download';
	const queryParams = [];

	if (params.startTime) queryParams.push(`startTime=${params.startTime}`);
	if (params.endTime) queryParams.push(`endTime=${params.endTime}`);
	if (params.sortOrder) queryParams.push(`sortOrder=${params.sortOrder}`);

	if (queryParams.length > 0) {
		url += `?${queryParams.join('&')}`;
	}

	return DOWNLOAD(url);
};

// 下载BMS日志
export const downloadBMSLogs = (params: any) => {
	let url = '/task-schedules/bms-logs/download';
	const queryParams = [];

	if (params.startTime) queryParams.push(`startTime=${params.startTime}`);
	if (params.endTime) queryParams.push(`endTime=${params.endTime}`);
	if (params.sortOrder) queryParams.push(`sortOrder=${params.sortOrder}`);

	if (queryParams.length > 0) {
		url += `?${queryParams.join('&')}`;
	}

	return DOWNLOAD(url);
};
