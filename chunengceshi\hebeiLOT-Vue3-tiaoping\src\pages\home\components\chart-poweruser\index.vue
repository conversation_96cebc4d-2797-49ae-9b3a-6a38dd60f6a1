<template>
	<div ref="chartContainer" style="width: 100%; height: 250px"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const chartContainer = ref(null);
let chart = null;

const data = [
	{ name: '负载用电量', value: 16.67, color: '#FF6B6B' },
	{ name: '光伏发电量', value: 0, color: '#4ECDC4' },
	{ name: '电网取电量', value: 13.08, color: '#45B7D1' },
	{ name: '电网馈电量', value: 1.38, color: '#FFA07A' },
	{ name: '储能充电量', value: 14.12, color: '#97C95C' },
	{ name: '储能放电量', value: 1.75, color: '#FFD700' }
];

const initChart = () => {
	if (chartContainer.value) {
		chart = echarts.init(chartContainer.value);
		const option = {
			grid: {
				left: '3%',
				right: '4%',
				bottom: '3%',
				containLabel: true
			},
			xAxis: {
				type: 'value',
				axisLine: { show: false },
				axisTick: { show: false },
				splitLine: { show: false }
			},
			yAxis: {
				type: 'category',
				data: data.map(item => item.name),
				axisLine: { show: false },
				axisTick: { show: false }
			},
			series: [
				{
					type: 'bar',
					data: data.map(item => ({
						value: item.value,
						itemStyle: { color: item.color }
					})),
					label: {
						show: true,
						position: 'right',
						formatter: '{c}'
					}
				}
			]
		};
		chart.setOption(option);
	}
};

onMounted(() => {
	initChart();
	window.addEventListener('resize', () => {
		chart && chart.resize();
	});
});

onUnmounted(() => {
	chart && chart.dispose();
});
</script>
