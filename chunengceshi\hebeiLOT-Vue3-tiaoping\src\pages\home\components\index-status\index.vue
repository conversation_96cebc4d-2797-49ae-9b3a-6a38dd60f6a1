<template>
	<div class="index-num">
		<a-row class="index-row-value">
			<a-col class="index-col">
				<img src="@/assets/images/facility.svg" style="width: 45px" class="icon" alt="" srcset="" />
				<span class="title">运行状态</span>
			</a-col>
			<a-divider type="vertical" class="index-divider" />
			<a-col class="index-col">
				<span class="status normal">正常</span>
			</a-col>
		</a-row>
		<a-row class="index-row-value">
			<a-col class="index-col">
				<img src="@/assets/images/online.svg" class="icon" alt="" srcset="" />
				<span class="title">联机状态</span>
			</a-col>
			<a-divider type="vertical" class="index-divider" />
			<a-col class="index-col">
				<span class="status normal">正常</span>
			</a-col>
		</a-row>
		<a-row class="index-row-value">
			<a-col class="index-col">
				<img src="@/assets/images/status.svg" class="icon" alt="" srcset="" />
				<span class="title">报警状态</span>
			</a-col>
			<a-divider type="vertical" class="index-divider" />
			<a-col class="index-col">
				<span class="status normal">正常</span>
			</a-col>
		</a-row>
	</div>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
.title {
	color: #02a6b5;
	font-size: 22px;
}
.status {
	font-size: 22px;
	&.error {
		color: #db5f5f;
	}
	&.warning {
		color: #dbb55f;
	}
	&.normal {
		color: #07db5f;
	}
}
@media (max-width: 480px) {
	.index-num {
		margin-top: -30px;
	}

	.unit {
		font-size: 12px;
	}

	.icon {
		width: 40px;
	}
}

@font-face {
	font-family: electronicFont;
	src: url('@/assets/font/DS-DIGIT.TTF');
}

.icon {
	width: 30px;
	height: 50px;
}

.unit {
	font-size: 1rem;
}

.index-num {
	background-color: #0f2159;
	border-radius: 4px;

	.index-row-value {
		position: relative;
		padding: 8px 0;
		border: 1px solid rgb(255 255 255 / 20%);

		.index-col {
			flex: 1;
			font-family: electronicFont;
			font-size: 1.8rem;
			color: #ffeb7b;
			text-align: center;
		}

		.index-divider {
			height: unset;
			margin-block: 20px;
			background: rgb(255 255 255 / 20%);
		}

		@mixin border-conrner {
			position: absolute;
			width: 30px;
			height: 15px;
			content: '';
		}

		&::before {
			@include border-conrner;

			top: 0;
			left: 0;
			border-top: 2px solid #02a6b5;
			border-left: 2px solid #02a6b5;
		}

		&::after {
			@include border-conrner;

			right: 0;
			bottom: 0;
			border-right: 2px solid #02a6b5;
			border-bottom: 2px solid #02a6b5;
		}
	}

	.index-row-label {
		color: rgb(255 255 255 / 70%);

		.index-col {
			flex: 1;
			margin: 8px 0;
			font-size: 16px;
			text-align: center;
		}
	}
}
</style>
